package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/user"

	"github.com/go-openapi/runtime/middleware"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getUserRolesImpl struct {
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetUserRolesHandler(
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) user.GetUserRolesHandler {
	return &getUserRolesImpl{
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *getUserRolesImpl) Handle(params user.GetUserRolesParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetUserRolesHandler")
	defer span.End()

	email := principal.(string)
	userRoles, err := impl.userRolesProvider.GetByEmail(ctx, email)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return user.NewGetUserRolesOK().WithPayload(&models.UserRoles{Roles: make([]*models.Role, 0)})
		}
		return user.NewGetUserRolesInternalServerError().WithPayload("Unable to fetch user roles")
	}
	response := &models.UserRoles{
		Roles: make([]*models.Role, 0, len(userRoles.Roles)),
	}
	for _, role := range userRoles.Roles {
		response.Roles = append(response.Roles, &models.Role{
			Role:        int32(role.Role),
			InstituteID: role.InstituteID,
		})
	}

	return user.NewGetUserRolesOK().WithPayload(response)
}
