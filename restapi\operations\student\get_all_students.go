// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllStudentsHandlerFunc turns a function with the right signature into a get all students handler
type GetAllStudentsHandlerFunc func(GetAllStudentsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetAllStudentsHandlerFunc) Handle(params GetAllStudentsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllStudentsHandler interface for that can handle valid get all students params
type GetAllStudentsHandler interface {
	Handle(GetAllStudentsParams, interface{}) middleware.Responder
}

// NewGetAllStudents creates a new http.Handler for the get all students operation
func NewGetAllStudents(ctx *middleware.Context, handler GetAllStudentsHandler) *GetAllStudents {
	return &GetAllStudents{Context: ctx, Handler: handler}
}

/*
	GetAllStudents swagger:route GET /institute/{instituteId}/student student getAllStudents

# Get all students

Get all students by Institute Id
*/
type GetAllStudents struct {
	Context *middleware.Context
	Handler GetAllStudentsHandler
}

func (o *GetAllStudents) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllStudentsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
