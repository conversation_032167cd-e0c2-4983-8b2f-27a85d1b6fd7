// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// StudentOverallPerformance student overall performance
//
// swagger:model StudentOverallPerformance
type StudentOverallPerformance struct {

	// total achieved score
	TotalAchievedScore float32 `json:"totalAchievedScore,omitempty"`

	// total assignments solved
	TotalAssignmentsSolved int32 `json:"totalAssignmentsSolved,omitempty"`

	// total attempted score
	TotalAttemptedScore int32 `json:"totalAttemptedScore,omitempty"`
}

// Validate validates this student overall performance
func (m *StudentOverallPerformance) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this student overall performance based on context it is used
func (m *StudentOverallPerformance) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *StudentOverallPerformance) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentOverallPerformance) UnmarshalBinary(b []byte) error {
	var res StudentOverallPerformance
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
