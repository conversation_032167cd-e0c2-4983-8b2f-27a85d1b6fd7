// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetClassAssessmentsBySectionsParams creates a new GetClassAssessmentsBySectionsParams object
//
// There are no default values defined in the spec.
func NewGetClassAssessmentsBySectionsParams() GetClassAssessmentsBySectionsParams {

	return GetClassAssessmentsBySectionsParams{}
}

// GetClassAssessmentsBySectionsParams contains all the bound params for the get class assessments by sections operation
// typically these are obtained from a http.Request
//
// swagger:parameters GetClassAssessmentsBySections
type GetClassAssessmentsBySectionsParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	Class int32
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewGetClassAssessmentsBySectionsParams() beforehand.
func (o *GetClassAssessmentsBySectionsParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rClass, rhkClass, _ := route.Params.GetOK("class")
	if err := o.bindClass(rClass, rhkClass, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindClass binds and validates parameter Class from path.
func (o *GetClassAssessmentsBySectionsParams) bindClass(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route

	value, err := swag.ConvertInt32(raw)
	if err != nil {
		return errors.InvalidType("class", "path", "int32", raw)
	}
	o.Class = value

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *GetClassAssessmentsBySectionsParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}
