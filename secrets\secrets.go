package secrets

import (
	"encoding/json"
	"os"

	"github.com/rs/zerolog/log"
)

type Secrets struct {
	DBUsername *string `json:"dbUsername"`
	DBPassword *string `json:"dbPassword"`
}

func LoadSecrets() *Secrets {
	file, err := os.Open("../../secrets/secrets.override.json")
	if err != nil {
		file, err = os.Open("secrets/secrets.json")
		if err != nil {
			log.Err(err)
			return nil
		}
	}

	defer file.Close()
	decoder := json.NewDecoder(file)
	secrets := Secrets{}
	err = decoder.Decode(&secrets)
	if err != nil {
		log.Err(err)
	}
	return &secrets
}
