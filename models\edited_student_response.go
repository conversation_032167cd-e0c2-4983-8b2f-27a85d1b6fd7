// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// EditedStudentResponse edited student response
//
// swagger:model EditedStudentResponse
type EditedStudentResponse struct {

	// feedback
	// Example: Improvement needed in so and so area.
	Feedback string `json:"feedback,omitempty"`

	// question number
	QuestionNumber int32 `json:"questionNumber,omitempty"`

	// score
	// Example: 5
	Score float32 `json:"score,omitempty"`

	// student response
	// Example: Mitochondria is the powerhouse of the cell.
	StudentResponse string `json:"studentResponse,omitempty"`
}

// Validate validates this edited student response
func (m *EditedStudentResponse) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this edited student response based on context it is used
func (m *EditedStudentResponse) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *EditedStudentResponse) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *EditedStudentResponse) UnmarshalBinary(b []byte) error {
	var res EditedStudentResponse
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
