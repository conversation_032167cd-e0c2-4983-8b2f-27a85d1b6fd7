// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllSubmissionsHandlerFunc turns a function with the right signature into a get all submissions handler
type GetAllSubmissionsHandlerFunc func(GetAllSubmissionsParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetAllSubmissionsHandlerFunc) Handle(params GetAllSubmissionsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllSubmissionsHandler interface for that can handle valid get all submissions params
type GetAllSubmissionsHandler interface {
	Handle(GetAllSubmissionsParams, interface{}) middleware.Responder
}

// NewGetAllSubmissions creates a new http.Handler for the get all submissions operation
func NewGetAllSubmissions(ctx *middleware.Context, handler GetAllSubmissionsHandler) *GetAllSubmissions {
	return &GetAllSubmissions{Context: ctx, Handler: handler}
}

/*
	GetAllSubmissions swagger:route GET /institute/{instituteId}/submissions submission getAllSubmissions

# Get all submissions

Get submissions based on query
*/
type GetAllSubmissions struct {
	Context *middleware.Context
	Handler GetAllSubmissionsHandler
}

func (o *GetAllSubmissions) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllSubmissionsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
