package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type publishAllSubmissionsImpl struct {
	submissionProvider data_providers.SubmissionProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewPublishAllSubmissionsHandler(
	submissionProvider data_providers.SubmissionProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.PublishAllSubmissionsHandler {
	return &publishAllSubmissionsImpl{
		submissionProvider: submissionProvider,
		instituteProvider:  instituteProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *publishAllSubmissionsImpl) Handle(params submission.PublishAllSubmissionsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : PublishAllSubmissionsHandler")
	defer span.End()

	principalEmail := principal.(string)

	// Check if user has admin/instructor role
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principalEmail, params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewPublishAllSubmissionsForbidden().WithPayload("Unauthorized")
	}

	// Validate institute exists
	_, err = impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return submission.NewPublishAllSubmissionsBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Err(err).Msg("Failed to fetch institute")
		return submission.NewPublishAllSubmissionsInternalServerError().WithPayload("Unable to fetch institute")
	}

	// Publish all graded submissions
	err = impl.submissionProvider.Publish(ctx, params.InstituteID, params.AssignmentID, nil, principalEmail)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return submission.NewPublishAllSubmissionsNotFound().WithPayload("No submissions found")
		}
		log.Error().Err(err).Msg("Failed to publish submissions")
		return submission.NewPublishAllSubmissionsInternalServerError().WithPayload("Failed to publish submissions")
	}

	return submission.NewPublishAllSubmissionsOK().WithPayload(&models.SuccessResponse{
		Message: "Successfully published all graded submissions",
	})
}
