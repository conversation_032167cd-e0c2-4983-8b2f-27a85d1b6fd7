package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getAllStudentsImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	termProvider      data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetAllStudentsHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) student.GetAllStudentsHandler {
	return &getAllStudentsImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		termProvider:      termProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *getAllStudentsImpl) Handle(params student.GetAllStudentsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAllStudentsHandler")
	defer span.End()

	principal = principal.(string)

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewGetAllStudentsForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.getAllStudentsValidator(params)
	if !valid {
		return validateResp
	}
	termID := params.TermID
	if termID == nil {
		currentTerm, err := impl.termProvider.GetCurrent(ctx, params.InstituteID)
		if err != nil {
			log.Error().Msg(err.Error())
			if errors.Is(err, mongo.ErrNoDocuments) {
				return student.NewGetAllStudentsBadRequest().WithPayload("Current Tern Not Found")
			}
			return student.NewGetAllStudentsInternalServerError().WithPayload("Unable to fetch Term")
		}
		if currentTerm != nil {
			termID = currentTerm.ID
		}
	}

	var class *int
	if params.Class != nil {
		classNo := int(*params.Class)
		class = &classNo
	}

	studentList, err := impl.studentProvider.GetAll(ctx, &params.InstituteID, nil, class, &params.Sections, termID)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewGetAllStudentsInternalServerError().WithPayload("Unable to fetch Students")
	}
	studentResponseList := make([]*models.Student, 0)
	for _, studentEntity := range *studentList {
		studentResponse := &models.Student{
			StudentID:  *studentEntity.StudentID,
			Class:      int32(studentEntity.AcademicHistory[*termID].Grade),
			RollNumber: int32(studentEntity.AcademicHistory[*termID].RollNumber),
		}
		if studentEntity.Email != nil {
			studentResponse.Email = *studentEntity.Email
		}
		if studentEntity.FirstName != nil {
			studentResponse.FirstName = *studentEntity.FirstName
		}
		if studentEntity.LastName != nil {
			studentResponse.LastName = *studentEntity.LastName
		}
		if studentEntity.AcademicHistory[*termID].Section != nil {
			studentResponse.Section = *studentEntity.AcademicHistory[*termID].Section
		}
		studentResponseList = append(studentResponseList, studentResponse)
	}
	return student.NewGetAllStudentsOK().WithPayload(studentResponseList)
}

func (impl *getAllStudentsImpl) getAllStudentsValidator(params student.GetAllStudentsParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "getAllStudentsValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, student.NewGetAllStudentsBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewGetAllStudentsBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, student.NewGetAllStudentsInternalServerError().WithPayload("Unable to fetch Institute")
	}
	return true, nil
}
