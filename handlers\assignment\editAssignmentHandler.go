package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type editAssignmentImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewEditAssignmentHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.EditAssignmentHandler {
	return &editAssignmentImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *editAssignmentImpl) Handle(params assignment.EditAssignmentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : EditAssignmentHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewEditAssignmentForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.editAssignmentValidator(ctx, params)
	if !valid {
		return validateResp
	}

	// Get the existing assignment
	existingAssignment, err := impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get existing assignment")
		if errors.Is(err, mongo.ErrNoDocuments) {
			return assignment.NewEditAssignmentNotFound().WithPayload("Assignment not found")
		}
		return assignment.NewEditAssignmentInternalServerError().WithPayload("Failed to get existing assignment")
	}

	if params.Assignment == nil {
		return assignment.NewEditAssignmentBadRequest().WithPayload("Invalid assignment")
	}

	// Update only the changed fields from the model
	if params.Assignment.Name != "" {
		existingAssignment.Name = &params.Assignment.Name
	}
	if params.Assignment.Class != 0 {
		existingAssignment.Grade = int(params.Assignment.Class)
	}
	if params.Assignment.SubjectName != "" {
		existingAssignment.Subject = &params.Assignment.SubjectName
	}
	if params.Assignment.SectionList != nil {
		existingAssignment.Sections = &params.Assignment.SectionList
	}
	if params.Assignment.TotalScore != 0 {
		existingAssignment.TotalScore = params.Assignment.TotalScore
	}
	if params.Assignment.Questions != nil {
		newQuestions := mapModelQuestionsToEntities(params.Assignment.Questions)
		existingAssignment.Questions = &newQuestions
	}
	updatedBy := principal.(string)
	existingAssignment.UpdatedBy = &updatedBy

	err = impl.assignmentProvider.Edit(ctx, params.AssignmentID, params.InstituteID, existingAssignment)
	if err != nil {
		log.Error().Err(err).Msg("Failed to edit assignment")
		if errors.Is(err, mongo.ErrNoDocuments) {
			return assignment.NewEditAssignmentNotFound().WithPayload("Assignment not found")
		}
		return assignment.NewEditAssignmentInternalServerError().WithPayload("Failed to edit assignment")
	}

	return assignment.NewEditAssignmentOK().WithPayload(&models.SuccessResponse{
		ID:      params.AssignmentID,
		Message: "Assignment edited successfully",
	})
}

func (impl *editAssignmentImpl) editAssignmentValidator(ctx context.Context, params assignment.EditAssignmentParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, assignment.NewEditAssignmentBadRequest().WithPayload("Invalid Institute ID")
	}

	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewEditAssignmentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewEditAssignmentInternalServerError().WithPayload("Unable to fetch Institute")
	}

	if params.AssignmentID == constants.EmptyString {
		return false, assignment.NewEditAssignmentBadRequest().WithPayload("Invalid Assignment ID")
	}

	if params.Assignment == nil {
		return false, assignment.NewEditAssignmentBadRequest().WithPayload("Invalid Assignment")
	}

	return true, nil
}

func mapModelQuestionsToEntities(modelQuestions []*models.Question) []entities.Question {
	var entityQuestions []entities.Question
	for _, mq := range modelQuestions {
		entityQuestions = append(entityQuestions, *mapModelQuestionToEntity(mq))
	}
	return entityQuestions
}

func mapModelQuestionToEntity(mq *models.Question) *entities.Question {
	if mq == nil {
		return nil
	}

	eq := &entities.Question{
		QuestionNumber: int(mq.QuestionNumber),
		Question:       &mq.Question,
		Rubric:         &mq.QuestionRubric,
		Score:          mq.QuestionScore,
	}

	var tms []entities.Topic
	for _, t := range mq.Topics {
		tms = append(tms, *mapModelTopicToEntity(t))
	}
	eq.Topics = &tms

	return eq
}

func mapModelTopicToEntity(mt *models.ChapterTopics) *entities.Topic {
	if mt == nil {
		return nil
	}

	return &entities.Topic{
		Chapter: &mt.Chapter,
		Topics:  &mt.Topics,
	}
}
