package config

import (
	"encoding/json"
	"os"

	"github.com/rs/zerolog/log"
)

type Configuration struct {
	DBUrl             *string `json:"dbUrl"`
	DBName            *string `json:"dbName"`
	AuthIssuer        *string `json:"authIssuer"`
	OTLPEndpoint      *string `json:"otlpEndpoint`
	InferenceEndpoint *string `json:"inferenceEndpoint"`
	Framework         *string `json:"framework"`
	Model             *string `json:"model"`
	FrameworkLite     *string `json:"frameworkLite"`
	ModelLite         *string `json:"modelLite"`
}

func LoadConfig() *Configuration {
	file, err := os.Open("config/config.override.json")

	if err != nil {
		file, err = os.Open("config/config.json")
		if err != nil {
			log.Err(err)
			return nil
		}
	}

	defer file.Close()
	decoder := json.NewDecoder(file)
	configuration := Configuration{}
	err = decoder.Decode(&configuration)
	if err != nil {
		log.Err(err)
	}
	return &configuration
}
