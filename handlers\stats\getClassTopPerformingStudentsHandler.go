package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"sort"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassTopPerformingStudentsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassTopPerformingStudentsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassTopPerformingStudentsHandler {
	return &getClassTopPerformingStudentsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassTopPerformingStudentsImpl) Handle(params stats.GetClassTopPerformingStudentsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassTopPerformingStudentsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassTopPerformingStudentsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get all student performance data for the class
	result, err := impl.statsProvider.GetAllStudentOverallPerformance(ctx, params.InstituteID, termID, params.Class)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get student performance data")
		return stats.NewGetClassTopPerformingStudentsInternalServerError().WithPayload("Unable to get student performance data")
	}

	// Sort students by performance (descending order)
	students := *result
	sort.Slice(students, func(i, j int) bool {
		return students[i].AverageSubmissionPercentage > students[j].AverageSubmissionPercentage
	})

	// Prepare response
	response := &models.ClassTopPerformingStudents{
		Data: make([]*models.ClassTopPerformingStudent, 0),
	}
	for _, student := range students {
		response.Data = append(response.Data, &models.ClassTopPerformingStudent{
			Student:            student.ID.StudentID,
			OverallPerformance: float32(student.AverageSubmissionPercentage),
			HighestScore:       float32(student.HighestSubmissionPercentage),
			LowestScore:        float32(student.LowestSubmissionPercentage),
		})
	}

	return stats.NewGetClassTopPerformingStudentsOK().WithPayload(response)
}
