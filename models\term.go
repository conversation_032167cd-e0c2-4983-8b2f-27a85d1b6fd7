// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Term term
//
// swagger:model Term
type Term struct {

	// end date
	EndDate string `json:"endDate,omitempty"`

	// name
	Name string `json:"name,omitempty"`

	// start date
	StartDate string `json:"startDate,omitempty"`

	// term Id
	TermID string `json:"termId,omitempty"`
}

// Validate validates this term
func (m *Term) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this term based on context it is used
func (m *Term) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Term) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Term) UnmarshalBinary(b []byte) error {
	var res Term
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
