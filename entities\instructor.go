package entities

import (
	"eddyowl-backend/constants"
	"time"

	"github.com/google/uuid"
)

type Instructor struct {
	ID          *string    `json:"id" bson:"_id"`
	InstituteID *string    `json:"institute_id" bson:"institute_id"`
	Email       *string    `json:"email" bson:"email"`
	FirstName   *string    `json:"first_name,omitempty" bson:"first_name,omitempty"`
	LastName    *string    `json:"last_name,omitempty" bson:"last_name,omitempty"`
	Status      int        `json:"status" bson:"status"`
	Role        int32      `json:"role" bson:"role"`
	CreatedBy   *string    `json:"created_by" bson:"created_by"`
	CreatedAt   *time.Time `json:"created_at" bson:"created_at"`
	UpdatedBy   *string    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt   *time.Time `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy   *string    `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

func NewInstructor(instituteId string, email string, role int32, createdBy string) *Instructor {
	id := uuid.New().String()
	now := time.Now()
	return &Instructor{
		ID:          &id,
		InstituteID: &instituteId,
		Email:       &email,
		CreatedBy:   &createdBy,
		CreatedAt:   &now,
		Status:      constants.UserStatusInactive,
		Role:        role,
	}
}

func (i *Instructor) Validate() error {
	if i.ID == nil || *i.ID == "" {
		return ErrMissingID
	}
	if i.InstituteID == nil || *i.InstituteID == "" {
		return ErrMissingInstituteID
	}
	if i.CreatedBy == nil || *i.CreatedBy == "" {
		return ErrMissingCreatedBy
	}
	if i.CreatedAt == nil {
		return ErrMissingCreatedAt
	}
	return nil
}

var (
	ErrMissingID          = NewValidationError("missing id")
	ErrMissingInstituteID = NewValidationError("missing institute id")
	ErrMissingCreatedBy   = NewValidationError("missing created by")
	ErrMissingCreatedAt   = NewValidationError("missing created at")
)

type ValidationError struct {
	Message string
}

func NewValidationError(message string) error {
	return &ValidationError{Message: message}
}

func (e *ValidationError) Error() string {
	return e.Message
}
