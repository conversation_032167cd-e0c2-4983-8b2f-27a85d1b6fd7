// Code generated by go-swagger; DO NOT EDIT.

package subject

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetSubjectsParams creates a new GetSubjectsParams object
//
// There are no default values defined in the spec.
func NewGetSubjectsParams() GetSubjectsParams {

	return GetSubjectsParams{}
}

// GetSubjectsParams contains all the bound params for the get subjects operation
// typically these are obtained from a http.Request
//
// swagger:parameters GetSubjects
type GetSubjectsParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	Grade int32
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewGetSubjectsParams() beforehand.
func (o *GetSubjectsParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rGrade, rhkGrade, _ := route.Params.GetOK("grade")
	if err := o.bindGrade(rGrade, rhkGrade, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindGrade binds and validates parameter Grade from path.
func (o *GetSubjectsParams) bindGrade(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route

	value, err := swag.ConvertInt32(raw)
	if err != nil {
		return errors.InvalidType("grade", "path", "int32", raw)
	}
	o.Grade = value

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *GetSubjectsParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}
