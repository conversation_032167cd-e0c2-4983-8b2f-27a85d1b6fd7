package custom_middlewares

import "net/http"

type corsMiddlewareImpl struct {
	nextHandler http.Handler
	http.Handler
}

// NewCORSMiddleware
func NewCORSMiddleware(nextHandler http.Handler) http.Handler {
	return &corsMiddlewareImpl{
		nextHandler: nextHandler,
	}
}

func (impl *corsMiddlewareImpl) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With")

	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}
	impl.nextHandler.ServeHTTP(w, r)
}
