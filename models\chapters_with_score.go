// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ChaptersWithScore chapters with score
//
// swagger:model ChaptersWithScore
type ChaptersWithScore struct {

	// chapter
	Chapter string `json:"chapter,omitempty"`

	// score
	Score float32 `json:"score,omitempty"`

	// topics with score
	TopicsWithScore []*TopicsWithScore `json:"topicsWithScore"`
}

// Validate validates this chapters with score
func (m *ChaptersWithScore) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateTopicsWithScore(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChaptersWithScore) validateTopicsWithScore(formats strfmt.Registry) error {
	if swag.IsZero(m.TopicsWithScore) { // not required
		return nil
	}

	for i := 0; i < len(m.TopicsWithScore); i++ {
		if swag.IsZero(m.TopicsWithScore[i]) { // not required
			continue
		}

		if m.TopicsWithScore[i] != nil {
			if err := m.TopicsWithScore[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topicsWithScore" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topicsWithScore" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this chapters with score based on the context it is used
func (m *ChaptersWithScore) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateTopicsWithScore(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChaptersWithScore) contextValidateTopicsWithScore(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.TopicsWithScore); i++ {

		if m.TopicsWithScore[i] != nil {

			if swag.IsZero(m.TopicsWithScore[i]) { // not required
				return nil
			}

			if err := m.TopicsWithScore[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topicsWithScore" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topicsWithScore" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *ChaptersWithScore) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ChaptersWithScore) UnmarshalBinary(b []byte) error {
	var res ChaptersWithScore
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
