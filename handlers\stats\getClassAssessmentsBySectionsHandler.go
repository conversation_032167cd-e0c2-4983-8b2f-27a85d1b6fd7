package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassAssessmentsBySectionsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassAssessmentsBySectionsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassAssessmentsBySectionsHandler {
	return &getClassAssessmentsBySectionsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassAssessmentsBySectionsImpl) Handle(params stats.GetClassAssessmentsBySectionsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassAssessmentsBySectionsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassAssessmentsBySectionsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get all sections submission count
	result, err := impl.statsProvider.GetAllSectionData(ctx, params.InstituteID, termID, params.Class)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get sections submission count")
		return stats.NewGetClassAssessmentsBySectionsInternalServerError().WithPayload("Unable to get sections submission count")
	}

	// Filter results for the specified grade and organize data
	response := &models.ClassAssessmentsBySections{
		Sections:    make([]string, 0),
		Assessments: make([]int32, 0),
	}

	for _, section := range *result {
		if section.ID.Grade == params.Class {
			response.Sections = append(response.Sections, section.ID.Section)
			response.Assessments = append(response.Assessments, section.SubmissionCount)
		}
	}

	return stats.NewGetClassAssessmentsBySectionsOK().WithPayload(response)
}
