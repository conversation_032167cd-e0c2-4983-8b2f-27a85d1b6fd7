package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getMonthlyAssessmentsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetMonthlyAssessmentsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetMonthlyAssessmentsHandler {
	return &getMonthlyAssessmentsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getMonthlyAssessmentsImpl) Handle(params stats.GetMonthlyAssessmentsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetMonthlyAssessmentsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetMonthlyAssessmentsInternalServerError().WithPayload("Unable to resolve term")
	}

	monthRangeRequired := 12

	// Get current date
	now := time.Now().UTC()

	// Calculate the start date (6 months ago from the start of current month)
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).
		AddDate(0, 1-monthRangeRequired, 0)

	months := make([]string, monthRangeRequired)
	assessments := make([]int32, monthRangeRequired)

	// Iterate through monthRangeRequired months
	for i := 0; i < monthRangeRequired; i++ {
		// Calculate target month
		targetDate := startDate.AddDate(0, i, 0)
		monthStr := targetDate.Format("2006-01") // Format as YYYY-MM

		// Store the month name with year
		months[i] = targetDate.Format("Jan 2006") // Format as "MMM YYYY"

		// Get submissions count for the month
		result, err := impl.statsProvider.GetMonthlySubmissionsCount(ctx, params.InstituteID, termID, monthStr)
		if err != nil {
			assessments[i] = 0
			continue
		}

		if result != nil {
			assessments[i] = int32(result.SubmissionCount)
		} else {
			assessments[i] = 0
		}
	}

	response := &models.MonthlyAssessmentsGraded{
		Months:      months,
		Assessments: assessments,
	}

	return stats.NewGetMonthlyAssessmentsOK().WithPayload(response)
}
