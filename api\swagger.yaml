swagger: "2.0"
info:
  title: EddyOwl CORE
  description: |-
    [TO DO]
  termsOfService: http://swagger.io/terms/
  contact:
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  version: 1.0.11
host: localhost:8080
basePath: /v1/api
schemes:
  - http
  # - https
produces: [application/json]
consumes: [application/json]

tags:
  - name: institute
    description: Operations on Institute
  - name: instructor
    description: Operations on Instructor
  - name: term
    description: Operations on Academic Term
  - name: student
    description: Operations on Student
  - name: assignment
    description: Operations on Assignment
  - name: folder
    description: Operations on Assignment
  - name: auto
    description: Automated Operations
  - name: submission
    description: Operations on Submission
  - name: user
    description: Operations on user
  - name: stats
    description: Operations on statistics
    
securityDefinitions:
  eddyowl-okta:
    authorizationUrl: https://dev-z8m8ne04to0cvoad.us.auth0.com/authorize?audience=https%3A%2F%2Feddyowl.com%2Fv1%2Fapi
    tokenUrl: https://dev-z8m8ne04to0cvoad.us.auth0.com/oauth/token
    type: oauth2
    flow: accessCode
    scopes:
      openid profile email: openid profile email
paths:
  /userRoles:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - user
      summary: Get user roles
      description: Returns all roles for a user
      operationId: GetUserRoles
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/UserRoles"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/ErrorResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/auto/rubric:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - auto
      summary: Auto Add Rubric
      description: Auto Add Rubric
      operationId: AutoAddRubric
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: questions
          schema:
            $ref: "#/definitions/RubricInput"
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/QuestionList"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
  /institute/{instituteId}/upload/rubric:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - auto
      summary: Add rubric from a file.
      description: Add Rubric from a file
      operationId: AutoFileRubric
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: options
          schema:
            $ref: "#/definitions/RubricUpload"
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/QuestionList"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - institute
      summary: Create a new institute
      description: Insterts new institute
      operationId: CreateInstitute
      parameters:
        - in: body
          name: newInstitute
          schema:
            $ref: "#/definitions/NewInstitute"
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - institute
      summary: Get institute
      description: Get institute by id
      operationId: GetInstituteById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Institute"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - institute
      summary: Edit institute
      description: Edit institute information
      operationId: EditInstitute
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: institute
          schema:
            $ref: "#/definitions/EditInstitute"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - institute
      summary: Delete institute
      description: Delete institute by id
      operationId: DeleteInstituteById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/instructor:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - instructor
      summary: Get instructors
      description: Get instructors by institute id
      operationId: GetInstructorByInstituteId
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/InstructorList"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - instructor
      summary: Add instructor
      description: Add instructor by institute id
      operationId: AddInstructorByInstituteId
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: instructor
          schema:
            $ref: "#/definitions/Instructor"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - instructor
      summary: Edit instructor
      description: Edit instructor by institute id
      operationId: EditInstructorByInstituteId
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: instructor
          schema:
            $ref: "#/definitions/Instructor"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - instructor
      summary: Delete instructor
      description: Delete instructor by instructor email
      operationId: DeleteInstructorByEmail
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: email
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/terms:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - term
      summary: Create new term
      description: Create new term
      operationId: CreateNewTerm
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: term
          schema:
            $ref: "#/definitions/NewTerm"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - term
      summary: Edit term
      description: Edit term
      operationId: EditTerm
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: term
          schema:
            $ref: "#/definitions/Term"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - term
      summary: Get all terms
      description: Get all terms by Institute Id
      operationId: GetAllTerms
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/TermList"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
  /institute/{instituteId}/term/{termId}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - term
      summary: Get term
      description: Get term by School Term Id
      operationId: GetTermById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: termId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Term"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - term
      summary: Delete term
      description: Delete term by School Term Id
      operationId: DeleteTermById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: termId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/subject/grade/{grade}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - subject
      summary: Get subjects by grade
      description: Returns list of subjects by grade
      operationId: GetSubjects
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: grade
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SubjectList"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/ErrorResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/student:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Create new student
      description: Create new student
      operationId: CreateNewStudent
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
        - in: body
          name: student
          schema:
            $ref: "#/definitions/Student"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Edit student
      description: Edit student
      operationId: EditStudent
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
        - in: body
          name: student
          schema:
            $ref: "#/definitions/Student"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Get all students
      description: Get all students by Institute Id
      operationId: GetAllStudents
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
        - in: query
          name: class
          type: integer
          format: int32
          required: false
        - in: query
          name: sections
          type: array
          items:
            type: string
          required: false
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/StudentList"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/student/{studentId}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Get student
      description: Get student by School Student Id
      operationId: GetStudentById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Student"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Delete student
      description: Delete student by School Student Id
      operationId: DeleteStudentById
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/auto:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - auto
      summary: Create new assignment from upload
      description: Create new assignment from upload
      operationId: AutoCreateAssignment
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: files
          schema:
            $ref: "#/definitions/FileList"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/QuestionList"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Get all assignments
      description: Get all assignments by Institute Id
      operationId: GetAllAssignments
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
        - in: query
          name: grade
          type: integer
          format: int32
          required: false
        - in: query
          name: section
          type: array
          items:
            type: string
          required: false
        - in: query
          name: subject
          type: string
          required: false
        - in: query
          name: folderId
          type: string
          required: false
          description: Return only assignments in this folder
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/AssignmentList"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Create new assignment
      description: Create new assignment
      operationId: CreateAssignment
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: termId
          type: string
          required: false
        - in: body
          name: assignment
          schema:
            $ref: "#/definitions/NewAssignment"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/folder:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - folder
      summary: Create a new folder
      description: Create a folder under an institute
      operationId: CreateFolder
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: body
          name: folder
          schema:
            type: object
            properties:
              name:
                type: string
                example: Chapter 1 Assignments
      responses:
        "200":
          description: Folder created
          schema:
            $ref: "#/definitions/Folder"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"

    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - folder
      summary: Get all folders for an institute
      operationId: GetAllFolders
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: List of folders
          schema:
            type: array
            items:
              $ref: "#/definitions/Folder"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/folder/{folderId}:
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - folder
      summary: Delete a folder
      operationId: DeleteFolder
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: folderId
          type: string
          required: true
      responses:
        "200":
          description: Folder deleted
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"


  /institute/{instituteId}/assignment/{assignmentId}/topics:
    post:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Add topics to assignment
      description: Add topics by question number
      operationId: AddTopicsToAssignment
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true

      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"

        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
  /institute/{instituteId}/submissions:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Get all submissions
      description: Get submissions based on query
      operationId: GetAllSubmissions
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: query
          name: assignmentId
          type: string
          required: false
        - in: query
          name: grade
          type: integer
          format: int32
          required: false
        - in: query
          name: section
          type: array
          items:
            type: string
          required: false
        - in: query
          name: termId
          type: string
          required: false
        - in: query
          name: studentId
          type: string
          required: false
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SubmissionList"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Get assignment
      description: Get assignment by assignmentid for an institute
      operationId: GetAssignmentByID
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Assignment"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Delete assignment
      description: Delete assignment by assignmentid for an institute
      operationId: DeleteAssignmentByID
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - assignment
      summary: Edit assignment
      description: Edit assignment
      operationId: EditAssignment
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: body
          name: assignment
          schema:
            $ref: "#/definitions/Assignment"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/edit/submission:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: "Edit student gradedsubmission"
      description: "Edit scores and feedabck fo graded submission"
      operationId: EditSubmission
      parameters:
        - name: instituteId
          in: path
          required: true
          type: string
          description: "ID of the institute"
        - name: assignmentId
          in: path
          required: true
          type: string
          description: "ID of the assignment"
        - name: studentId
          in: path
          required: true
          type: string
          description: "ID of the student"
        - in: body
          name: submission
          schema:
            $ref: "#/definitions/EditedSubmission"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: "Bad Request"
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: "Forbidden"
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: "Not Found"
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: "Too Many Requests"
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: "Internal Server Error"
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: "Service Unavailable"
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Upload student solutions
      description: Upload solutions to S3
      operationId: CreateSubmission

      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
        - in: body
          name: files
          schema:
            $ref: "#/definitions/FileList"

      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    delete:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Delete student submission
      description: Delete submission
      operationId: DeleteSubmission

      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Get graded student submission
      description: Get graded submission
      operationId: GetSubmission

      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/StudentSubmission"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/publish:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Publish all submissions
      description: Publish all submissions
      operationId: PublishAllSubmissions
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/publish:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Publish student submission
      description: Publish submission
      operationId: PublishSubmission
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/submission/bulk:
    put:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - submission
      summary: Bulk upload student submissions
      description: Upload multiple student submissions for grading
      operationId: BulkCreateSubmission
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
          description: "ID of the institute"
        - in: path
          name: assignmentId
          type: string
          required: true
          description: "ID of the assignment"
        - in: body
          name: submissions
          required: true
          schema:
            $ref: "#/definitions/BulkCreateSubmissionBody"
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/SuccessResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unavailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/performance:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get graded student submission performance
      description: Get graded submission performance
      operationId: GetGradedSubmissionPerformance

      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: assignmentId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/GradedSubmissionPerformance"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/allstats:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get all stats
      description: Get all stats for students, instructors, and assessments
      operationId: GetAllStats
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/AllStats"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assessments/weekly:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get weekly assessments
      description: Get all the assessments graded last week for each day Mon to Fri
      operationId: GetWeeklyAssessments
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/WeeklyAssessmentsGraded"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/assessments/monthly:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get monthly assessments
      description: Get all the assessments graded last 6 months for each month
      operationId: GetMonthlyAssessments
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/MonthlyAssessmentsGraded"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/classdetails:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Details
      description: Get Class Details
      operationId: GetClassDetails
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassDetails"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/allstats:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Stats
      description: Get Class Stats
      operationId: GetClassStats
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/AllStats"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/assessmentsbysub:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Assessments By Subject
      description: Get Class Assessments By Subject
      operationId: GetClassAssessmentsBySubject
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassAssessmentsBySubject"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/assessmentsbysub/monthly:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Assessments By Subject Monthly
      description: Get Class Assessments By Subject Monthly for the last 12 months.
      operationId: GetClassAssessmentsBySubjectMonthly
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassAggMonthlyAssessmentsBySubject"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/assessmentsbysections:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Assessments By Sections
      description: Get Class Assessments By Sections.
      operationId: GetClassAssessmentsBySections
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassAssessmentsBySections"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/studentsbysections:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Students By Sections
      description: Get Class Students By Sections.
      operationId: GetClassStudentsBySections
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassStudentsBySections"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/class/{class}/topstudents:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Class Top Performing Students
      description: Get Class Top Performing Students
      operationId: GetClassTopPerformingStudents
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: class
          type: integer
          format: int32
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/ClassTopPerformingStudents"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/student/{studentId}/studentOverallStats:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - stats
      summary: Get Student Overall Stats
      description: Get overall stats for student
      operationId: GetStudentOverallStats
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: studentId
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/StudentOverallStats"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unvailable
          schema:
            $ref: "#/definitions/ErrorResponse"

  /institute/{instituteId}/student/email/{email}:
    get:
      security:
        - eddyowl-okta: [https://eddyowl.com/backend]
      tags:
        - student
      summary: Get student by email
      description: Get student by email address
      operationId: GetStudentByEmail
      parameters:
        - in: path
          name: instituteId
          type: string
          required: true
        - in: path
          name: email
          type: string
          required: true
      responses:
        "200":
          description: Successful operation
          schema:
            $ref: "#/definitions/Student"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/ErrorResponse"
        "403":
          description: Forbidden
          schema:
            $ref: "#/definitions/ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/ErrorResponse"
        "429":
          description: Too Many Requests
          schema:
            $ref: "#/definitions/ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/ErrorResponse"
        "503":
          description: Service Unavailable
          schema:
            $ref: "#/definitions/ErrorResponse"

definitions:
  ErrorResponse:
    type: string
    example: Unable
  SuccessResponse:
    type: object
    properties:
      id:
        type: string
      message:
        type: string
  Assignment:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
        example: Assignment-1
      totalScore:
        type: number
        format: float
      duration:
        type: integer
        format: int32
      subjectName:
        type: string
      class:
        type: integer
        format: int32
      sectionList:
        type: array
        items:
          type: string
      folderId:
        type: string
        description: Optional folder ID the assignment belongs to
      questions:
        type: array
        items:
          type: object
          $ref: "#/definitions/Question"
  NewAssignment:
    type: object
    properties:
      name:
        type: string
        example: Assignment-1
      totalScore:
        type: integer
        format: int32
      duration:
        type: integer
        format: int32
      subjectName:
        type: string
      class:
        type: integer
        format: int32
      sectionList:
        type: array
        items:
          type: string
      folderId:
        type: string
        description: Optional folder ID if assignment is inside a folder    
      questions:
        type: array
        items:
          type: object
          $ref: "#/definitions/Question"
  AssignmentList:
    type: array
    items:
      type: object
      $ref: "#/definitions/Assignment"

  Question:
    type: object
    properties:
      questionNumber:
        type: integer
        format: int32
      question:
        type: string
      questionScore:
        type: number
        format: float
      questionRubric:
        type: string
      topics:
        type: array
        items:
          type: object
          $ref: "#/definitions/ChapterTopics"

  Folder:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      createdAt:
        type: string
        format: date-time
      userEmail:
        type: string

  EditedStudentResponse:
    type: object
    properties:
      questionNumber:
        type: integer
        format: int32
      studentResponse:
        type: string
        example: Mitochondria is the powerhouse of the cell.
      feedback:
        type: string
        example: Improvement needed in so and so area.
      score:
        type: number
        format: float
        example: 5

  StudentResponse:
    type: object
    properties:
      questionNumber:
        type: integer
        format: int32
      studentResponse:
        type: string
        example: Mitochondria is the powerhouse of the cell.
      score:
        type: number
        format: float
        example: 5
      feedback:
        type: string
        example: Improvement needed in so and so area.
      question:
        type: string
      questionScore:
        type: integer
        format: int32
      questionRubric:
        type: string

  GradedSubmissionPerformance:
    type: object
    properties:
      stats:
        type: array
        items:
          type: object
          $ref: "#/definitions/QuestionStats"

  QuestionStats:
    type: object
    properties:
      questionNumber:
        type: integer
        format: int32
      stats:
        type: array
        items:
          type: object
          $ref: "#/definitions/ChapterStats"
  StudentSubmission:
    type: object
    properties:
      id:
        type: string
      instituteId:
        type: string
      assignmentId:
        type: string
      studentId:
        type: string
      history:
        type: array
        items:
          $ref: "#/definitions/StatusHistory"
      studentResponses:
        type: array
        items:
          type: object
          properties:
            questionNumber:
              type: integer
              format: int32
            response:
              type: string
            score:
              type: number
              format: float
            feedback:
              type: string
            question:
              type: string
            questionScore:
              type: integer
              format: int32
            rubric:
              type: string
            topics:
              type: array
              items:
                $ref: "#/definitions/ChapterTopics"
      missedQuestions:
        type: array
        items:
          type: integer
          format: int32
      imageIds:
        type: array
        items:
          type: string
      createdAt:
        type: string
        format: date-time
      createdBy:
        type: string
      updatedAt:
        type: string
        format: date-time
      updatedBy:
        type: string
      totalScore:
        type: number
        format: float
      # Student details
      studentGrade:
        type: integer
        format: int32
      studentSection:
        type: string
      studentRollNumber:
        type: integer
        format: int32
      studentFirstName:
        type: string
      studentLastName:
        type: string
      studentEmail:
        type: string
      # Assignment details
      assignmentName:
        type: string
      grade:
        type: integer
        format: int32
      subject:
        type: string
      termId:
        type: string
      assignmentScore:
        type: integer
        format: int32
      chapterPerformance:
        type: array
        items:
          $ref: "#/definitions/ChaptersWithScore"

  EditedSubmission:
    type: object
    properties:
      studentResponses:
        type: array
        items:
          type: object
          $ref: "#/definitions/EditedStudentResponse"

  AssignmentResult:
    type: object
    properties:
      subject:
        type: string
      imageIdList:
        type: array
        items:
          type: string
      acheivedScore:
        type: number
        format: float
        example: 75.5
      missedQuestionNumbers:
        type: array
        items:
          type: integer
          format: int32
      StudentResponseList:
        type: array
        items:
          type: object
          $ref: "#/definitions/StudentResponse"

  NewInstitute:
    type: object
    properties:
      name:
        type: string
        example: "Saraswati Vidya Mandir"
      program:
        type: string
      address:
        type: object
        $ref: "#/definitions/Address"
      terms:
        type: array
        items:
          $ref: "#/definitions/NewTerm"
  Institute:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
        example: "Saraswati Vidya Mandir"
      program:
        type: string
      address:
        type: object
        $ref: "#/definitions/Address"
      sectionList:
        type: object
        $ref: "#/definitions/SectionList"

  EditInstitute:
    type: object
    properties:
      name:
        type: string
      address:
        $ref: "#/definitions/Address"
      availableSections:
        type: array
        items:
          type: string
        example: ["A", "B", "C"]

  Address:
    type: object
    properties:
      addressOne:
        type: string
      addressTwo:
        type: string
      state:
        type: string
      pincode:
        type: string
      city:
        type: string

  TopicsByQuestion:
    type: object
    properties:
      questionNumber:
        type: integer
        format: int32
      topics:
        type: array
        items:
          type: object
          $ref: "#/definitions/ChapterTopics"

  ChapterTopics:
    type: object
    properties:
      chapter:
        type: string
      topics:
        type: array
        items:
          type: string

  ChaptersWithScore:
    type: object
    properties:
      chapter:
        type: string
      score:
        type: number
        format: float
      topicsWithScore:
        type: array
        items:
          type: object
          $ref: "#/definitions/TopicsWithScore"

  TopicsWithScore:
    type: object
    properties:
      topic:
        type: string
      topicScore:
        type: number
        format: float

  RubricInput:
    type: object
    properties:
      subject:
        type: string
      class:
        type: integer
        format: int32
      questions:
        type: object
        $ref: "#/definitions/QuestionList"
  RubricUpload:
    type: object
    properties:
      subject:
        type: string
      class:
        type: integer
        format: int32
      questions:
        type: object
        $ref: "#/definitions/QuestionList"
      filePaths:
        type: array
        items:
          type: string
  InstructorList:
    type: array
    items:
      $ref: "#/definitions/Instructor"
  Instructor:
    type: object
    properties:
      email:
        type: string
      firstName:
        type: string
      lastName:
        type: string
      role:
        type: integer
        format: int32
        enum:
          - 1
          - 2
        description: |
          0: AdminRole
          1: InstructorRole
        x-go-const-names:
          - AdminRole
          - InstructorRole
  Student:
    type: object
    properties:
      studentId:
        type: string
      rollNumber:
        type: integer
        format: int32
      firstName:
        type: string
      lastName:
        type: string
      email:
        type: string
      class:
        type: integer
        format: int32
        example: 10
      section:
        type: string
        example: "C"
  NewTerm:
    type: object
    properties:
      name:
        type: string
      startDate:
        type: string
        format: date-time
      endDate:
        type: string
        format: date-time
  Term:
    type: object
    properties:
      name:
        type: string
      termId:
        type: string
      startDate:
        type: string
      endDate:
        type: string

  TermList:
    type: array
    items:
      type: object
      $ref: "#/definitions/Term"

  SubjectAssessmentPerformance:
    type: object
    properties:
      subject:
        type: string
      totalAchievedScore:
        type: number
        format: float
      totalAttemptedScore:
        type: integer
        format: int32
      totalAssignmentsSolved:
        type: integer
        format: int32

  StudentOverallPerformance:
    type: object
    properties:
      totalAchievedScore:
        type: number
        format: float
      totalAttemptedScore:
        type: integer
        format: int32
      totalAssignmentsSolved:
        type: integer
        format: int32

  StudentPerformance:
    type: object
    properties:
      assessmentPerformance:
        type: array
        items:
          type: object
          $ref: "#/definitions/SubjectAssessmentPerformance"
      overallPerformance:
        type: object
        $ref: "#/definitions/StudentOverallPerformance"
      highestPerformance:
        type: number
        format: float
      lowestPerformance:
        type: number
        format: float

  # SubjectWisePerformance:
  #   type: object
  #   properties:
  #     subjectStats:
  #       type: array
  #       items:
  #         type: object
  #         $ref: "#/definitions/SubjectStats"

  SubjectList:
    type: array
    items:
      type: string

  SubjectStats:
    type: object
    properties:
      subject:
        type: string
      chapters:
        type: array
        items:
          type: object
          $ref: "#/definitions/ChapterStats"

  ChapterStats:
    type: object
    properties:
      chapter:
        type: string
      topics:
        type: array
        items:
          type: object
          $ref: "#/definitions/TopicStats"

  TopicStats:
    type: object
    properties:
      topic:
        type: string
      score:
        type: integer
        format: int32

  StudentList:
    type: array
    items:
      $ref: "#/definitions/Student"

  SectionList:
    type: array
    items:
      type: string

  Submission:
    type: object
    properties:
      studentName:
        type: string
      studentRollNumber:
        type: integer
        format: int32
      studentId:
        type: string
      studentScore:
        type: number
        format: float
      status:
        type: string
      class:
        type: integer
        format: int32
      section:
        type: string
      assignmentId:
        type: string
      assignmentName:
        type: string
      assignmentScore:
        type: integer
        format: int32
      subject:
        type: string

  UserRoles:
    type: object
    properties:
      roles:
        type: array
        items:
          $ref: "#/definitions/Role"
  Role:
    type: object
    properties:
      role:
        type: integer
        format: int32
        enum:
          - 1
          - 2
          - 3
        description: |
          0: AdminRole
          1: InstructorRole
          2: StudentRole
        x-go-const-names:
          - AdminRole
          - InstructorRole
          - StudentRole
      instituteId:
        type: string
  SubmissionList:
    type: array
    items:
      $ref: "#/definitions/Submission"

  FileList:
    type: object
    properties:
      files:
        type: array
        items:
          type: string

  AllStats:
    type: object
    properties:
      students:
        type: integer
        format: int32
      instructors:
        type: integer
        format: int32
      assessments:
        type: integer
        format: int32
      overallStudentPerformance:
        type: integer
        format: int32

  WeeklyAssessmentsGraded:
    type: object
    properties:
      weeklyData:
        type: array
        items:
          type: integer
          format: int32

  MonthlyAssessmentsGraded:
    type: object
    properties:
      months:
        type: array
        items:
          type: string
      assessments:
        type: array
        items:
          type: integer
          format: int32

  ClassDetails:
    type: object
    properties:
      classes:
        type: array
        items:
          type: string
      assessments:
        type: array
        items:
          type: integer
          format: int32
      students:
        type: array
        items:
          type: integer
          format: int32

  ClassAssessmentsBySubject:
    type: object
    properties:
      subjects:
        type: array
        items:
          type: string
      assessments:
        type: array
        items:
          type: integer
          format: int32

  ClassMonthlyAssessmentsBySubject:
    type: object
    properties:
      subjects:
        type: string
      assessments:
        type: array
        items:
          type: integer
          format: int32

  ClassAggMonthlyAssessmentsBySubject:
    type: object
    properties:
      months:
        type: array
        items:
          type: string
      data:
        type: array
        items:
          $ref: "#/definitions/ClassMonthlyAssessmentsBySubject"

  ClassAssessmentsBySections:
    type: object
    properties:
      sections:
        type: array
        items:
          type: string
      assessments:
        type: array
        items:
          type: integer
          format: int32

  ClassStudentsBySections:
    type: object
    properties:
      sections:
        type: array
        items:
          type: string
      students:
        type: array
        items:
          type: integer
          format: int32

  ClassTopPerformingStudent:
    type: object
    properties:
      student:
        type: string
      overallPerformance:
        type: number
        format: float
      highestScore:
        type: number
        format: float
      lowestScore:
        type: number
        format: float

  ClassTopPerformingStudents:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: "#/definitions/ClassTopPerformingStudent"

  QuestionList:
    type: object
    properties:
      questionList:
        type: array
        items:
          $ref: "#/definitions/Question"

  StudentOverallStats:
    type: object
    properties:
      totalAssignmentsSolved:
        type: integer
        format: int32
      averageStudentPerformance:
        type: number
        format: float

  StatusHistory:
    type: object
    properties:
      status:
        type: integer
        format: int32
        enum: [0, 1, 2, 3, 4, 5]
        description: |
          0: Due
          1: Submitted
          2: Processing
          3: Graded
          4: Failed
          5: Published
        x-go-const-names:
          - SubmissionStatusDue
          - SubmissionStatusSubmitted
          - SubmissionStatusProcessing
          - SubmissionStatusGraded
          - SubmissionStatusFailed
          - SubmissionStatusPublished
      timestamp:
        type: string
        format: date-time
      updated_by:
        type: string

  SubmissionData:
    type: object
    required:
      - studentId
      - imageUrls
    properties:
      studentId:
        type: string
      imageUrls:
        type: array
        items:
          type: string

  BulkCreateSubmissionBody:
    type: object
    properties:
      submissionData:
        type: array
        items:
          $ref: "#/definitions/SubmissionData"
