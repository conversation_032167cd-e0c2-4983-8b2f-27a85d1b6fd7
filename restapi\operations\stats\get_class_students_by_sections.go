// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassStudentsBySectionsHandlerFunc turns a function with the right signature into a get class students by sections handler
type GetClassStudentsBySectionsHandlerFunc func(GetClassStudentsBySectionsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassStudentsBySectionsHandlerFunc) Handle(params GetClassStudentsBySectionsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassStudentsBySectionsHandler interface for that can handle valid get class students by sections params
type GetClassStudentsBySectionsHandler interface {
	Handle(GetClassStudentsBySectionsParams, interface{}) middleware.Responder
}

// NewGetClassStudentsBySections creates a new http.Handler for the get class students by sections operation
func NewGetClassStudentsBySections(ctx *middleware.Context, handler GetClassStudentsBySectionsHandler) *GetClassStudentsBySections {
	return &GetClassStudentsBySections{Context: ctx, Handler: handler}
}

/*
	GetClassStudentsBySections swagger:route GET /institute/{instituteId}/class/{class}/studentsbysections stats getClassStudentsBySections

# Get Class Students By Sections

Get Class Students By Sections.
*/
type GetClassStudentsBySections struct {
	Context *middleware.Context
	Handler GetClassStudentsBySectionsHandler
}

func (o *GetClassStudentsBySections) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassStudentsBySectionsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
