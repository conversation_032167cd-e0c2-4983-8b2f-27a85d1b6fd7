// This file is safe to edit. Once it exists it will not be overwritten

package restapi

import (
	"context"
	"crypto/tls"
	"net/http"
	"os"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"eddyowl-backend/components"
	"eddyowl-backend/config"
	custom_middlewares "eddyowl-backend/custom-middlewares"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/db"
	"eddyowl-backend/entities"
	assignmentHandlers "eddyowl-backend/handlers/assignment"
	autoHandlers "eddyowl-backend/handlers/auto"
	folderHandlers "eddyowl-backend/handlers/folder"
	instituteHandlers "eddyowl-backend/handlers/institute"
	instructorHandlers "eddyowl-backend/handlers/instructor"
	statsHandlers "eddyowl-backend/handlers/stats"
	studentHandlers "eddyowl-backend/handlers/student"
	subjectHandlers "eddyowl-backend/handlers/subject"
	submissionHandlers "eddyowl-backend/handlers/submission"
	termHandlers "eddyowl-backend/handlers/term"
	userHandlers "eddyowl-backend/handlers/user"
	"eddyowl-backend/restapi/operations"
	"eddyowl-backend/secrets"
	"eddyowl-backend/utils"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

//go:generate swagger generate server --target ../ --name EddyOwlCoreAPI --spec ../api/swagger.yaml --principal interface{} --default-scheme https

func configureFlags(api *operations.EddyOwlCoreAPIAPI) {
	// api.CommandLineOptionsGroups = []swag.CommandLineOptionsGroup{ ... }
}

var (
	tracer trace.Tracer
)

// OTLP Exporter
func newOTLPExporter(ctx context.Context, otlpEndpoint string) (sdktrace.SpanExporter, error) {
	// Change default HTTPS -> HTTP
	insecureOpt := otlptracegrpc.WithInsecure()

	// Update default OTLP reciver endpoint
	endpointOpt := otlptracegrpc.WithEndpoint(otlpEndpoint)

	return otlptracegrpc.New(ctx, insecureOpt, endpointOpt)
}

// TracerProvider is an OpenTelemetry TracerProvider.
// It provides Tracers to instrumentation so it can trace operational flow through a system.
func newTraceProvider(exp sdktrace.SpanExporter) *sdktrace.TracerProvider {
	// Ensure default SDK resources and the required service name are set.
	// Create the tracer provider
	return sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exp),
		sdktrace.WithResource(resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceNameKey.String("eddyowl-backend"),
		)),
	)
}

// func newConsoleExporter(logger zerolog.Logger) (sdktrace.SpanExporter, error) {
// 	return stdouttrace.New(stdouttrace.WithWriter(logger.Write))
// }

func configureAPI(api *operations.EddyOwlCoreAPIAPI) http.Handler {
	ctx := context.Background()

	runLogFile, _ := os.OpenFile(
		"/var/log/eddy-owl-backend.log",
		os.O_APPEND|os.O_CREATE|os.O_WRONLY,
		0664,
	)
	multi := zerolog.MultiLevelWriter(os.Stdout, runLogFile)
	log.Logger = zerolog.New(multi).With().Timestamp().Logger()
	// configure the api here
	api.ServeError = errors.ServeError

	// Set your custom logger if needed. Default one is log.Printf
	// Expected interface func(string, ...interface{})
	//
	// Example:
	api.Logger = log.Logger.Printf

	api.UseSwaggerUI()
	// To continue using redoc as your UI, uncomment the following line
	// api.UseRedoc()

	api.JSONConsumer = runtime.JSONConsumer()

	api.JSONProducer = runtime.JSONProducer()

	secrets := secrets.LoadSecrets()
	if secrets == nil {
		log.Error()
	}

	config := config.LoadConfig()
	if config == nil {
		log.Error()
	}

	exp, err := newOTLPExporter(ctx, *config.OTLPEndpoint)
	if err != nil {
		log.Fatal().Msg("Failed to load OTLP Exporter")
	}

	// Create a new tracer provider with a batch span processor and the given exporter.
	tp := newTraceProvider(exp)
	otel.SetTracerProvider(tp)
	// Finally, set the tracer that can be used for this package.
	tracer = tp.Tracer("Backend")
	tokenInfoProvider, err := utils.NewTokenInfoProvider(*config.AuthIssuer, tracer)
	if err != nil {
		log.Fatal().Msg("Failed to load Token Info Provider")
	}

	dbClient := db.NewDBClient(*config.DBUrl, *secrets.DBUsername, *secrets.DBPassword)
	userProvider := data_providers.NewUserProvider(dbClient, *config.DBName, tracer)
	api.EddyowlOktaAuth = func(token string, scopes []string) (interface{}, error) {
		email, err := tokenInfoProvider.ValidateToken(ctx, token, "https://eddyowl.com/v1/api")
		if err != nil {
			return nil, err
		}
		_, err = userProvider.GetByEmail(ctx, email)
		if err != nil {
			userInfo, err := tokenInfoProvider.GetUserInfo(ctx, token)
			if err != nil {
				return nil, errors.New(http.StatusUnauthorized, "Unable to get user information")
			}
			user := entities.NewUser(&email, &userInfo.GivenName, &userInfo.FamilyName, email)
			userProvider.Add(ctx, user)
		}
		return email, nil
	}

	instituteProvider := data_providers.NewInstituteProvider(dbClient, *config.DBName, tracer)
	eddyOwlUserProvider := data_providers.NewUserProvider(dbClient, *config.DBName, tracer)
	instructorProvider := data_providers.NewInstructorProvider(dbClient, *config.DBName, tracer)
	termProvider := data_providers.NewTermProvider(tracer, dbClient, *config.DBName)
	studentProvider := data_providers.NewStudentProvider(dbClient, *config.DBName, tracer)
	assignmentProvider := data_providers.NewAssignmentProvider(dbClient, *config.DBName, tracer)
	aiComponent := components.NewAIInferenceComponent(*config.InferenceEndpoint, *config.Framework, *config.Model, *config.FrameworkLite, *config.ModelLite, tracer)
	submissionProvider := data_providers.NewSubmissionProvider(dbClient, *config.DBName, tracer)
	userRolesProvider := data_providers.NewUserRolesProvider(dbClient, *config.DBName, tracer)
	subjectProvider := data_providers.NewSubjectProvider(dbClient, *config.DBName, tracer)
	statsProvider := data_providers.NewStatsProvider(dbClient, *config.DBName, tracer)
	folderProvider := data_providers.NewFolderProvider(dbClient, *config.DBName, tracer)

	// InstituteHandlers
	api.InstituteCreateInstituteHandler = instituteHandlers.NewCreateInstituteHandler(instituteProvider, tokenInfoProvider, instructorProvider, termProvider, tracer)
	api.InstituteEditInstituteHandler = instituteHandlers.NewEditInstituteHandler(instituteProvider, userRolesProvider, tracer)
	api.InstituteDeleteInstituteByIDHandler = instituteHandlers.NewDeleteInstituteByIDHandler(instituteProvider, instructorProvider, termProvider, userRolesProvider, tracer)
	api.InstituteGetInstituteByIDHandler = instituteHandlers.NewGetInstituteByIDHandler(instituteProvider, userRolesProvider, tracer)

	// InstructorHandlers
	api.InstructorAddInstructorByInstituteIDHandler = instructorHandlers.NewAddInstructorByInstituteIDHandler(instructorProvider, eddyOwlUserProvider, instituteProvider, userRolesProvider, tracer)
	api.InstructorGetInstructorByInstituteIDHandler = instructorHandlers.NewGetInstructorByInstituteIDHandler(instructorProvider, userRolesProvider, tracer)
	api.InstructorDeleteInstructorByEmailHandler = instructorHandlers.NewDeleteInstructorHandler(instructorProvider, instituteProvider, userRolesProvider, tracer)
	api.InstructorEditInstructorByInstituteIDHandler = instructorHandlers.NewEditInstructorByInstituteIDHandler(instructorProvider, instituteProvider, userRolesProvider, tracer)

	// TermHandlers
	api.TermCreateNewTermHandler = termHandlers.NewCreateNewTermHandler(termProvider, userRolesProvider, tracer)
	api.TermGetAllTermsHandler = termHandlers.NewGetAllTermsHandler(termProvider, userRolesProvider, tracer)
	api.TermDeleteTermByIDHandler = termHandlers.NewDeleteTermByIDHandler(termProvider, userRolesProvider, tracer)

	// StudentHandlers
	api.StudentCreateNewStudentHandler = studentHandlers.NewCreateNewStudentHandler(studentProvider, instituteProvider, eddyOwlUserProvider, termProvider, userRolesProvider, tracer)
	api.StudentGetAllStudentsHandler = studentHandlers.NewGetAllStudentsHandler(studentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.StudentDeleteStudentByIDHandler = studentHandlers.NewDeleteStudentHandler(studentProvider, instituteProvider, userRolesProvider, tracer)
	api.StudentGetStudentByIDHandler = studentHandlers.NewGetStudentHandler(studentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.StudentEditStudentHandler = studentHandlers.NewEditStudentHandler(studentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.StudentGetStudentByEmailHandler = studentHandlers.NewGetStudentByEmailHandler(studentProvider, instituteProvider, userRolesProvider, termProvider, tracer)

	//AssignmentHandlers
	api.AssignmentCreateAssignmentHandler = assignmentHandlers.NewCreateAssignmentHandler(assignmentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.AssignmentAddTopicsToAssignmentHandler = assignmentHandlers.NewAddTopicsToAssignmentHandler(assignmentProvider, instituteProvider, aiComponent, userRolesProvider, tracer)
	api.AssignmentGetAllAssignmentsHandler = assignmentHandlers.NewGetAllAssignmentHandler(assignmentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.AssignmentCreateAssignmentHandler = assignmentHandlers.NewCreateAssignmentHandler(assignmentProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.AssignmentDeleteAssignmentByIDHandler = assignmentHandlers.NewDeleteAssignmentByIDHandler(assignmentProvider, instituteProvider, userRolesProvider, tracer)
	api.AssignmentEditAssignmentHandler = assignmentHandlers.NewEditAssignmentHandler(assignmentProvider, instituteProvider, userRolesProvider, tracer)
	api.AssignmentGetAssignmentByIDHandler = assignmentHandlers.NewGetAssignmentByIDHandler(assignmentProvider, userRolesProvider, tracer)

	// AutoHandlers
	api.AutoAutoCreateAssignmentHandler = autoHandlers.NewAutoCreateAssignmentHandler(assignmentProvider, instituteProvider, termProvider, aiComponent, userRolesProvider, tracer)
	api.AutoAutoAddRubricHandler = autoHandlers.NewAutoAddRubricHandler(aiComponent, instituteProvider, userRolesProvider, tracer)
	api.AutoAutoFileRubricHandler = autoHandlers.NewAutoFileRubricHandler(aiComponent, instituteProvider, userRolesProvider, tracer)

	// FolderHandlers
	api.FolderCreateFolderHandler = folderHandlers.NewCreateFolderHandler(folderProvider, userRolesProvider, tracer)

	// SubmissionHandlers
	api.SubmissionCreateSubmissionHandler = submissionHandlers.NewCreateSubmissionHandler(submissionProvider, assignmentProvider, studentProvider, aiComponent, userRolesProvider, tracer)
	api.SubmissionGetAllSubmissionsHandler = submissionHandlers.NewGetAllSubmissionHandler(submissionProvider, instituteProvider, userRolesProvider, termProvider, studentProvider, tracer)
	api.SubmissionGetSubmissionHandler = submissionHandlers.NewGetSubmissionHandler(submissionProvider, userRolesProvider, statsProvider, tracer)
	api.SubmissionEditSubmissionHandler = submissionHandlers.NewEditSubmissionHandler(submissionProvider, assignmentProvider, userRolesProvider, tracer)
	api.SubmissionDeleteSubmissionHandler = submissionHandlers.NewDeleteSubmissionHandler(submissionProvider, assignmentProvider, userRolesProvider, tracer)
	api.SubmissionPublishAllSubmissionsHandler = submissionHandlers.NewPublishAllSubmissionsHandler(submissionProvider, instituteProvider, userRolesProvider, tracer)
	api.SubmissionPublishSubmissionHandler = submissionHandlers.NewPublishSubmissionHandler(submissionProvider, instituteProvider, userRolesProvider, tracer)
	api.SubmissionBulkCreateSubmissionHandler = submissionHandlers.NewBulkCreateSubmissionHandler(
		submissionProvider,
		assignmentProvider,
		studentProvider,
		aiComponent,
		userRolesProvider,
		tracer,
	)

	// UserInfoHandlers
	api.UserGetUserRolesHandler = userHandlers.NewGetUserRolesHandler(userRolesProvider, tracer)

	// SubjectHandlers
	api.SubjectGetSubjectsHandler = subjectHandlers.NewGetSubjectsHandler(subjectProvider, instituteProvider, userRolesProvider, tracer)

	// StatsHandlers
	// Institute Stats
	api.StatsGetAllStatsHandler = statsHandlers.NewGetAllStatsHandler(statsProvider, instituteProvider, termProvider, userRolesProvider, tracer)
	api.StatsGetWeeklyAssessmentsHandler = statsHandlers.NewGetWeeklyAssessmentsHandler(statsProvider, termProvider, tracer)
	api.StatsGetMonthlyAssessmentsHandler = statsHandlers.NewGetMonthlyAssessmentsHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassDetailsHandler = statsHandlers.NewGetClassDetailsHandler(statsProvider, termProvider, tracer)
	// Class Stats
	api.StatsGetClassStatsHandler = statsHandlers.NewGetClassStatsHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassAssessmentsBySubjectHandler = statsHandlers.NewGetClassAssessmentsBySubjectHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassAssessmentsBySubjectMonthlyHandler = statsHandlers.NewGetClassAssessmentsBySubjectMonthlyHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassAssessmentsBySectionsHandler = statsHandlers.NewGetClassAssessmentsBySectionsHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassStudentsBySectionsHandler = statsHandlers.NewGetClassStudentsBySectionsHandler(statsProvider, termProvider, tracer)
	api.StatsGetClassTopPerformingStudentsHandler = statsHandlers.NewGetClassTopPerformingStudentsHandler(statsProvider, termProvider, tracer)
	// Student Stats
	api.StatsGetStudentOverallStatsHandler = statsHandlers.NewGetStudentOverallStatsHandler(statsProvider, termProvider, tracer)

	api.PreServerShutdown = func() {}

	api.ServerShutdown = func() {
		dbClient.Disconnect(context.TODO())
		// Handle shutdown properly so nothing leaks.
		tp.Shutdown(ctx)
	}

	// return setupGlobalMiddleware(api.Serve(setupMiddlewares), tokenInfoProvider, instructorProvider)
	return setupGlobalMiddleware(api.Serve(setupMiddlewares))
}

// The TLS configuration before HTTPS server starts.
func configureTLS(tlsConfig *tls.Config) {
	// Make all necessary changes to the TLS configuration here.
}

// As soon as server is initialized but not run yet, this function will be called.
// If you need to modify a config, store server instance to stop it individually later, this is the place.
// This function can be called multiple times, depending on the number of serving schemes.
// scheme value will be set accordingly: "http", "https" or "unix".
func configureServer(s *http.Server, scheme, addr string) {

}

// The middleware configuration is for the handler executors. These do not apply to the swagger.json document.
// The middleware executes after routing but before authentication, binding and validation.
func setupMiddlewares(handler http.Handler) http.Handler {
	return handler
}

// The middleware configuration happens before anything, this middleware also applies to serving the swagger.json document.
// So this is a good place to plug in a panic handling middleware, logging and metrics.
// func setupGlobalMiddleware(handler http.Handler, tokenInfoProvider utils.TokenInfoProvider, instructorProvider data_providers.InstructorProvider) http.Handler {
// 	// return custom_middlewares.LoggingMiddleware(custom_middlewares.NewCORSMiddleware(custom_middlewares.NewScurityMiddleware(handler, tokenInfoProvider, instructorProvider, tracer)))
// 	return handler
// }

func setupGlobalMiddleware(handler http.Handler) http.Handler {
	return custom_middlewares.LoggingMiddleware(custom_middlewares.NewCORSMiddleware(handler))
}
