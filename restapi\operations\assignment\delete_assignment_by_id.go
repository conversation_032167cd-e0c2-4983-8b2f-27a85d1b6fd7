// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteAssignmentByIDHandlerFunc turns a function with the right signature into a delete assignment by ID handler
type DeleteAssignmentByIDHandlerFunc func(DeleteAssignmentByIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteAssignmentByIDHandlerFunc) Handle(params DeleteAssignmentByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteAssignmentByIDHandler interface for that can handle valid delete assignment by ID params
type DeleteAssignmentByIDHandler interface {
	Handle(DeleteAssignmentByIDParams, interface{}) middleware.Responder
}

// NewDeleteAssignmentByID creates a new http.Handler for the delete assignment by ID operation
func NewDeleteAssignmentByID(ctx *middleware.Context, handler DeleteAssignment<PERSON>y<PERSON><PERSON><PERSON><PERSON>) *DeleteAssignmentByID {
	return &DeleteAssignmentByID{Context: ctx, Handler: handler}
}

/*
	DeleteAssignmentByID swagger:route DELETE /institute/{instituteId}/assignment/{assignmentId} assignment deleteAssignmentById

# Delete assignment

Delete assignment by assignmentid for an institute
*/
type DeleteAssignmentByID struct {
	Context *middleware.Context
	Handler DeleteAssignmentByIDHandler
}

func (o *DeleteAssignmentByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteAssignmentByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
