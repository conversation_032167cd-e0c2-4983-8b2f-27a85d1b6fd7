package handlers

import (
	"context"
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/auto"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type createAssignmentAutoImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	termProvider       data_providers.TermProvider
	aiComponent        components.AIComponent
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewAutoCreateAssignmentHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	aiComponent components.AIComponent,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) auto.AutoCreateAssignmentHandler {
	return &createAssignmentAutoImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		termProvider:       termProvider,
		aiComponent:        aiComponent,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *createAssignmentAutoImpl) Handle(params auto.AutoCreateAssignmentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : AutoCreateAssignmentHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return auto.NewAutoCreateAssignmentForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.createAssignmentAutoValidator(ctx, params)
	if !valid {
		return validateResp
	}
	questionSchema, err := impl.aiComponent.CreateAssignment(ctx, params.Files.Files)
	if err != nil {
		log.Error().Msg(err.Error())
		return auto.NewAutoCreateAssignmentInternalServerError().WithPayload("Unable to create Assignment")
	}
	questionList := []*models.Question{}
	for _, question := range *questionSchema {
		questionList = append(questionList, &models.Question{
			QuestionNumber: int32(question.QuestionNumber),
			Question:       question.Question,
			QuestionScore:  float32(question.QuestionScore),
		})
	}
	return auto.NewAutoCreateAssignmentOK().WithPayload(
		&models.QuestionList{
			QuestionList: questionList,
		},
	)
}

func (impl *createAssignmentAutoImpl) createAssignmentAutoValidator(ctx context.Context, params auto.AutoCreateAssignmentParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, auto.NewAutoCreateAssignmentBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, auto.NewAutoCreateAssignmentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, auto.NewAutoCreateAssignmentInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if len(params.Files.Files) <= 0 {
		return false, auto.NewAutoCreateAssignmentBadRequest().WithPayload("Invalid Image Urls")
	}
	return true, nil
}
