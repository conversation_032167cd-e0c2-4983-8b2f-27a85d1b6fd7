// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetStudentByIDOKCode is the HTTP code returned for type GetStudentByIDOK
const GetStudentByIDOKCode int = 200

/*
GetStudentByIDOK Successful operation

swagger:response getStudentByIdOK
*/
type GetStudentByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.Student `json:"body,omitempty"`
}

// NewGetStudentByIDOK creates GetStudentByIDOK with default headers values
func NewGetStudentByIDOK() *GetStudentByIDOK {

	return &GetStudentByIDOK{}
}

// WithPayload adds the payload to the get student by Id o k response
func (o *GetStudentByIDOK) WithPayload(payload *models.Student) *GetStudentByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id o k response
func (o *GetStudentByIDOK) SetPayload(payload *models.Student) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetStudentByIDBadRequestCode is the HTTP code returned for type GetStudentByIDBadRequest
const GetStudentByIDBadRequestCode int = 400

/*
GetStudentByIDBadRequest Bad Request

swagger:response getStudentByIdBadRequest
*/
type GetStudentByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDBadRequest creates GetStudentByIDBadRequest with default headers values
func NewGetStudentByIDBadRequest() *GetStudentByIDBadRequest {

	return &GetStudentByIDBadRequest{}
}

// WithPayload adds the payload to the get student by Id bad request response
func (o *GetStudentByIDBadRequest) WithPayload(payload models.ErrorResponse) *GetStudentByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id bad request response
func (o *GetStudentByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByIDForbiddenCode is the HTTP code returned for type GetStudentByIDForbidden
const GetStudentByIDForbiddenCode int = 403

/*
GetStudentByIDForbidden Forbidden

swagger:response getStudentByIdForbidden
*/
type GetStudentByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDForbidden creates GetStudentByIDForbidden with default headers values
func NewGetStudentByIDForbidden() *GetStudentByIDForbidden {

	return &GetStudentByIDForbidden{}
}

// WithPayload adds the payload to the get student by Id forbidden response
func (o *GetStudentByIDForbidden) WithPayload(payload models.ErrorResponse) *GetStudentByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id forbidden response
func (o *GetStudentByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByIDNotFoundCode is the HTTP code returned for type GetStudentByIDNotFound
const GetStudentByIDNotFoundCode int = 404

/*
GetStudentByIDNotFound Not Found

swagger:response getStudentByIdNotFound
*/
type GetStudentByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDNotFound creates GetStudentByIDNotFound with default headers values
func NewGetStudentByIDNotFound() *GetStudentByIDNotFound {

	return &GetStudentByIDNotFound{}
}

// WithPayload adds the payload to the get student by Id not found response
func (o *GetStudentByIDNotFound) WithPayload(payload models.ErrorResponse) *GetStudentByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id not found response
func (o *GetStudentByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByIDTooManyRequestsCode is the HTTP code returned for type GetStudentByIDTooManyRequests
const GetStudentByIDTooManyRequestsCode int = 429

/*
GetStudentByIDTooManyRequests Too Many Requests

swagger:response getStudentByIdTooManyRequests
*/
type GetStudentByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDTooManyRequests creates GetStudentByIDTooManyRequests with default headers values
func NewGetStudentByIDTooManyRequests() *GetStudentByIDTooManyRequests {

	return &GetStudentByIDTooManyRequests{}
}

// WithPayload adds the payload to the get student by Id too many requests response
func (o *GetStudentByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *GetStudentByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id too many requests response
func (o *GetStudentByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByIDInternalServerErrorCode is the HTTP code returned for type GetStudentByIDInternalServerError
const GetStudentByIDInternalServerErrorCode int = 500

/*
GetStudentByIDInternalServerError Internal Server Error

swagger:response getStudentByIdInternalServerError
*/
type GetStudentByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDInternalServerError creates GetStudentByIDInternalServerError with default headers values
func NewGetStudentByIDInternalServerError() *GetStudentByIDInternalServerError {

	return &GetStudentByIDInternalServerError{}
}

// WithPayload adds the payload to the get student by Id internal server error response
func (o *GetStudentByIDInternalServerError) WithPayload(payload models.ErrorResponse) *GetStudentByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id internal server error response
func (o *GetStudentByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByIDServiceUnavailableCode is the HTTP code returned for type GetStudentByIDServiceUnavailable
const GetStudentByIDServiceUnavailableCode int = 503

/*
GetStudentByIDServiceUnavailable Service Unvailable

swagger:response getStudentByIdServiceUnavailable
*/
type GetStudentByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByIDServiceUnavailable creates GetStudentByIDServiceUnavailable with default headers values
func NewGetStudentByIDServiceUnavailable() *GetStudentByIDServiceUnavailable {

	return &GetStudentByIDServiceUnavailable{}
}

// WithPayload adds the payload to the get student by Id service unavailable response
func (o *GetStudentByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetStudentByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by Id service unavailable response
func (o *GetStudentByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
