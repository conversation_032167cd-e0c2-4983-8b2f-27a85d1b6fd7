// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateSubmissionOKCode is the HTTP code returned for type CreateSubmissionOK
const CreateSubmissionOKCode int = 200

/*
CreateSubmissionOK Successful operation

swagger:response createSubmissionOK
*/
type CreateSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewCreateSubmissionOK creates CreateSubmissionOK with default headers values
func NewCreateSubmissionOK() *CreateSubmissionOK {

	return &CreateSubmissionOK{}
}

// WithPayload adds the payload to the create submission o k response
func (o *CreateSubmissionOK) WithPayload(payload *models.SuccessResponse) *CreateSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission o k response
func (o *CreateSubmissionOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateSubmissionBadRequestCode is the HTTP code returned for type CreateSubmissionBadRequest
const CreateSubmissionBadRequestCode int = 400

/*
CreateSubmissionBadRequest Bad Request

swagger:response createSubmissionBadRequest
*/
type CreateSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionBadRequest creates CreateSubmissionBadRequest with default headers values
func NewCreateSubmissionBadRequest() *CreateSubmissionBadRequest {

	return &CreateSubmissionBadRequest{}
}

// WithPayload adds the payload to the create submission bad request response
func (o *CreateSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *CreateSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission bad request response
func (o *CreateSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateSubmissionForbiddenCode is the HTTP code returned for type CreateSubmissionForbidden
const CreateSubmissionForbiddenCode int = 403

/*
CreateSubmissionForbidden Forbidden

swagger:response createSubmissionForbidden
*/
type CreateSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionForbidden creates CreateSubmissionForbidden with default headers values
func NewCreateSubmissionForbidden() *CreateSubmissionForbidden {

	return &CreateSubmissionForbidden{}
}

// WithPayload adds the payload to the create submission forbidden response
func (o *CreateSubmissionForbidden) WithPayload(payload models.ErrorResponse) *CreateSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission forbidden response
func (o *CreateSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateSubmissionNotFoundCode is the HTTP code returned for type CreateSubmissionNotFound
const CreateSubmissionNotFoundCode int = 404

/*
CreateSubmissionNotFound Not Found

swagger:response createSubmissionNotFound
*/
type CreateSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionNotFound creates CreateSubmissionNotFound with default headers values
func NewCreateSubmissionNotFound() *CreateSubmissionNotFound {

	return &CreateSubmissionNotFound{}
}

// WithPayload adds the payload to the create submission not found response
func (o *CreateSubmissionNotFound) WithPayload(payload models.ErrorResponse) *CreateSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission not found response
func (o *CreateSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateSubmissionTooManyRequestsCode is the HTTP code returned for type CreateSubmissionTooManyRequests
const CreateSubmissionTooManyRequestsCode int = 429

/*
CreateSubmissionTooManyRequests Too Many Requests

swagger:response createSubmissionTooManyRequests
*/
type CreateSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionTooManyRequests creates CreateSubmissionTooManyRequests with default headers values
func NewCreateSubmissionTooManyRequests() *CreateSubmissionTooManyRequests {

	return &CreateSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the create submission too many requests response
func (o *CreateSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *CreateSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission too many requests response
func (o *CreateSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateSubmissionInternalServerErrorCode is the HTTP code returned for type CreateSubmissionInternalServerError
const CreateSubmissionInternalServerErrorCode int = 500

/*
CreateSubmissionInternalServerError Internal Server Error

swagger:response createSubmissionInternalServerError
*/
type CreateSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionInternalServerError creates CreateSubmissionInternalServerError with default headers values
func NewCreateSubmissionInternalServerError() *CreateSubmissionInternalServerError {

	return &CreateSubmissionInternalServerError{}
}

// WithPayload adds the payload to the create submission internal server error response
func (o *CreateSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *CreateSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission internal server error response
func (o *CreateSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateSubmissionServiceUnavailableCode is the HTTP code returned for type CreateSubmissionServiceUnavailable
const CreateSubmissionServiceUnavailableCode int = 503

/*
CreateSubmissionServiceUnavailable Service Unvailable

swagger:response createSubmissionServiceUnavailable
*/
type CreateSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateSubmissionServiceUnavailable creates CreateSubmissionServiceUnavailable with default headers values
func NewCreateSubmissionServiceUnavailable() *CreateSubmissionServiceUnavailable {

	return &CreateSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the create submission service unavailable response
func (o *CreateSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *CreateSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create submission service unavailable response
func (o *CreateSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
