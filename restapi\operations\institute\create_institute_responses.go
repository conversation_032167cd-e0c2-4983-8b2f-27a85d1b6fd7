// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateInstituteOKCode is the HTTP code returned for type CreateInstituteOK
const CreateInstituteOKCode int = 200

/*
CreateInstituteOK Successful operation

swagger:response createInstituteOK
*/
type CreateInstituteOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewCreateInstituteOK creates CreateInstituteOK with default headers values
func NewCreateInstituteOK() *CreateInstituteOK {

	return &CreateInstituteOK{}
}

// WithPayload adds the payload to the create institute o k response
func (o *CreateInstituteOK) WithPayload(payload *models.SuccessResponse) *CreateInstituteOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute o k response
func (o *CreateInstituteOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateInstituteBadRequestCode is the HTTP code returned for type CreateInstituteBadRequest
const CreateInstituteBadRequestCode int = 400

/*
CreateInstituteBadRequest Bad Request

swagger:response createInstituteBadRequest
*/
type CreateInstituteBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteBadRequest creates CreateInstituteBadRequest with default headers values
func NewCreateInstituteBadRequest() *CreateInstituteBadRequest {

	return &CreateInstituteBadRequest{}
}

// WithPayload adds the payload to the create institute bad request response
func (o *CreateInstituteBadRequest) WithPayload(payload models.ErrorResponse) *CreateInstituteBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute bad request response
func (o *CreateInstituteBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteUnauthorizedCode is the HTTP code returned for type CreateInstituteUnauthorized
const CreateInstituteUnauthorizedCode int = 401

/*
CreateInstituteUnauthorized Unauthorized

swagger:response createInstituteUnauthorized
*/
type CreateInstituteUnauthorized struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteUnauthorized creates CreateInstituteUnauthorized with default headers values
func NewCreateInstituteUnauthorized() *CreateInstituteUnauthorized {

	return &CreateInstituteUnauthorized{}
}

// WithPayload adds the payload to the create institute unauthorized response
func (o *CreateInstituteUnauthorized) WithPayload(payload models.ErrorResponse) *CreateInstituteUnauthorized {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute unauthorized response
func (o *CreateInstituteUnauthorized) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteUnauthorized) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(401)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteForbiddenCode is the HTTP code returned for type CreateInstituteForbidden
const CreateInstituteForbiddenCode int = 403

/*
CreateInstituteForbidden Forbidden

swagger:response createInstituteForbidden
*/
type CreateInstituteForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteForbidden creates CreateInstituteForbidden with default headers values
func NewCreateInstituteForbidden() *CreateInstituteForbidden {

	return &CreateInstituteForbidden{}
}

// WithPayload adds the payload to the create institute forbidden response
func (o *CreateInstituteForbidden) WithPayload(payload models.ErrorResponse) *CreateInstituteForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute forbidden response
func (o *CreateInstituteForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteNotFoundCode is the HTTP code returned for type CreateInstituteNotFound
const CreateInstituteNotFoundCode int = 404

/*
CreateInstituteNotFound Not Found

swagger:response createInstituteNotFound
*/
type CreateInstituteNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteNotFound creates CreateInstituteNotFound with default headers values
func NewCreateInstituteNotFound() *CreateInstituteNotFound {

	return &CreateInstituteNotFound{}
}

// WithPayload adds the payload to the create institute not found response
func (o *CreateInstituteNotFound) WithPayload(payload models.ErrorResponse) *CreateInstituteNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute not found response
func (o *CreateInstituteNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteTooManyRequestsCode is the HTTP code returned for type CreateInstituteTooManyRequests
const CreateInstituteTooManyRequestsCode int = 429

/*
CreateInstituteTooManyRequests Too Many Requests

swagger:response createInstituteTooManyRequests
*/
type CreateInstituteTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteTooManyRequests creates CreateInstituteTooManyRequests with default headers values
func NewCreateInstituteTooManyRequests() *CreateInstituteTooManyRequests {

	return &CreateInstituteTooManyRequests{}
}

// WithPayload adds the payload to the create institute too many requests response
func (o *CreateInstituteTooManyRequests) WithPayload(payload models.ErrorResponse) *CreateInstituteTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute too many requests response
func (o *CreateInstituteTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteInternalServerErrorCode is the HTTP code returned for type CreateInstituteInternalServerError
const CreateInstituteInternalServerErrorCode int = 500

/*
CreateInstituteInternalServerError Internal Server Error

swagger:response createInstituteInternalServerError
*/
type CreateInstituteInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteInternalServerError creates CreateInstituteInternalServerError with default headers values
func NewCreateInstituteInternalServerError() *CreateInstituteInternalServerError {

	return &CreateInstituteInternalServerError{}
}

// WithPayload adds the payload to the create institute internal server error response
func (o *CreateInstituteInternalServerError) WithPayload(payload models.ErrorResponse) *CreateInstituteInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute internal server error response
func (o *CreateInstituteInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateInstituteServiceUnavailableCode is the HTTP code returned for type CreateInstituteServiceUnavailable
const CreateInstituteServiceUnavailableCode int = 503

/*
CreateInstituteServiceUnavailable Service Unvailable

swagger:response createInstituteServiceUnavailable
*/
type CreateInstituteServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateInstituteServiceUnavailable creates CreateInstituteServiceUnavailable with default headers values
func NewCreateInstituteServiceUnavailable() *CreateInstituteServiceUnavailable {

	return &CreateInstituteServiceUnavailable{}
}

// WithPayload adds the payload to the create institute service unavailable response
func (o *CreateInstituteServiceUnavailable) WithPayload(payload models.ErrorResponse) *CreateInstituteServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create institute service unavailable response
func (o *CreateInstituteServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateInstituteServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
