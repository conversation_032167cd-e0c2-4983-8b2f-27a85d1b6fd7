// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetStudentOverallStatsOKCode is the HTTP code returned for type GetStudentOverallStatsOK
const GetStudentOverallStatsOKCode int = 200

/*
GetStudentOverallStatsOK Successful operation

swagger:response getStudentOverallStatsOK
*/
type GetStudentOverallStatsOK struct {

	/*
	  In: Body
	*/
	Payload *models.StudentOverallStats `json:"body,omitempty"`
}

// NewGetStudentOverallStatsOK creates GetStudentOverallStatsOK with default headers values
func NewGetStudentOverallStatsOK() *GetStudentOverallStatsOK {

	return &GetStudentOverallStatsOK{}
}

// WithPayload adds the payload to the get student overall stats o k response
func (o *GetStudentOverallStatsOK) WithPayload(payload *models.StudentOverallStats) *GetStudentOverallStatsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats o k response
func (o *GetStudentOverallStatsOK) SetPayload(payload *models.StudentOverallStats) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetStudentOverallStatsBadRequestCode is the HTTP code returned for type GetStudentOverallStatsBadRequest
const GetStudentOverallStatsBadRequestCode int = 400

/*
GetStudentOverallStatsBadRequest Bad Request

swagger:response getStudentOverallStatsBadRequest
*/
type GetStudentOverallStatsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsBadRequest creates GetStudentOverallStatsBadRequest with default headers values
func NewGetStudentOverallStatsBadRequest() *GetStudentOverallStatsBadRequest {

	return &GetStudentOverallStatsBadRequest{}
}

// WithPayload adds the payload to the get student overall stats bad request response
func (o *GetStudentOverallStatsBadRequest) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats bad request response
func (o *GetStudentOverallStatsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentOverallStatsForbiddenCode is the HTTP code returned for type GetStudentOverallStatsForbidden
const GetStudentOverallStatsForbiddenCode int = 403

/*
GetStudentOverallStatsForbidden Forbidden

swagger:response getStudentOverallStatsForbidden
*/
type GetStudentOverallStatsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsForbidden creates GetStudentOverallStatsForbidden with default headers values
func NewGetStudentOverallStatsForbidden() *GetStudentOverallStatsForbidden {

	return &GetStudentOverallStatsForbidden{}
}

// WithPayload adds the payload to the get student overall stats forbidden response
func (o *GetStudentOverallStatsForbidden) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats forbidden response
func (o *GetStudentOverallStatsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentOverallStatsNotFoundCode is the HTTP code returned for type GetStudentOverallStatsNotFound
const GetStudentOverallStatsNotFoundCode int = 404

/*
GetStudentOverallStatsNotFound Not Found

swagger:response getStudentOverallStatsNotFound
*/
type GetStudentOverallStatsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsNotFound creates GetStudentOverallStatsNotFound with default headers values
func NewGetStudentOverallStatsNotFound() *GetStudentOverallStatsNotFound {

	return &GetStudentOverallStatsNotFound{}
}

// WithPayload adds the payload to the get student overall stats not found response
func (o *GetStudentOverallStatsNotFound) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats not found response
func (o *GetStudentOverallStatsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentOverallStatsTooManyRequestsCode is the HTTP code returned for type GetStudentOverallStatsTooManyRequests
const GetStudentOverallStatsTooManyRequestsCode int = 429

/*
GetStudentOverallStatsTooManyRequests Too Many Requests

swagger:response getStudentOverallStatsTooManyRequests
*/
type GetStudentOverallStatsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsTooManyRequests creates GetStudentOverallStatsTooManyRequests with default headers values
func NewGetStudentOverallStatsTooManyRequests() *GetStudentOverallStatsTooManyRequests {

	return &GetStudentOverallStatsTooManyRequests{}
}

// WithPayload adds the payload to the get student overall stats too many requests response
func (o *GetStudentOverallStatsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats too many requests response
func (o *GetStudentOverallStatsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentOverallStatsInternalServerErrorCode is the HTTP code returned for type GetStudentOverallStatsInternalServerError
const GetStudentOverallStatsInternalServerErrorCode int = 500

/*
GetStudentOverallStatsInternalServerError Internal Server Error

swagger:response getStudentOverallStatsInternalServerError
*/
type GetStudentOverallStatsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsInternalServerError creates GetStudentOverallStatsInternalServerError with default headers values
func NewGetStudentOverallStatsInternalServerError() *GetStudentOverallStatsInternalServerError {

	return &GetStudentOverallStatsInternalServerError{}
}

// WithPayload adds the payload to the get student overall stats internal server error response
func (o *GetStudentOverallStatsInternalServerError) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats internal server error response
func (o *GetStudentOverallStatsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentOverallStatsServiceUnavailableCode is the HTTP code returned for type GetStudentOverallStatsServiceUnavailable
const GetStudentOverallStatsServiceUnavailableCode int = 503

/*
GetStudentOverallStatsServiceUnavailable Service Unvailable

swagger:response getStudentOverallStatsServiceUnavailable
*/
type GetStudentOverallStatsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentOverallStatsServiceUnavailable creates GetStudentOverallStatsServiceUnavailable with default headers values
func NewGetStudentOverallStatsServiceUnavailable() *GetStudentOverallStatsServiceUnavailable {

	return &GetStudentOverallStatsServiceUnavailable{}
}

// WithPayload adds the payload to the get student overall stats service unavailable response
func (o *GetStudentOverallStatsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetStudentOverallStatsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student overall stats service unavailable response
func (o *GetStudentOverallStatsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentOverallStatsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
