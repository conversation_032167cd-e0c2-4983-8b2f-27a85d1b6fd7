// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteInstructorByEmailHandlerFunc turns a function with the right signature into a delete instructor by email handler
type DeleteInstructorByEmailHandlerFunc func(DeleteInstructorByEmailParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteInstructorByEmailHandlerFunc) Handle(params DeleteInstructorByEmailParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteInstructorByEmailHandler interface for that can handle valid delete instructor by email params
type DeleteInstructorByEmailHandler interface {
	Handle(DeleteInstructorByEmailParams, interface{}) middleware.Responder
}

// NewDeleteInstructorByEmail creates a new http.Handler for the delete instructor by email operation
func NewDeleteInstructorByEmail(ctx *middleware.Context, handler DeleteInstructorByEmailHandler) *DeleteInstructorByEmail {
	return &DeleteInstructorByEmail{Context: ctx, Handler: handler}
}

/*
	DeleteInstructorByEmail swagger:route DELETE /institute/{instituteId}/instructor instructor deleteInstructorByEmail

# Delete instructor

Delete instructor by instructor email
*/
type DeleteInstructorByEmail struct {
	Context *middleware.Context
	Handler DeleteInstructorByEmailHandler
}

func (o *DeleteInstructorByEmail) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteInstructorByEmailParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
