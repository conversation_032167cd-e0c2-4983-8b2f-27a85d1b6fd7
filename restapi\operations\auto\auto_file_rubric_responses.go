// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// AutoFileRubricOKCode is the HTTP code returned for type AutoFileRubricOK
const AutoFileRubricOKCode int = 200

/*
AutoFileRubricOK Successful operation

swagger:response autoFileRubricOK
*/
type AutoFileRubricOK struct {

	/*
	  In: Body
	*/
	Payload *models.QuestionList `json:"body,omitempty"`
}

// NewAutoFileRubricOK creates AutoFileRubricOK with default headers values
func NewAutoFileRubricOK() *AutoFileRubricOK {

	return &AutoFileRubricOK{}
}

// WithPayload adds the payload to the auto file rubric o k response
func (o *AutoFileRubricOK) WithPayload(payload *models.QuestionList) *AutoFileRubricOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric o k response
func (o *AutoFileRubricOK) SetPayload(payload *models.QuestionList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// AutoFileRubricBadRequestCode is the HTTP code returned for type AutoFileRubricBadRequest
const AutoFileRubricBadRequestCode int = 400

/*
AutoFileRubricBadRequest Bad Request

swagger:response autoFileRubricBadRequest
*/
type AutoFileRubricBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricBadRequest creates AutoFileRubricBadRequest with default headers values
func NewAutoFileRubricBadRequest() *AutoFileRubricBadRequest {

	return &AutoFileRubricBadRequest{}
}

// WithPayload adds the payload to the auto file rubric bad request response
func (o *AutoFileRubricBadRequest) WithPayload(payload models.ErrorResponse) *AutoFileRubricBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric bad request response
func (o *AutoFileRubricBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoFileRubricForbiddenCode is the HTTP code returned for type AutoFileRubricForbidden
const AutoFileRubricForbiddenCode int = 403

/*
AutoFileRubricForbidden Forbidden

swagger:response autoFileRubricForbidden
*/
type AutoFileRubricForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricForbidden creates AutoFileRubricForbidden with default headers values
func NewAutoFileRubricForbidden() *AutoFileRubricForbidden {

	return &AutoFileRubricForbidden{}
}

// WithPayload adds the payload to the auto file rubric forbidden response
func (o *AutoFileRubricForbidden) WithPayload(payload models.ErrorResponse) *AutoFileRubricForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric forbidden response
func (o *AutoFileRubricForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoFileRubricNotFoundCode is the HTTP code returned for type AutoFileRubricNotFound
const AutoFileRubricNotFoundCode int = 404

/*
AutoFileRubricNotFound Not Found

swagger:response autoFileRubricNotFound
*/
type AutoFileRubricNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricNotFound creates AutoFileRubricNotFound with default headers values
func NewAutoFileRubricNotFound() *AutoFileRubricNotFound {

	return &AutoFileRubricNotFound{}
}

// WithPayload adds the payload to the auto file rubric not found response
func (o *AutoFileRubricNotFound) WithPayload(payload models.ErrorResponse) *AutoFileRubricNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric not found response
func (o *AutoFileRubricNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoFileRubricTooManyRequestsCode is the HTTP code returned for type AutoFileRubricTooManyRequests
const AutoFileRubricTooManyRequestsCode int = 429

/*
AutoFileRubricTooManyRequests Too Many Requests

swagger:response autoFileRubricTooManyRequests
*/
type AutoFileRubricTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricTooManyRequests creates AutoFileRubricTooManyRequests with default headers values
func NewAutoFileRubricTooManyRequests() *AutoFileRubricTooManyRequests {

	return &AutoFileRubricTooManyRequests{}
}

// WithPayload adds the payload to the auto file rubric too many requests response
func (o *AutoFileRubricTooManyRequests) WithPayload(payload models.ErrorResponse) *AutoFileRubricTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric too many requests response
func (o *AutoFileRubricTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoFileRubricInternalServerErrorCode is the HTTP code returned for type AutoFileRubricInternalServerError
const AutoFileRubricInternalServerErrorCode int = 500

/*
AutoFileRubricInternalServerError Internal Server Error

swagger:response autoFileRubricInternalServerError
*/
type AutoFileRubricInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricInternalServerError creates AutoFileRubricInternalServerError with default headers values
func NewAutoFileRubricInternalServerError() *AutoFileRubricInternalServerError {

	return &AutoFileRubricInternalServerError{}
}

// WithPayload adds the payload to the auto file rubric internal server error response
func (o *AutoFileRubricInternalServerError) WithPayload(payload models.ErrorResponse) *AutoFileRubricInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric internal server error response
func (o *AutoFileRubricInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoFileRubricServiceUnavailableCode is the HTTP code returned for type AutoFileRubricServiceUnavailable
const AutoFileRubricServiceUnavailableCode int = 503

/*
AutoFileRubricServiceUnavailable Service Unvailable

swagger:response autoFileRubricServiceUnavailable
*/
type AutoFileRubricServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoFileRubricServiceUnavailable creates AutoFileRubricServiceUnavailable with default headers values
func NewAutoFileRubricServiceUnavailable() *AutoFileRubricServiceUnavailable {

	return &AutoFileRubricServiceUnavailable{}
}

// WithPayload adds the payload to the auto file rubric service unavailable response
func (o *AutoFileRubricServiceUnavailable) WithPayload(payload models.ErrorResponse) *AutoFileRubricServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto file rubric service unavailable response
func (o *AutoFileRubricServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoFileRubricServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
