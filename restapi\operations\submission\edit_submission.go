// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditSubmissionHandlerFunc turns a function with the right signature into a edit submission handler
type EditSubmissionHandlerFunc func(EditSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditSubmissionHandlerFunc) Handle(params EditSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditSubmissionHandler interface for that can handle valid edit submission params
type EditSubmissionHandler interface {
	Handle(EditSubmissionParams, interface{}) middleware.Responder
}

// NewEditSubmission creates a new http.Handler for the edit submission operation
func NewEditSubmission(ctx *middleware.Context, handler EditSubmissionHandler) *EditSubmission {
	return &EditSubmission{Context: ctx, Handler: handler}
}

/*
	EditSubmission swagger:route PUT /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/edit/submission submission editSubmission

# Edit student gradedsubmission

Edit scores and feedabck fo graded submission
*/
type EditSubmission struct {
	Context *middleware.Context
	Handler EditSubmissionHandler
}

func (o *EditSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
