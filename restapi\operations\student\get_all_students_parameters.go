// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetAllStudentsParams creates a new GetAllStudentsParams object
//
// There are no default values defined in the spec.
func NewGetAllStudentsParams() GetAllStudentsParams {

	return GetAllStudentsParams{}
}

// GetAllStudentsParams contains all the bound params for the get all students operation
// typically these are obtained from a http.Request
//
// swagger:parameters GetAllStudents
type GetAllStudentsParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  In: query
	*/
	Class *int32
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  In: query
	*/
	Sections []string
	/*
	  In: query
	*/
	TermID *string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewGetAllStudentsParams() beforehand.
func (o *GetAllStudentsParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	qs := runtime.Values(r.URL.Query())

	qClass, qhkClass, _ := qs.GetOK("class")
	if err := o.bindClass(qClass, qhkClass, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	qSections, qhkSections, _ := qs.GetOK("sections")
	if err := o.bindSections(qSections, qhkSections, route.Formats); err != nil {
		res = append(res, err)
	}

	qTermID, qhkTermID, _ := qs.GetOK("termId")
	if err := o.bindTermID(qTermID, qhkTermID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindClass binds and validates parameter Class from query.
func (o *GetAllStudentsParams) bindClass(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}

	value, err := swag.ConvertInt32(raw)
	if err != nil {
		return errors.InvalidType("class", "query", "int32", raw)
	}
	o.Class = &value

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *GetAllStudentsParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}

// bindSections binds and validates array parameter Sections from query.
//
// Arrays are parsed according to CollectionFormat: "" (defaults to "csv" when empty).
func (o *GetAllStudentsParams) bindSections(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var qvSections string
	if len(rawData) > 0 {
		qvSections = rawData[len(rawData)-1]
	}

	// CollectionFormat:
	sectionsIC := swag.SplitByFormat(qvSections, "")
	if len(sectionsIC) == 0 {
		return nil
	}

	var sectionsIR []string
	for _, sectionsIV := range sectionsIC {
		sectionsI := sectionsIV

		sectionsIR = append(sectionsIR, sectionsI)
	}

	o.Sections = sectionsIR

	return nil
}

// bindTermID binds and validates parameter TermID from query.
func (o *GetAllStudentsParams) bindTermID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.TermID = &raw

	return nil
}
