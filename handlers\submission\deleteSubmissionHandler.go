package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type deleteSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	assignmentProvider data_providers.AssignmentProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewDeleteSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	assignmentProvider data_providers.AssignmentProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.DeleteSubmissionHandler {
	return &deleteSubmissionImpl{
		submissionProvider: submissionProvider,
		assignmentProvider: assignmentProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *deleteSubmissionImpl) Handle(params submission.DeleteSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteSubmissionHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewDeleteSubmissionForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.deleteSubmissionValidator(params)
	if !valid {
		return validateResp
	}
	deletedBy := principal.(string)

	err = impl.submissionProvider.Delete(ctx, params.InstituteID, params.AssignmentID, params.StudentID, deletedBy)
	if err != nil {
		log.Error().Msg(err.Error())
		return submission.NewDeleteSubmissionInternalServerError().WithPayload("Unable to delete submission")
	}

	return submission.NewDeleteSubmissionOK().WithPayload(
		&models.SuccessResponse{
			Message: "Successfully deleted submission",
		},
	)
}

func (impl *deleteSubmissionImpl) deleteSubmissionValidator(params submission.DeleteSubmissionParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "deleteSubmissionValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, submission.NewDeleteSubmissionBadRequest().WithPayload("Invalid Institute ID")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, submission.NewDeleteSubmissionBadRequest().WithPayload("Invalid Assignment ID")
	}
	if params.StudentID == constants.EmptyString {
		return false, submission.NewDeleteSubmissionBadRequest().WithPayload("Invalid Student ID")
	}
	_, err := impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, submission.NewDeleteSubmissionBadRequest().WithPayload("Invalid Assignment ID")
		}
		return false, submission.NewDeleteSubmissionInternalServerError().WithPayload("Unable to fetch Assignment")
	}
	return true, nil
}
