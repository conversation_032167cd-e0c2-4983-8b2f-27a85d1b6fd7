// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteFolderHandlerFunc turns a function with the right signature into a delete folder handler
type DeleteFolderHandlerFunc func(DeleteFolderParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteFolderHandlerFunc) Handle(params DeleteFolderParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteFolderHandler interface for that can handle valid delete folder params
type DeleteFolderHandler interface {
	Handle(DeleteFolderParams, interface{}) middleware.Responder
}

// NewDeleteFolder creates a new http.Handler for the delete folder operation
func NewDeleteFolder(ctx *middleware.Context, handler DeleteFolderHandler) *DeleteFolder {
	return &DeleteFolder{Context: ctx, Handler: handler}
}

/*
	DeleteFolder swagger:route DELETE /institute/{instituteId}/folder/{folderId} folder deleteFolder

Delete a folder
*/
type DeleteFolder struct {
	Context *middleware.Context
	Handler DeleteFolderHandler
}

func (o *DeleteFolder) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteFolderParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
