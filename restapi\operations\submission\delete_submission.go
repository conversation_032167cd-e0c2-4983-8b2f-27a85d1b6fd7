// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteSubmissionHandlerFunc turns a function with the right signature into a delete submission handler
type DeleteSubmissionHandlerFunc func(DeleteSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteSubmissionHandlerFunc) Handle(params DeleteSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteSubmissionHandler interface for that can handle valid delete submission params
type DeleteSubmissionHandler interface {
	Handle(DeleteSubmissionParams, interface{}) middleware.Responder
}

// NewDeleteSubmission creates a new http.Handler for the delete submission operation
func NewDeleteSubmission(ctx *middleware.Context, handler DeleteSubmissionHandler) *DeleteSubmission {
	return &DeleteSubmission{Context: ctx, Handler: handler}
}

/*
	DeleteSubmission swagger:route DELETE /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission submission deleteSubmission

# Delete student submission

Delete submission
*/
type DeleteSubmission struct {
	Context *middleware.Context
	Handler DeleteSubmissionHandler
}

func (o *DeleteSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
