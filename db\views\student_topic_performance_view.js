db.createView("student_chapter_performance_view", "submissions", [
  {
    $lookup: {
      from: "assignments",
      localField: "assignment_id",
      foreignField: "_id",
      as: "assignment",
    },
  },
  { $unwind: "$assignment" },
  { $unwind: "$assignment.questions" },
  { $unwind: "$student_responses" },
  {
    $match: {
      $expr: {
        $eq: [
          "$assignment.questions.question_number",
          "$student_responses.question_number",
        ],
      },
    },
  },
  { $unwind: "$assignment.questions.topics" },
  { $unwind: "$assignment.questions.topics.topics" },
  {
    $addFields: {
      normalized_score: {
        $multiply: [
          {
            $divide: [
              "$student_responses.score",
              "$assignment.questions.score",
            ],
          },
          10,
        ],
      },
    },
  },
  {
    $group: {
      _id: {
        assignment_id: "$assignment_id",
        term_id: "$assignment.term_id",
        student_id: "$student_id",
        subject: "$assignment.subject",
        chapter: "$assignment.questions.topics.chapter",
        topic: "$assignment.questions.topics.topics",
      },
      topic_score: { $avg: "$normalized_score" },
    },
  },
  {
    $group: {
      _id: {
        assignment_id: "$_id.assignment_id",
        term_id: "$_id.term_id",
        student_id: "$_id.student_id",
        subject: "$_id.subject",
        chapter: "$_id.chapter",
      },
      topic_performance: {
        $push: {
          name: "$_id.topic",
          score: "$topic_score",
        },
      },
      chapter_score: { $avg: "$topic_score" },
    },
  },
  {
    $group: {
      _id: {
        assignment_id: "$_id.assignment_id",
        term_id: "$_id.term_id",
        student_id: "$_id.student_id",
        subject: "$_id.subject",
      },
      chapter_performance: {
        $push: {
          name: "$_id.chapter",
          topic_performance: "$topic_performance",
          score: "$chapter_score",
        },
      },
    },
  },
  {
    $project: {
      _id: 0,
      assignment_id: "$_id.assignment_id",
      term_id: "$_id.term_id",
      student_id: "$_id.student_id",
      subject: "$_id.subject",
      chapter_performance: 1,
    },
  },
]);
