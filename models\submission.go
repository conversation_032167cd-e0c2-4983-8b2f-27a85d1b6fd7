// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Submission submission
//
// swagger:model Submission
type Submission struct {

	// assignment Id
	AssignmentID string `json:"assignmentId,omitempty"`

	// assignment name
	AssignmentName string `json:"assignmentName,omitempty"`

	// assignment score
	AssignmentScore int32 `json:"assignmentScore,omitempty"`

	// class
	Class int32 `json:"class,omitempty"`

	// section
	Section string `json:"section,omitempty"`

	// status
	Status string `json:"status,omitempty"`

	// student Id
	StudentID string `json:"studentId,omitempty"`

	// student name
	StudentName string `json:"studentName,omitempty"`

	// student roll number
	StudentRollNumber int32 `json:"studentRollNumber,omitempty"`

	// student score
	StudentScore float32 `json:"studentScore,omitempty"`

	// subject
	Subject string `json:"subject,omitempty"`
}

// Validate validates this submission
func (m *Submission) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this submission based on context it is used
func (m *Submission) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Submission) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Submission) UnmarshalBinary(b []byte) error {
	var res Submission
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
