package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/instructor"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type editInstructorByInstituteIDImpl struct {
	instructorProvider data_providers.InstructorProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewEditInstructorByInstituteIDHandler(
	instructorProvider data_providers.InstructorProvider, instituteProvider data_providers.InstituteProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) instructor.EditInstructorByInstituteIDHandler {
	return &editInstructorByInstituteIDImpl{instructorProvider: instructorProvider, instituteProvider: instituteProvider, userRolesProvider: userRolesProvider, tracer: tracer}
}

func (impl *editInstructorByInstituteIDImpl) Handle(params instructor.EditInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : EditInstructorByInstituteIDHandler")
	defer span.End()

	principal = principal.(string)
	if params.InstituteID == constants.EmptyString {
		return instructor.NewEditInstructorByInstituteIDBadRequest().WithPayload("Invalid InstituteID")
	}

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return instructor.NewEditInstructorByInstituteIDForbidden().WithPayload("Unauthorized")
	}

	// Validate institute exists
	_, err = impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get institute")
		return instructor.NewEditInstructorByInstituteIDBadRequest().WithPayload("Invalid institute ID")
	}

	// Get existing instructor
	existingInstructor, err := impl.instructorProvider.GetByEmailAndInstitute(ctx, params.Instructor.Email, params.InstituteID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return instructor.NewEditInstructorByInstituteIDNotFound().WithPayload("Instructor not found")
		}
		log.Error().Err(err).Msg("Failed to get instructor")
		return instructor.NewEditInstructorByInstituteIDInternalServerError().WithPayload("Failed to get instructor")
	}

	// Update instructor fields
	existingInstructor.FirstName = &params.Instructor.FirstName
	existingInstructor.LastName = &params.Instructor.LastName

	// Set updated by
	updatedBy := principal.(string)
	existingInstructor.UpdatedBy = &updatedBy

	// Validate instructor data
	if err := existingInstructor.Validate(); err != nil {
		return instructor.NewEditInstructorByInstituteIDBadRequest().WithPayload("Invalid instructor data")
	}

	// Update instructor
	err = impl.instructorProvider.Edit(ctx, *existingInstructor.ID, existingInstructor)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update instructor")
		return instructor.NewEditInstructorByInstituteIDInternalServerError().WithPayload("Failed to update instructor")
	}

	return instructor.NewEditInstructorByInstituteIDOK().WithPayload(&models.SuccessResponse{
		ID:      *existingInstructor.ID,
		Message: "Successfully updated instructor",
	})
}
