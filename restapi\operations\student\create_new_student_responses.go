// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateNewStudentOKCode is the HTTP code returned for type CreateNewStudentOK
const CreateNewStudentOKCode int = 200

/*
CreateNewStudentOK Successful operation

swagger:response createNewStudentOK
*/
type CreateNewStudentOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewCreateNewStudentOK creates CreateNewStudentOK with default headers values
func NewCreateNewStudentOK() *CreateNewStudentOK {

	return &CreateNewStudentOK{}
}

// WithPayload adds the payload to the create new student o k response
func (o *CreateNewStudentOK) WithPayload(payload *models.SuccessResponse) *CreateNewStudentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student o k response
func (o *CreateNewStudentOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateNewStudentBadRequestCode is the HTTP code returned for type CreateNewStudentBadRequest
const CreateNewStudentBadRequestCode int = 400

/*
CreateNewStudentBadRequest Bad Request

swagger:response createNewStudentBadRequest
*/
type CreateNewStudentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentBadRequest creates CreateNewStudentBadRequest with default headers values
func NewCreateNewStudentBadRequest() *CreateNewStudentBadRequest {

	return &CreateNewStudentBadRequest{}
}

// WithPayload adds the payload to the create new student bad request response
func (o *CreateNewStudentBadRequest) WithPayload(payload models.ErrorResponse) *CreateNewStudentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student bad request response
func (o *CreateNewStudentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewStudentForbiddenCode is the HTTP code returned for type CreateNewStudentForbidden
const CreateNewStudentForbiddenCode int = 403

/*
CreateNewStudentForbidden Forbidden

swagger:response createNewStudentForbidden
*/
type CreateNewStudentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentForbidden creates CreateNewStudentForbidden with default headers values
func NewCreateNewStudentForbidden() *CreateNewStudentForbidden {

	return &CreateNewStudentForbidden{}
}

// WithPayload adds the payload to the create new student forbidden response
func (o *CreateNewStudentForbidden) WithPayload(payload models.ErrorResponse) *CreateNewStudentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student forbidden response
func (o *CreateNewStudentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewStudentNotFoundCode is the HTTP code returned for type CreateNewStudentNotFound
const CreateNewStudentNotFoundCode int = 404

/*
CreateNewStudentNotFound Not Found

swagger:response createNewStudentNotFound
*/
type CreateNewStudentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentNotFound creates CreateNewStudentNotFound with default headers values
func NewCreateNewStudentNotFound() *CreateNewStudentNotFound {

	return &CreateNewStudentNotFound{}
}

// WithPayload adds the payload to the create new student not found response
func (o *CreateNewStudentNotFound) WithPayload(payload models.ErrorResponse) *CreateNewStudentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student not found response
func (o *CreateNewStudentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewStudentTooManyRequestsCode is the HTTP code returned for type CreateNewStudentTooManyRequests
const CreateNewStudentTooManyRequestsCode int = 429

/*
CreateNewStudentTooManyRequests Too Many Requests

swagger:response createNewStudentTooManyRequests
*/
type CreateNewStudentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentTooManyRequests creates CreateNewStudentTooManyRequests with default headers values
func NewCreateNewStudentTooManyRequests() *CreateNewStudentTooManyRequests {

	return &CreateNewStudentTooManyRequests{}
}

// WithPayload adds the payload to the create new student too many requests response
func (o *CreateNewStudentTooManyRequests) WithPayload(payload models.ErrorResponse) *CreateNewStudentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student too many requests response
func (o *CreateNewStudentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewStudentInternalServerErrorCode is the HTTP code returned for type CreateNewStudentInternalServerError
const CreateNewStudentInternalServerErrorCode int = 500

/*
CreateNewStudentInternalServerError Internal Server Error

swagger:response createNewStudentInternalServerError
*/
type CreateNewStudentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentInternalServerError creates CreateNewStudentInternalServerError with default headers values
func NewCreateNewStudentInternalServerError() *CreateNewStudentInternalServerError {

	return &CreateNewStudentInternalServerError{}
}

// WithPayload adds the payload to the create new student internal server error response
func (o *CreateNewStudentInternalServerError) WithPayload(payload models.ErrorResponse) *CreateNewStudentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student internal server error response
func (o *CreateNewStudentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewStudentServiceUnavailableCode is the HTTP code returned for type CreateNewStudentServiceUnavailable
const CreateNewStudentServiceUnavailableCode int = 503

/*
CreateNewStudentServiceUnavailable Service Unvailable

swagger:response createNewStudentServiceUnavailable
*/
type CreateNewStudentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewStudentServiceUnavailable creates CreateNewStudentServiceUnavailable with default headers values
func NewCreateNewStudentServiceUnavailable() *CreateNewStudentServiceUnavailable {

	return &CreateNewStudentServiceUnavailable{}
}

// WithPayload adds the payload to the create new student service unavailable response
func (o *CreateNewStudentServiceUnavailable) WithPayload(payload models.ErrorResponse) *CreateNewStudentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new student service unavailable response
func (o *CreateNewStudentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewStudentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
