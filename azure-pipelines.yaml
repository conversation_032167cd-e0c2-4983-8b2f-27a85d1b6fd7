trigger:
  - main

variables:
  - group: EddyOwl-DigitalOcean
  - name: configBasePath
    value: /etc/eddyowl

stages:
  - stage: Build
    jobs:
      - job: "BuildAndPush"
        displayName: Build and Push Docker Image
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            fetchDepth: 0
          # Download dev config files for building the image
          - task: DownloadSecureFile@1
            name: DownloadDevSecrets
            inputs:
              secureFile: core_dev.secrets.override.json
          - task: DownloadSecureFile@1
            name: DownloadDevConfig
            inputs:
              secureFile: core_dev.config.override.json
          - script: |
              mkdir -p $(Build.SourcesDirectory)/secrets
              mkdir -p $(Build.SourcesDirectory)/config
              cp $(Agent.TempDirectory)/core_dev.secrets.override.json $(Build.SourcesDirectory)/secrets/secrets.json
              cp $(Agent.TempDirectory)/core_dev.config.override.json $(Build.SourcesDirectory)/config/config.json
            displayName: Copy dev config files for build
          - task: DockerInstaller@0
            inputs:
              dockerVersion: "19.03.12"
          - script: |
              export DOCKER_BUILDKIT=0
              export COMPOSE_DOCKER_CLI_BUILD=0
              echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) 
              docker build -t $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId) .
              docker push $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId)
            displayName: Build and Push Docker Image
  - stage: Dev
    dependsOn: Build
    jobs:
      - deployment: DeployToDev
        displayName: Deployment to Dev Environment
        pool:
          vmImage: "ubuntu-latest"
        environment: Dev
        strategy:
          runOnce:
            deploy:
              steps:
                - task: DownloadSecureFile@1
                  name: DownloadSSHKey
                  inputs:
                    secureFile: $(sshKeyFile)
                - task: DownloadSecureFile@1
                  name: DownloadDevSecrets
                  inputs:
                    secureFile: core_dev.secrets.override.json
                - task: DownloadSecureFile@1
                  name: DownloadDevConfig
                  inputs:
                    secureFile: core_dev.config.override.json
                - script: |
                    mkdir -p ~/.ssh
                    cp $(Agent.TempDirectory)/$(sshKeyFile) ~/.ssh/$(sshKeyFile)
                    chmod 700 ~/.ssh
                    chmod 600 ~/.ssh/$(sshKeyFile)
                    
                    # Setup configuration directory and files on Dev server
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) "
                      sudo mkdir -p $(configBasePath)/dev
                      sudo chown -R $(sshUser):$(sshUser) $(configBasePath)
                    "
                    
                    # Copy config files to server
                    scp -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no \
                      $(Agent.TempDirectory)/core_dev.secrets.override.json \
                      $(sshUser)@$(coreDropletIP):$(configBasePath)/dev/secrets.override.json
                    
                    scp -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no \
                      $(Agent.TempDirectory)/core_dev.config.override.json \
                      $(sshUser)@$(coreDropletIP):$(configBasePath)/dev/config.override.json
                    
                    # Set proper permissions
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) "
                      chmod 600 $(configBasePath)/dev/*.json
                    "
                    
                    # Deploy container
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) '
                      echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) &&
                      docker pull $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId) &&
                      docker stop dev_backend_container || true &&
                      docker rm dev_backend_container || true &&
                      docker run -d --name dev_backend_container \
                        -v $(dockerLogVolumeName):$(dockerLogVolumeMountPath) \
                        -v $(configBasePath)/dev/secrets.override.json:/backend/secrets/secrets.override.json \
                        -v $(configBasePath)/dev/config.override.json:/backend/config/config.override.json \
                        -p 8081:8080 \
                        --add-host host.docker.internal:host-gateway \
                        $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId) &&
                      docker system prune -af
                    '
                  displayName: Setup Config and Deploy to Development
  - stage: Prod
    dependsOn: Build
    jobs:
      - deployment: DeployToProd
        displayName: Deployment to Prod Environment
        pool:
          vmImage: "ubuntu-latest"
        environment: Prod
        strategy:
          runOnce:
            deploy:
              steps:
                - task: DownloadSecureFile@1
                  name: DownloadSSHKey
                  inputs:
                    secureFile: $(sshKeyFile)
                - task: DownloadSecureFile@1
                  name: DownloadProdSecrets
                  inputs:
                    secureFile: core_prod.secrets.override.json
                - task: DownloadSecureFile@1
                  name: DownloadProdConfig
                  inputs:
                    secureFile: core_prod.config.override.json
                - script: |
                    mkdir -p ~/.ssh
                    cp $(Agent.TempDirectory)/$(sshKeyFile) ~/.ssh/$(sshKeyFile)
                    chmod 700 ~/.ssh
                    chmod 600 ~/.ssh/$(sshKeyFile)
                    
                    # Setup configuration directory and files on Prod server
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) "
                      sudo mkdir -p $(configBasePath)/prod
                      sudo chown -R $(sshUser):$(sshUser) $(configBasePath)
                    "
                    
                    # Copy config files to server
                    scp -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no \
                      $(Agent.TempDirectory)/core_prod.secrets.override.json \
                      $(sshUser)@$(coreDropletIP):$(configBasePath)/prod/secrets.override.json
                    
                    scp -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no \
                      $(Agent.TempDirectory)/core_prod.config.override.json \
                      $(sshUser)@$(coreDropletIP):$(configBasePath)/prod/config.override.json
                    
                    # Set proper permissions
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) "
                      chmod 600 $(configBasePath)/prod/*.json
                    "
                    
                    # Deploy container
                    ssh -i ~/.ssh/$(sshKeyFile) -o StrictHostKeyChecking=no $(sshUser)@$(coreDropletIP) '
                      echo $(dockerRegistryPassword) | docker login -u $(dockerRegistryUsername) --password-stdin $(dockerRegistryUrl) &&
                      docker pull $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId) &&
                      docker stop prod_backend_container || true &&
                      docker rm prod_backend_container || true &&
                      docker run -d --name prod_backend_container \
                        -v $(dockerLogVolumeName):$(dockerLogVolumeMountPath) \
                        -v $(configBasePath)/prod/secrets.override.json:/backend/secrets/secrets.override.json \
                        -v $(configBasePath)/prod/config.override.json:/backend/config/config.override.json \
                        -p 8080:8080 \
                        --add-host host.docker.internal:host-gateway \
                        $(dockerRegistryUrl)/$(backendImageName):$(Build.BuildId) &&
                      docker system prune -af
                    '
                  displayName: Setup Config and Deploy to Production
