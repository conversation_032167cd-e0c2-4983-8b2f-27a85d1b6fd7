package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type studentProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Add implements StudentProvider.
func (s *studentProvider) Add(ctx context.Context, student entities.Student) (string, error) {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : Add")
	defer span.End()

	if student.ID == nil {
		return constants.EmptyString, errors.New("student ID is required")
	}

	_, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBCollectionStudents).
		InsertOne(ctx, student)
	if err != nil {
		log.Error().Msg(err.<PERSON>rror())
		return constants.EmptyString, err
	}
	return *student.ID, nil
}

// Delete implements StudentProvider.
func (s *studentProvider) Delete(ctx context.Context, instituteId string, studentId string, deletedBy string) error {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : Delete")
	defer span.End()
	student, err := s.Get(ctx, instituteId, studentId)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	currentTime := time.Now()
	student.DeletedAt = &currentTime
	student.DeletedBy = &deletedBy
	_, err = s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionStudentsArchive).InsertOne(ctx, student)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	res, err := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionStudents).DeleteOne(ctx, bson.D{{"student_id", studentId}})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.DeletedCount != 1 {
		log.Error().Msg("Student not found : " + studentId)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Edit implements StudentProvider.
func (s *studentProvider) Edit(ctx context.Context, instituteId string, studentId string, student *entities.Student) error {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : Edit")
	defer span.End()
	now := time.Now()
	student.UpdatedAt = &now
	res, err := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionStudents).UpdateOne(ctx, bson.D{{"student_id", studentId}, {"institute_id", instituteId}}, bson.M{"$set": student})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.MatchedCount != 1 {
		log.Error().Msg("Student not found : " + studentId)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Get implements StudentProvider.
func (s *studentProvider) Get(ctx context.Context, instituteId string, studentId string) (*entities.Student, error) {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : Get")
	defer span.End()
	student := &entities.Student{}
	filter := bson.D{
		{"student_id", studentId},
		{"institute_id", instituteId}}
	err := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionStudents).FindOne(ctx, filter).Decode(student)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return student, nil
}

// GetAll implements StudentProvider.
func (s *studentProvider) GetAll(ctx context.Context, instituteId, email *string, grade *int, section *[]string, termId *string) (*[]entities.Student, error) {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : GetAll")
	defer span.End()
	studentList := make([]entities.Student, 0)
	filter := bson.D{}
	if instituteId != nil {
		filter = append(filter, bson.E{Key: "institute_id", Value: *instituteId})
	}
	if termId != nil {
		filter = append(filter, bson.E{Key: "academic_history." + *termId, Value: bson.M{"$exists": true}})
	}
	if email != nil {
		filter = append(filter, bson.E{Key: "email", Value: *email})
	}
	if grade != nil {
		filter = append(filter, bson.E{Key: "academic_history." + *termId + ".grade", Value: *grade})
	}
	if section != nil && len(*section) > 0 {
		filter = append(filter, bson.E{Key: "academic_history." + *termId + ".section", Value: bson.M{"$in": *section}})
	}
	if termId != nil {
		filter = append(filter, bson.E{Key: "academic_history." + *termId, Value: bson.M{"$exists": true}})
	}

	cursor, err := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionStudents).Find(ctx, filter)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	err = cursor.All(ctx, &studentList)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return &studentList, nil
}

// GetByEmail implements StudentProvider.
func (s *studentProvider) GetByEmail(ctx context.Context, instituteId, email string) (*entities.Student, error) {
	ctx, span := s.tracer.Start(ctx, "StudentProvider : GetByEmail")
	defer span.End()

	student := &entities.Student{}
	filter := bson.D{
		{"institute_id", instituteId},
		{"email", email},
	}

	err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBCollectionStudents).
		FindOne(ctx, filter).
		Decode(student)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	return student, nil
}

func NewStudentProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) StudentProvider {
	return &studentProvider{
		mongoClient: mongoClient,
		dbName:      databaseName,
		tracer:      tracer,
	}
}
