package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

// getAllAssgetAllSubmissionImplignmentImpl implements the GetAllSubmissionsHandler interface.
type getAllSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	instituteProvider  data_providers.InstituteProvider
	termProvider       data_providers.TermProvider
	userRolesProvider  data_providers.UserRolesProvider
	studentProvider    data_providers.StudentProvider
	tracer             trace.Tracer
}

// NewGetAllSubmissionHandler returns a handler for fetching all submissions.
func NewGetAllSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	termProvider data_providers.TermProvider,
	studentProvider data_providers.StudentProvider,
	tracer trace.Tracer,
) submission.GetAllSubmissionsHandler {
	return &getAllSubmissionImpl{
		submissionProvider: submissionProvider,
		instituteProvider:  instituteProvider,
		termProvider:       termProvider,
		userRolesProvider:  userRolesProvider,
		studentProvider:    studentProvider,
		tracer:             tracer,
	}
}

func (impl *getAllSubmissionImpl) Handle(params submission.GetAllSubmissionsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAllSubmissionsHandler")
	defer span.End()

	principalEmail := principal.(string)
	isStudent := false

	// First check if user has admin/instructor role
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principalEmail, params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole})

	// If user is not admin/instructor and studentID is provided, verify if the request is for their own submissions
	if err != nil && params.StudentID != nil {
		// Check if user has student role
		err = utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principalEmail, params.InstituteID,
			[]int{constants.StudentRole})
		if err != nil {
			log.Error().Err(err).Msg("Failed to check user roles")
			return submission.NewGetAllSubmissionsForbidden().WithPayload("Unauthorized")
		}
		isStudent = true

		// Get student by email to verify studentID
		student, err := impl.studentProvider.GetByEmail(ctx, params.InstituteID, principalEmail)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get student by email")
			return submission.NewGetAllSubmissionsForbidden().WithPayload("Unauthorized")
		}

		// Verify that the requested studentID matches the authenticated student
		if student == nil || student.StudentID == nil || *student.StudentID != *params.StudentID {
			log.Error().Msg("Student attempting to access another student's submissions")
			return submission.NewGetAllSubmissionsForbidden().WithPayload("Unauthorized")
		}
	} else if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewGetAllSubmissionsForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.getAllSubmissionValidator(ctx, params)
	if !valid {
		return validateResp
	}

	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, params.TermID)
	if err != nil {
		if errors.Is(err, utils.ErrTermNotFound) {
			return submission.NewGetAllSubmissionsBadRequest().WithPayload("Term not found")
		}
		return submission.NewGetAllSubmissionsInternalServerError().WithPayload("Unable to resolve term")
	}

	submissionList, err := impl.submissionProvider.GetAll(ctx, params.InstituteID, params.AssignmentID, params.Grade, &params.Section, params.StudentID, &termID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch submissions")
		return submission.NewGetAllSubmissionsInternalServerError().WithPayload("Unable to fetch submissions")
	}

	var response []*models.Submission
	for _, submission := range *submissionList {
		submissionEntity := &models.Submission{}
		if submission.Grade != nil {
			submissionEntity.Class = int32(*submission.Grade)
		}
		if submission.TotalAchievedScore != nil {
			submissionEntity.StudentScore = float32(*submission.TotalAchievedScore)
		}
		if submission.TotalScore != nil {
			submissionEntity.AssignmentScore = *submission.TotalScore
		}
		if submission.AssignmentName != nil {
			submissionEntity.AssignmentName = *submission.AssignmentName
		}
		if submission.AssignmentID != nil {
			submissionEntity.AssignmentID = *submission.AssignmentID
		}
		if submission.Subject != nil {
			submissionEntity.Subject = *submission.Subject
		}
		if submission.StudentSection != nil {
			submissionEntity.Section = *submission.StudentSection
		}
		if submission.History != nil && len(*submission.History) > 0 {
			status := (*submission.History)[len(*submission.History)-1]
			submissionEntity.Status = constants.StatusMap[status.Status]
		} else {
			submissionEntity.Status = constants.StatusMap[constants.SubmissionStatusDue]
		}
		if submission.StudentID != nil {
			submissionEntity.StudentID = *submission.StudentID
		}
		if submission.StudentRollNumber != nil {
			submissionEntity.StudentRollNumber = *submission.StudentRollNumber
		}
		if submission.StudentFirstName != nil && submission.StudentLastName != nil {
			submissionEntity.StudentName = *submission.StudentFirstName + " " + *submission.StudentLastName
		}
		response = append(response, submissionEntity)
	}

	// Modify response for student role
	if isStudent {
		for _, sub := range response {
			switch sub.Status {
			case "graded":
				sub.Status = "processing"
				sub.StudentScore = 0
			case "published":
				sub.Status = "graded"
			}
		}
	}

	return submission.NewGetAllSubmissionsOK().WithPayload(response)
}

func (impl *getAllSubmissionImpl) getAllSubmissionValidator(ctx context.Context, params submission.GetAllSubmissionsParams) (bool, middleware.Responder) {
	if params.InstituteID == "" {
		log.Error().Msg("Invalid institute ID")
		return false, submission.NewGetAllSubmissionsBadRequest().WithPayload("Invalid institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, submission.NewGetAllSubmissionsBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, submission.NewGetAllSubmissionsInternalServerError().WithPayload("Unable to fetch Institute")
	}
	return true, nil
}
