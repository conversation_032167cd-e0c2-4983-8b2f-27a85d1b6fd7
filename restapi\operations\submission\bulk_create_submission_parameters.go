// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"io"
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/validate"

	"eddyowl-backend/models"
)

// NewBulkCreateSubmissionParams creates a new BulkCreateSubmissionParams object
//
// There are no default values defined in the spec.
func NewBulkCreateSubmissionParams() BulkCreateSubmissionParams {

	return BulkCreateSubmissionParams{}
}

// BulkCreateSubmissionParams contains all the bound params for the bulk create submission operation
// typically these are obtained from a http.Request
//
// swagger:parameters BulkCreateSubmission
type BulkCreateSubmissionParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*ID of the assignment
	  Required: true
	  In: path
	*/
	AssignmentID string
	/*ID of the institute
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  Required: true
	  In: body
	*/
	Submissions *models.BulkCreateSubmissionBody
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewBulkCreateSubmissionParams() beforehand.
func (o *BulkCreateSubmissionParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rAssignmentID, rhkAssignmentID, _ := route.Params.GetOK("assignmentId")
	if err := o.bindAssignmentID(rAssignmentID, rhkAssignmentID, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	if runtime.HasBody(r) {
		defer r.Body.Close()
		var body models.BulkCreateSubmissionBody
		if err := route.Consumer.Consume(r.Body, &body); err != nil {
			if err == io.EOF {
				res = append(res, errors.Required("submissions", "body", ""))
			} else {
				res = append(res, errors.NewParseError("submissions", "body", "", err))
			}
		} else {
			// validate body object
			if err := body.Validate(route.Formats); err != nil {
				res = append(res, err)
			}

			ctx := validate.WithOperationRequest(r.Context())
			if err := body.ContextValidate(ctx, route.Formats); err != nil {
				res = append(res, err)
			}

			if len(res) == 0 {
				o.Submissions = &body
			}
		}
	} else {
		res = append(res, errors.Required("submissions", "body", ""))
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindAssignmentID binds and validates parameter AssignmentID from path.
func (o *BulkCreateSubmissionParams) bindAssignmentID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.AssignmentID = raw

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *BulkCreateSubmissionParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}
