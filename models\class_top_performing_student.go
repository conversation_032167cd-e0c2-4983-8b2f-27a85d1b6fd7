// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassTopPerformingStudent class top performing student
//
// swagger:model ClassTopPerformingStudent
type ClassTopPerformingStudent struct {

	// highest score
	HighestScore float32 `json:"highestScore,omitempty"`

	// lowest score
	LowestScore float32 `json:"lowestScore,omitempty"`

	// overall performance
	OverallPerformance float32 `json:"overallPerformance,omitempty"`

	// student
	Student string `json:"student,omitempty"`
}

// Validate validates this class top performing student
func (m *ClassTopPerformingStudent) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class top performing student based on context it is used
func (m *ClassTopPerformingStudent) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassTopPerformingStudent) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassTopPerformingStudent) UnmarshalBinary(b []byte) error {
	var res ClassTopPerformingStudent
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
