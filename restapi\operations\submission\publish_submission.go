// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// PublishSubmissionHandlerFunc turns a function with the right signature into a publish submission handler
type PublishSubmissionHandlerFunc func(PublishSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn PublishSubmissionHandlerFunc) Handle(params PublishSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// PublishSubmissionHandler interface for that can handle valid publish submission params
type PublishSubmissionHandler interface {
	Handle(PublishSubmissionParams, interface{}) middleware.Responder
}

// NewPublishSubmission creates a new http.Handler for the publish submission operation
func NewPublishSubmission(ctx *middleware.Context, handler PublishSubmissionHandler) *PublishSubmission {
	return &PublishSubmission{Context: ctx, Handler: handler}
}

/*
	PublishSubmission swagger:route PUT /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/publish submission publishSubmission

# Publish student submission

Publish submission
*/
type PublishSubmission struct {
	Context *middleware.Context
	Handler PublishSubmissionHandler
}

func (o *PublishSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewPublishSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
