// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// CreateInstituteHandlerFunc turns a function with the right signature into a create institute handler
type CreateInstituteHandlerFunc func(CreateInstituteParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn CreateInstituteHandlerFunc) Handle(params CreateInstituteParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateInstituteHandler interface for that can handle valid create institute params
type CreateInstituteHandler interface {
	Handle(CreateInstituteParams, interface{}) middleware.Responder
}

// NewCreateInstitute creates a new http.Handler for the create institute operation
func NewCreateInstitute(ctx *middleware.Context, handler CreateInstituteHand<PERSON>) *CreateInstitute {
	return &CreateInstitute{Context: ctx, Handler: handler}
}

/*
	CreateInstitute swagger:route POST /institute institute createInstitute

# Create a new institute

Insterts new institute
*/
type CreateInstitute struct {
	Context *middleware.Context
	Handler CreateInstituteHandler
}

func (o *CreateInstitute) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateInstituteParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
