package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassAssessmentsBySubjectMonthlyImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassAssessmentsBySubjectMonthlyHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassAssessmentsBySubjectMonthlyHandler {
	return &getClassAssessmentsBySubjectMonthlyImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassAssessmentsBySubjectMonthlyImpl) Handle(params stats.GetClassAssessmentsBySubjectMonthlyParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassAssessmentsBySubjectMonthlyHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassAssessmentsBySubjectMonthlyInternalServerError().WithPayload("Unable to resolve term")
	}

	monthRangeRequired := 12

	// Get current date
	now := time.Now().UTC()

	// Calculate the start date (6 months ago from the start of current month)
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).
		AddDate(0, 1-monthRangeRequired, 0)

	months := make([]string, monthRangeRequired)
	subjectData := make(map[string][]int32)

	// Initialize the months array
	for i := 0; i < monthRangeRequired; i++ {
		targetDate := startDate.AddDate(0, i, 0)
		months[i] = targetDate.Format("Jan 2006") // Format as "MMM YYYY"
	}

	// Iterate through monthRangeRequired months
	for i := 0; i < monthRangeRequired; i++ {
		// Calculate target month
		targetDate := startDate.AddDate(0, i, 0)
		monthStr := targetDate.Format("2006-01") // Format as YYYY-MM

		// Get submissions count for the month
		results, err := impl.statsProvider.GetGradeMonthlySubmissionsCount(ctx, params.InstituteID, termID, params.Class, monthStr, nil)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get monthly submissions count")
			continue
		}

		// Process each result and organize by subject
		for _, result := range *results {
			subject := result.ID.Subject
			if _, exists := subjectData[subject]; !exists {
				subjectData[subject] = make([]int32, monthRangeRequired)
			}
			subjectData[subject][i] = result.SubmissionCount
		}
	}

	// Convert map to response format
	data := make([]*models.ClassMonthlyAssessmentsBySubject, 0, len(subjectData))
	for subject, assessments := range subjectData {
		data = append(data, &models.ClassMonthlyAssessmentsBySubject{
			Subjects:    subject,
			Assessments: assessments,
		})
	}

	response := &models.ClassAggMonthlyAssessmentsBySubject{
		Months: months,
		Data:   data,
	}

	return stats.NewGetClassAssessmentsBySubjectMonthlyOK().WithPayload(response)
}
