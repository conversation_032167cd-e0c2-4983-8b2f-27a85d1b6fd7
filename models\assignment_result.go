// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// AssignmentResult assignment result
//
// swagger:model AssignmentResult
type AssignmentResult struct {

	// student response list
	StudentResponseList []*StudentResponse `json:"StudentResponseList"`

	// acheived score
	// Example: 75.5
	AcheivedScore float32 `json:"acheivedScore,omitempty"`

	// image Id list
	ImageIDList []string `json:"imageIdList"`

	// missed question numbers
	MissedQuestionNumbers []int32 `json:"missedQuestionNumbers"`

	// subject
	Subject string `json:"subject,omitempty"`
}

// Validate validates this assignment result
func (m *AssignmentResult) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateStudentResponseList(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *AssignmentResult) validateStudentResponseList(formats strfmt.Registry) error {
	if swag.IsZero(m.StudentResponseList) { // not required
		return nil
	}

	for i := 0; i < len(m.StudentResponseList); i++ {
		if swag.IsZero(m.StudentResponseList[i]) { // not required
			continue
		}

		if m.StudentResponseList[i] != nil {
			if err := m.StudentResponseList[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("StudentResponseList" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("StudentResponseList" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this assignment result based on the context it is used
func (m *AssignmentResult) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateStudentResponseList(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *AssignmentResult) contextValidateStudentResponseList(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.StudentResponseList); i++ {

		if m.StudentResponseList[i] != nil {

			if swag.IsZero(m.StudentResponseList[i]) { // not required
				return nil
			}

			if err := m.StudentResponseList[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("StudentResponseList" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("StudentResponseList" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *AssignmentResult) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *AssignmentResult) UnmarshalBinary(b []byte) error {
	var res AssignmentResult
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
