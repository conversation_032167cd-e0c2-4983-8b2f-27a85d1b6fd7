// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetWeeklyAssessmentsHandlerFunc turns a function with the right signature into a get weekly assessments handler
type GetWeeklyAssessmentsHandlerFunc func(GetWeeklyAssessmentsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetWeeklyAssessmentsHandlerFunc) Handle(params GetWeeklyAssessmentsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetWeeklyAssessmentsHandler interface for that can handle valid get weekly assessments params
type GetWeeklyAssessmentsHandler interface {
	Handle(GetWeeklyAssessmentsParams, interface{}) middleware.Responder
}

// NewGetWeeklyAssessments creates a new http.Handler for the get weekly assessments operation
func NewGetWeeklyAssessments(ctx *middleware.Context, handler GetWeeklyAssessmentsHand<PERSON>) *GetWeeklyAssessments {
	return &GetWeeklyAssessments{Context: ctx, Handler: handler}
}

/*
	GetWeeklyAssessments swagger:route GET /institute/{instituteId}/assessments/weekly stats getWeeklyAssessments

# Get weekly assessments

Get all the assessments graded last week for each day Mon to Fri
*/
type GetWeeklyAssessments struct {
	Context *middleware.Context
	Handler GetWeeklyAssessmentsHandler
}

func (o *GetWeeklyAssessments) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetWeeklyAssessmentsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
