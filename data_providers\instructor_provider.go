package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

// InstructorProvider defines the interface for instructor data operations.
type InstructorProvider interface {
	// Add creates a new instructor.
	Add(ctx context.Context, instructor *entities.Instructor) (string, error)
	// Get retrieves an instructor by ID.
	Get(ctx context.Context, id string) (*entities.Instructor, error)
	// GetByEmailAndInstitute retrieves an instructor by email and institute ID.
	GetByEmailAndInstitute(ctx context.Context, email string, instituteId string) (*entities.Instructor, error)
	// Edit updates an existing instructor.
	Edit(ctx context.Context, id string, instructor *entities.Instructor) error
	// Delete deletes an instructor by ID.
	Delete(ctx context.Context, id string, deletedBy string) error
	// GetAll retrieves a list of instructors.
	GetAll(ctx context.Context, instituteId *string, email *string) (*[]entities.Instructor, error)
}
