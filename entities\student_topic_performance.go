package entities

// StudentTopicPerformance represents a student's performance across topics for an assignment
type StudentTopicPerformance struct {
	AssignmentID       *string              `json:"assignment_id" bson:"assignment_id"`
	TermID             *string              `json:"term_id" bson:"term_id"`
	StudentID          *string              `json:"student_id" bson:"student_id"`
	Subject            *string              `json:"subject" bson:"subject"`
	ChapterPerformance []ChapterPerformance `json:"chapter_performance" bson:"chapter_performance"`
}

// ChapterPerformance represents performance data for a specific chapter
type ChapterPerformance struct {
	Name             string       `json:"name" bson:"name"`
	TopicPerformance []TopicScore `json:"topic_performance" bson:"topic_performance"`
	Score            float64      `json:"score" bson:"score"`
}

// TopicScore represents a topic and its associated score
type TopicScore struct {
	Name  string  `json:"name" bson:"name"`
	Score float64 `json:"score" bson:"score"`
}
