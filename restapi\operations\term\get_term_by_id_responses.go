// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetTermByIDOKCode is the HTTP code returned for type GetTermByIDOK
const GetTermByIDOKCode int = 200

/*
GetTermByIDOK Successful operation

swagger:response getTermByIdOK
*/
type GetTermByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.Term `json:"body,omitempty"`
}

// NewGetTermByIDOK creates GetTermByIDOK with default headers values
func NewGetTermByIDOK() *GetTermByIDOK {

	return &GetTermByIDOK{}
}

// WithPayload adds the payload to the get term by Id o k response
func (o *GetTermByIDOK) WithPayload(payload *models.Term) *GetTermByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id o k response
func (o *GetTermByIDOK) SetPayload(payload *models.Term) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetTermByIDBadRequestCode is the HTTP code returned for type GetTermByIDBadRequest
const GetTermByIDBadRequestCode int = 400

/*
GetTermByIDBadRequest Bad Request

swagger:response getTermByIdBadRequest
*/
type GetTermByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDBadRequest creates GetTermByIDBadRequest with default headers values
func NewGetTermByIDBadRequest() *GetTermByIDBadRequest {

	return &GetTermByIDBadRequest{}
}

// WithPayload adds the payload to the get term by Id bad request response
func (o *GetTermByIDBadRequest) WithPayload(payload models.ErrorResponse) *GetTermByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id bad request response
func (o *GetTermByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetTermByIDForbiddenCode is the HTTP code returned for type GetTermByIDForbidden
const GetTermByIDForbiddenCode int = 403

/*
GetTermByIDForbidden Forbidden

swagger:response getTermByIdForbidden
*/
type GetTermByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDForbidden creates GetTermByIDForbidden with default headers values
func NewGetTermByIDForbidden() *GetTermByIDForbidden {

	return &GetTermByIDForbidden{}
}

// WithPayload adds the payload to the get term by Id forbidden response
func (o *GetTermByIDForbidden) WithPayload(payload models.ErrorResponse) *GetTermByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id forbidden response
func (o *GetTermByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetTermByIDNotFoundCode is the HTTP code returned for type GetTermByIDNotFound
const GetTermByIDNotFoundCode int = 404

/*
GetTermByIDNotFound Not Found

swagger:response getTermByIdNotFound
*/
type GetTermByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDNotFound creates GetTermByIDNotFound with default headers values
func NewGetTermByIDNotFound() *GetTermByIDNotFound {

	return &GetTermByIDNotFound{}
}

// WithPayload adds the payload to the get term by Id not found response
func (o *GetTermByIDNotFound) WithPayload(payload models.ErrorResponse) *GetTermByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id not found response
func (o *GetTermByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetTermByIDTooManyRequestsCode is the HTTP code returned for type GetTermByIDTooManyRequests
const GetTermByIDTooManyRequestsCode int = 429

/*
GetTermByIDTooManyRequests Too Many Requests

swagger:response getTermByIdTooManyRequests
*/
type GetTermByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDTooManyRequests creates GetTermByIDTooManyRequests with default headers values
func NewGetTermByIDTooManyRequests() *GetTermByIDTooManyRequests {

	return &GetTermByIDTooManyRequests{}
}

// WithPayload adds the payload to the get term by Id too many requests response
func (o *GetTermByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *GetTermByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id too many requests response
func (o *GetTermByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetTermByIDInternalServerErrorCode is the HTTP code returned for type GetTermByIDInternalServerError
const GetTermByIDInternalServerErrorCode int = 500

/*
GetTermByIDInternalServerError Internal Server Error

swagger:response getTermByIdInternalServerError
*/
type GetTermByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDInternalServerError creates GetTermByIDInternalServerError with default headers values
func NewGetTermByIDInternalServerError() *GetTermByIDInternalServerError {

	return &GetTermByIDInternalServerError{}
}

// WithPayload adds the payload to the get term by Id internal server error response
func (o *GetTermByIDInternalServerError) WithPayload(payload models.ErrorResponse) *GetTermByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id internal server error response
func (o *GetTermByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetTermByIDServiceUnavailableCode is the HTTP code returned for type GetTermByIDServiceUnavailable
const GetTermByIDServiceUnavailableCode int = 503

/*
GetTermByIDServiceUnavailable Service Unvailable

swagger:response getTermByIdServiceUnavailable
*/
type GetTermByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetTermByIDServiceUnavailable creates GetTermByIDServiceUnavailable with default headers values
func NewGetTermByIDServiceUnavailable() *GetTermByIDServiceUnavailable {

	return &GetTermByIDServiceUnavailable{}
}

// WithPayload adds the payload to the get term by Id service unavailable response
func (o *GetTermByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetTermByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get term by Id service unavailable response
func (o *GetTermByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetTermByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
