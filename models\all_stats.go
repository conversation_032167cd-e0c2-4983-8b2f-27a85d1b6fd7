// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// AllStats all stats
//
// swagger:model AllStats
type AllStats struct {

	// assessments
	Assessments int32 `json:"assessments,omitempty"`

	// instructors
	Instructors int32 `json:"instructors,omitempty"`

	// overall student performance
	OverallStudentPerformance int32 `json:"overallStudentPerformance,omitempty"`

	// students
	Students int32 `json:"students,omitempty"`
}

// Validate validates this all stats
func (m *AllStats) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this all stats based on context it is used
func (m *AllStats) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *AllStats) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *AllStats) UnmarshalBinary(b []byte) error {
	var res AllStats
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
