// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetStudentByEmailHandlerFunc turns a function with the right signature into a get student by email handler
type GetStudentByEmailHandlerFunc func(GetStudentByEmailParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetStudentByEmailHandlerFunc) Handle(params GetStudentByEmailParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetStudentByEmailHandler interface for that can handle valid get student by email params
type GetStudentByEmailHandler interface {
	Handle(GetStudentByEmailParams, interface{}) middleware.Responder
}

// NewGetStudentByEmail creates a new http.Handler for the get student by email operation
func NewGetStudentByEmail(ctx *middleware.Context, handler GetStudentByEmail<PERSON><PERSON><PERSON>) *GetStudentByEmail {
	return &GetStudentByEmail{Context: ctx, Handler: handler}
}

/*
	GetStudentByEmail swagger:route GET /institute/{instituteId}/student/email/{email} student getStudentByEmail

# Get student by email

Get student by email address
*/
type GetStudentByEmail struct {
	Context *middleware.Context
	Handler GetStudentByEmailHandler
}

func (o *GetStudentByEmail) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetStudentByEmailParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
