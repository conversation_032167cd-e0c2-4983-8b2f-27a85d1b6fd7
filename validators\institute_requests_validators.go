package validators

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/restapi/operations/institute"
)

func CreateInstituteValidator(params institute.CreateInstituteParams) bool {
	if params.NewInstitute.Name == constants.EmptyString || params.NewInstitute.Program == constants.EmptyString {
		return false
	}
	if params.NewInstitute.Address == nil || params.NewInstitute.Address.AddressOne == constants.EmptyString || params.NewInstitute.Address.City == constants.EmptyString || params.NewInstitute.Address.Pincode == constants.EmptyString || params.NewInstitute.Address.State == constants.EmptyString {
		return false
	}
	return true
}

func EditInstituteValidator(params institute.EditInstituteParams) bool {
	if params.Institute.Name == constants.EmptyString {
		return false
	}
	if params.Institute.Address == nil || params.Institute.Address.AddressOne == constants.EmptyString || params.Institute.Address.City == constants.EmptyString || params.Institute.Address.Pincode == constants.EmptyString || params.Institute.Address.State == constants.EmptyString {
		return false
	}
	return true
}
