// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// TopicStats topic stats
//
// swagger:model TopicStats
type TopicStats struct {

	// score
	Score int32 `json:"score,omitempty"`

	// topic
	Topic string `json:"topic,omitempty"`
}

// Validate validates this topic stats
func (m *TopicStats) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this topic stats based on context it is used
func (m *TopicStats) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *TopicStats) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *TopicStats) UnmarshalBinary(b []byte) error {
	var res TopicStats
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
