package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type StatsProvider interface {
	// Daily submissions
	GetDailySubmissionsCount(ctx context.Context, instituteID, termID string, date string) (*entities.DailySubmissionsCountView, error)
	GetAllDailySubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.DailySubmissionsCountView, error)

	// Monthly submissions
	GetMonthlySubmissionsCount(ctx context.Context, instituteID, termID string, date string) (*entities.MonthlySubmissionsCountView, error)
	GetAllMonthlySubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.MonthlySubmissionsCountView, error)

	// Grade submissions
	GetGradeSubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, date string) (*entities.GradeSubmissionsCountView, error)
	GetAllGradeSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubmissionsCountView, error)

	// Subject submissions
	GetSubjectSubmissionsCount(ctx context.Context, instituteID, termID string, subject string) (*entities.SubjectSubmissionsCountView, error)
	GetAllSubjectSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.SubjectSubmissionsCountView, error)

	// Grade and subject submissions
	GetGradeSubjectSubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, subject *string) (*[]entities.GradeSubjectSubmissionDataView, error)
	GetAllGradeSubjectSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubjectSubmissionsCountView, error)

	// Student performance
	GetStudentOverallPerformance(ctx context.Context, instituteID, termID, studentID string) (*entities.StudentOverallPercentageView, error)
	GetAllStudentOverallPerformance(ctx context.Context, instituteID, termID string, grade int32) (*[]entities.StudentOverallPercentageView, error)

	// Grade subject data
	GetGradeSubjectData(ctx context.Context, instituteID, termID string, grade int32, subject string) (*entities.GradeSubjectSubmissionDataView, error)
	GetAllGradeSubjectData(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubjectSubmissionDataView, error)

	// Grade overall stats
	GetGradeOverallStats(ctx context.Context, instituteID, termID string, grade int32) (*entities.GradeOverallStats, error)
	GetAllGradeOverallStats(ctx context.Context, instituteID, termID string) (*[]entities.GradeOverallStats, error)

	// Section submission count
	GetSectionData(ctx context.Context, instituteID, termID string, grade int32, section string) (*entities.SectionDataView, error)
	GetAllSectionData(ctx context.Context, instituteID, termID string, grade int32) (*[]entities.SectionDataView, error)

	// Institute overall stats
	GetInstituteOverallStats(ctx context.Context, instituteID, termID string) (*entities.InstituteOverallStatsView, error)

	GetGradeMonthlySubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, date string, subject *string) (*[]entities.GradeMonthlySubmissionsCountView, error)

	// Student Topic Performance
	GetStudentTopicPerformance(ctx context.Context, assignmentID, termID, studentID string) (*entities.StudentTopicPerformance, error)
	GetAllStudentTopicPerformance(ctx context.Context, termID string, studentID string) (*[]entities.StudentTopicPerformance, error)
}
