// Create Collections
db.createCollection("institutes");
db.createCollection("institutes_archive");
db.createCollection("users");
db.createCollection("users_archive");
db.createCollection("instructors");
db.createCollection("instructors_archive");
db.createCollection("terms");
db.createCollection("terms_archive");
db.createCollection("students");
db.createCollection("students_archive");
db.createCollection("assignments");
db.createCollection("assignments_archive");
db.createCollection("submissions");
db.createCollection("submissions_archive");

db.institutes.createIndex({ _id: 1 });
db.institutes.createIndex({ name: 1 });
db.institutes.createIndex({ created_at: 1 });
db.institutes.createIndex({ deleted_at: 1 });

// Create Indexes for users
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ created_at: 1 });
db.users.createIndex({ status: 1 });

// Create Indexes for instructors
db.instructors.createIndex({ institute_id: 1 });
db.instructors.createIndex({ email: 1 });
db.instructors.createIndex({ created_at: 1 });
db.instructors.createIndex({ status: 1 });
db.instructors.createIndex({ institute_id: 1, email: 1 }, { unique: true });

// Create Indexes for terms
db.terms.createIndex({ institute_id: 1 });
db.terms.createIndex({ _id: 1 });
db.terms.createIndex({ created_at: 1 });
db.terms.createIndex({ status: 1 });

// Create Indexes for students
db.students.createIndex({ institute_id: 1 });
db.students.createIndex({ email: 1 });
db.students.createIndex({ roll_number: 1 });
db.students.createIndex({ created_at: 1 });
db.students.createIndex({ status: 1 });
db.students.createIndex(
  { institute_id: 1, email: 1 },
  {
    unique: true,
    partialFilterExpression: {
      email: { $exists: true, $type: "string" },
    },
  }
);
db.students.createIndex({ institute_id: 1, student_id: 1 }, { unique: true });
db.students.createIndex({ academic_history: 1 });

// Create Indexes for assignments
db.assignments.createIndex({ institute_id: 1 });
db.assignments.createIndex({ term_id: 1 });
db.assignments.createIndex({ grade: 1 });
db.assignments.createIndex({ subject: 1 });
db.assignments.createIndex({ sections: 1 });
db.assignments.createIndex({ created_at: 1 });
db.assignments.createIndex({ status: 1 });
db.assignments.createIndex({
  institute_id: 1,
  term_id: 1,
  grade: 1,
  subject: 1,
});

// Create Indexes for submissions
db.submissions.createIndex({ institute_id: 1 });
db.submissions.createIndex({ assignment_id: 1 });
db.submissions.createIndex({ student_id: 1 });
db.submissions.createIndex({ created_at: 1 });
db.submissions.createIndex({ status: 1 });
db.submissions.createIndex(
  {
    institute_id: 1,
    assignment_id: 1,
    student_id: 1,
  },
  { unique: true }
);

// Create Indexes for archive collections
// institutes_archive
db.institutes_archive.createIndex({ original_id: 1 });
db.institutes_archive.createIndex({ archived_at: 1 });

// users_archive
db.users_archive.createIndex({ original_id: 1 });
db.users_archive.createIndex({ archived_at: 1 });

// instructors_archive
db.instructors_archive.createIndex({ original_id: 1 });
db.instructors_archive.createIndex({ archived_at: 1 });

// terms_archive
db.terms_archive.createIndex({ original_id: 1 });
db.terms_archive.createIndex({ archived_at: 1 });

// students_archive
db.students_archive.createIndex({ original_id: 1 });
db.students_archive.createIndex({ archived_at: 1 });

// assignments_archive
db.assignments_archive.createIndex({ original_id: 1 });
db.assignments_archive.createIndex({ archived_at: 1 });

// submissions_archive
db.submissions_archive.createIndex({ original_id: 1 });
db.submissions_archive.createIndex({ archived_at: 1 });

// Create Indexes for daily_submissions_count_view
db.daily_submissions_count_view.createIndex({ "_id.institute_id": 1 });
db.daily_submissions_count_view.createIndex({ "_id.term_id": 1 });
db.daily_submissions_count_view.createIndex({ "_id.date": 1 });

// Create Indexes for monthly_submissions_count_view
db.monthly_submissions_count_view.createIndex({ "_id.institute_id": 1 });
db.monthly_submissions_count_view.createIndex({ "_id.term_id": 1 });
db.monthly_submissions_count_view.createIndex({ "_id.date": 1 });

// Create Indexes for grade_submissions_count_view
db.grade_submissions_count_view.createIndex({ "_id.institute_id": 1 });
db.grade_submissions_count_view.createIndex({ "_id.term_id": 1 });
db.grade_submissions_count_view.createIndex({ "_id.grade": 1 });
db.grade_submissions_count_view.createIndex({ "_id.date": 1 });

// Create Indexes for subject_submissions_count_view
db.subject_submissions_count_view.createIndex({ "_id.institute_id": 1 });
db.subject_submissions_count_view.createIndex({ "_id.term_id": 1 });
db.subject_submissions_count_view.createIndex({ "_id.subject": 1 });

// Create Indexes for student_overall_percentage_view
db.student_overall_percentage_view.createIndex({ institute_id: 1 });
db.student_overall_percentage_view.createIndex({ term_id: 1 });
db.student_overall_percentage_view.createIndex({ student_id: 1 });

// Create Indexes for grade_subject_submission_data_view
db.grade_subject_submission_data_view.createIndex({ institute_id: 1 });
db.grade_subject_submission_data_view.createIndex({ term_id: 1 });
db.grade_subject_submission_data_view.createIndex({ grade: 1 });
db.grade_subject_submission_data_view.createIndex({ subject: 1 });

// Create Indexes for grade_overall_stats
db.grade_overall_stats.createIndex({ institute_id: 1 });
db.grade_overall_stats.createIndex({ term_id: 1 });
db.grade_overall_stats.createIndex({ grade: 1 });

// Create Indexes for monthly_grade_subject_submissions_count_view
db.monthly_grade_subject_submissions_count_view.createIndex({
  "_id.institute_id": 1,
});
db.monthly_grade_subject_submissions_count_view.createIndex({
  "_id.term_id": 1,
});
db.monthly_grade_subject_submissions_count_view.createIndex({ "_id.grade": 1 });
db.monthly_grade_subject_submissions_count_view.createIndex({
  "_id.subject": 1,
});
db.monthly_grade_subject_submissions_count_view.createIndex({ "_id.date": 1 });

// Create Indexes for section_data_view
db.section_data_view.createIndex({ "_id.institute_id": 1, "_id.term_id": 1 }); // Compound index for common query pattern
db.section_data_view.createIndex({
  "_id.institute_id": 1,
  "_id.term_id": 1,
  "_id.grade": 1,
}); // For grade-specific queries
db.section_data_view.createIndex({
  "_id.institute_id": 1,
  "_id.term_id": 1,
  "_id.section": 1,
}); // For section-specific queries

// Create Indexes for student_chapter_performance_view
db.student_chapter_performance_view.createIndex({ institute_id: 1 });
db.student_chapter_performance_view.createIndex({ student_id: 1 });
db.student_chapter_performance_view.createIndex({ subject: 1 });
db.student_chapter_performance_view.createIndex({ chapter: 1 });

// Create Indexes for submissions_view
db.submissions_view.createIndex({ institute_id: 1 });
db.submissions_view.createIndex({ term_id: 1 });
db.submissions_view.createIndex({ student_id: 1 });
db.submissions_view.createIndex({ assignment_id: 1 });
db.submissions_view.createIndex({ created_at: 1 });

// Create Indexes for subjects view
db.subjects.createIndex({ grade: 1 });
db.subjects.createIndex({ subject: 1 });

// Create Indexes for user_roles_view
db.user_roles_view.createIndex({ email: 1 });
db.user_roles_view.createIndex({ "roles.institute_id": 1 });
db.user_roles_view.createIndex({ "roles.role": 1 });
