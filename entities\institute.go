package entities

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	ErrNameEmpty    = errors.New("name cannot be empty")
	ErrProgramEmpty = errors.New("program cannot be empty")
	ErrAddressNil   = errors.New("address cannot be nil")
)

type Institute struct {
	ID                *string    `json:"id,omitempty" bson:"_id,omitempty"`
	Name              *string    `json:"name" bson:"name"`
	Program           *string    `json:"program" bson:"program"`
	AvailableSections *[]string  `json:"available_sections" bson:"available_sections"`
	Address           *Address   `json:"address" bson:"address"`
	CreatedBy         *string    `json:"created_by" bson:"created_by"`
	CreatedAt         *time.Time `json:"created_at" bson:"created_at"`
	UpdatedBy         *string    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt         *time.Time `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy         *string    `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt         *time.Time `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

func NewInstitute(name, program string, address *Address, createdBy string) *Institute {
	now := time.Now()
	id := uuid.New().String()
	institute := &Institute{
		ID:        &id,
		Name:      &name,
		Program:   &program,
		Address:   address,
		CreatedBy: &createdBy,
		CreatedAt: &now,
	}
	return institute
}

func (i *Institute) Validate() error {
	if i.Name == nil || *i.Name == "" {
		return ErrNameEmpty
	}
	if i.Program == nil || *i.Program == "" {
		return ErrProgramEmpty
	}
	if i.Address == nil {
		return ErrAddressNil
	}
	return i.Address.Validate()
}
