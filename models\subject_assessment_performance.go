// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// SubjectAssessmentPerformance subject assessment performance
//
// swagger:model SubjectAssessmentPerformance
type SubjectAssessmentPerformance struct {

	// subject
	Subject string `json:"subject,omitempty"`

	// total achieved score
	TotalAchievedScore float32 `json:"totalAchievedScore,omitempty"`

	// total assignments solved
	TotalAssignmentsSolved int32 `json:"totalAssignmentsSolved,omitempty"`

	// total attempted score
	TotalAttemptedScore int32 `json:"totalAttemptedScore,omitempty"`
}

// Validate validates this subject assessment performance
func (m *SubjectAssessmentPerformance) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this subject assessment performance based on context it is used
func (m *SubjectAssessmentPerformance) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *SubjectAssessmentPerformance) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *SubjectAssessmentPerformance) UnmarshalBinary(b []byte) error {
	var res SubjectAssessmentPerformance
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
