package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type instructorProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Add implements InstructorProvider.
func (e *instructorProvider) Add(ctx context.Context, instructor *entities.Instructor) (string, error) {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : Add")
	defer span.End()

	if instructor.ID == nil {
		return constants.EmptyString, errors.New("instructor ID is required")
	}

	_, err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		InsertOne(ctx, instructor)
	if err != nil {
		log.Error().Msg(err.Error())
		return constants.EmptyString, err
	}
	return *instructor.ID, nil
}

// Delete implements InstructorProvider.
func (e *instructorProvider) Delete(ctx context.Context, id string, deletedBy string) error {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : Delete")
	defer span.End()

	instructor, err := e.Get(ctx, id)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	currentTime := time.Now()
	instructor.DeletedAt = &currentTime
	instructor.DeletedBy = &deletedBy

	_, err = e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructorsArchive).
		InsertOne(ctx, instructor)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	res, err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		DeleteOne(ctx, bson.D{{"_id", id}})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	if res.DeletedCount != 1 {
		log.Error().Msg("Instructor not found : " + id)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Edit implements InstructorProvider.
func (e *instructorProvider) Edit(ctx context.Context, id string, instructor *entities.Instructor) error {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : Edit")
	defer span.End()

	now := time.Now()
	instructor.UpdatedAt = &now

	res, err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		UpdateOne(ctx, bson.D{{"_id", id}}, bson.M{"$set": instructor})
	if err != nil {
		log.Error().Err(err).Msg("Error while updating instructor")
		return err
	}

	if res.MatchedCount != 1 {
		log.Error().Msg("Instructor not found : " + id)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Get implements InstructorProvider.
func (e *instructorProvider) Get(ctx context.Context, id string) (*entities.Instructor, error) {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : Get")
	defer span.End()

	instructor := &entities.Instructor{}
	err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		FindOne(ctx, bson.D{{"_id", id}}).
		Decode(instructor)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return instructor, nil
}

// GetByEmailAndInstitute implements InstructorProvider.
func (e *instructorProvider) GetByEmailAndInstitute(ctx context.Context, email string, instituteId string) (*entities.Instructor, error) {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : GetByEmailAndInstitute")
	defer span.End()

	instructor := &entities.Instructor{}
	err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		FindOne(ctx, bson.D{
			{"email", email},
			{"institute_id", instituteId},
		}).
		Decode(instructor)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return instructor, nil
}

// GetAll implements InstructorProvider.
func (e *instructorProvider) GetAll(ctx context.Context, instituteId *string, email *string) (*[]entities.Instructor, error) {
	ctx, span := e.tracer.Start(ctx, "InstructorProvider : GetAll")
	defer span.End()

	instructorList := make([]entities.Instructor, 0)
	filter := bson.D{}

	if instituteId != nil {
		filter = append(filter, bson.E{"institute_id", instituteId})
	}
	if email != nil {
		filter = append(filter, bson.E{"email", email})
	}

	cursor, err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlInstructors).
		Find(ctx, filter)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	err = cursor.All(ctx, &instructorList)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return &instructorList, nil
}
func NewInstructorProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) InstructorProvider {
	return &instructorProvider{mongoClient: mongoClient, dbName: databaseName, tracer: tracer}
}
