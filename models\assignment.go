// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Assignment assignment
//
// swagger:model Assignment
type Assignment struct {

	// class
	Class int32 `json:"class,omitempty"`

	// duration
	Duration int32 `json:"duration,omitempty"`

	// Optional folder ID the assignment belongs to
	FolderID string `json:"folderId,omitempty"`

	// id
	ID string `json:"id,omitempty"`

	// name
	// Example: Assignment-1
	Name string `json:"name,omitempty"`

	// questions
	Questions []*Question `json:"questions"`

	// section list
	SectionList []string `json:"sectionList"`

	// subject name
	SubjectName string `json:"subjectName,omitempty"`

	// total score
	TotalScore float32 `json:"totalScore,omitempty"`
}

// Validate validates this assignment
func (m *Assignment) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateQuestions(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Assignment) validateQuestions(formats strfmt.Registry) error {
	if swag.IsZero(m.Questions) { // not required
		return nil
	}

	for i := 0; i < len(m.Questions); i++ {
		if swag.IsZero(m.Questions[i]) { // not required
			continue
		}

		if m.Questions[i] != nil {
			if err := m.Questions[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("questions" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("questions" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this assignment based on the context it is used
func (m *Assignment) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateQuestions(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Assignment) contextValidateQuestions(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Questions); i++ {

		if m.Questions[i] != nil {

			if swag.IsZero(m.Questions[i]) { // not required
				return nil
			}

			if err := m.Questions[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("questions" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("questions" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *Assignment) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Assignment) UnmarshalBinary(b []byte) error {
	var res Assignment
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
