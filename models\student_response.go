// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// StudentResponse student response
//
// swagger:model StudentResponse
type StudentResponse struct {

	// feedback
	// Example: Improvement needed in so and so area.
	Feedback string `json:"feedback,omitempty"`

	// question
	Question string `json:"question,omitempty"`

	// question number
	QuestionNumber int32 `json:"questionNumber,omitempty"`

	// question rubric
	QuestionRubric string `json:"questionRubric,omitempty"`

	// question score
	QuestionScore int32 `json:"questionScore,omitempty"`

	// score
	// Example: 5
	Score float32 `json:"score,omitempty"`

	// student response
	// Example: Mitochondria is the powerhouse of the cell.
	StudentResponse string `json:"studentResponse,omitempty"`
}

// Validate validates this student response
func (m *StudentResponse) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this student response based on context it is used
func (m *StudentResponse) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *StudentResponse) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentResponse) UnmarshalBinary(b []byte) error {
	var res StudentResponse
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
