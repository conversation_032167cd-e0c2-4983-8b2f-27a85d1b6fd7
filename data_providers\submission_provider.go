package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type SubmissionProvider interface {
	Add(ctx context.Context, submission *entities.Submission) (string, error)
	Delete(ctx context.Context, instituteId, assignmentId, studentId string, deletedBy string) error
	Get(ctx context.Context, instituteId string, assignmentId string, studentId string) (*entities.SubmissionView, error)
	GetForEdit(ctx context.Context, instituteId string, assignmentId string, studentId string) (*entities.Submission, error)
	Edit(ctx context.Context, instituteId string, assignmentId string, studentId string, submission *entities.Submission) (string, error)
	GetAll(ctx context.Context, instituteId string, assignmentId *string, class *int32, section *[]string, studentId *string, termId *string, status *string) (*[]entities.SubmissionView, error)
	Publish(ctx context.Context, instituteId string, assignmentId string, studentId *string, updatedBy string) error
}
