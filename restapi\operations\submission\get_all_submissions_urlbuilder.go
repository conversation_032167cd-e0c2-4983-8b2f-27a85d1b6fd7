// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"errors"
	"net/url"
	golangswaggerpaths "path"
	"strings"

	"github.com/go-openapi/swag"
)

// GetAllSubmissionsURL generates an URL for the get all submissions operation
type GetAllSubmissionsURL struct {
	InstituteID string

	AssignmentID *string
	Grade        *int32
	Section      []string
	StudentID    *string
	TermID       *string

	_basePath string
	// avoid unkeyed usage
	_ struct{}
}

// WithBasePath sets the base path for this url builder, only required when it's different from the
// base path specified in the swagger spec.
// When the value of the base path is an empty string
func (o *GetAllSubmissionsURL) WithBasePath(bp string) *GetAllSubmissionsURL {
	o.SetBasePath(bp)
	return o
}

// SetBasePath sets the base path for this url builder, only required when it's different from the
// base path specified in the swagger spec.
// When the value of the base path is an empty string
func (o *GetAllSubmissionsURL) SetBasePath(bp string) {
	o._basePath = bp
}

// Build a url path and query string
func (o *GetAllSubmissionsURL) Build() (*url.URL, error) {
	var _result url.URL

	var _path = "/institute/{instituteId}/submissions"

	instituteID := o.InstituteID
	if instituteID != "" {
		_path = strings.Replace(_path, "{instituteId}", instituteID, -1)
	} else {
		return nil, errors.New("instituteId is required on GetAllSubmissionsURL")
	}

	_basePath := o._basePath
	if _basePath == "" {
		_basePath = "/v1/api"
	}
	_result.Path = golangswaggerpaths.Join(_basePath, _path)

	qs := make(url.Values)

	var assignmentIDQ string
	if o.AssignmentID != nil {
		assignmentIDQ = *o.AssignmentID
	}
	if assignmentIDQ != "" {
		qs.Set("assignmentId", assignmentIDQ)
	}

	var gradeQ string
	if o.Grade != nil {
		gradeQ = swag.FormatInt32(*o.Grade)
	}
	if gradeQ != "" {
		qs.Set("grade", gradeQ)
	}

	var sectionIR []string
	for _, sectionI := range o.Section {
		sectionIS := sectionI
		if sectionIS != "" {
			sectionIR = append(sectionIR, sectionIS)
		}
	}

	section := swag.JoinByFormat(sectionIR, "")

	if len(section) > 0 {
		qsv := section[0]
		if qsv != "" {
			qs.Set("section", qsv)
		}
	}

	var studentIDQ string
	if o.StudentID != nil {
		studentIDQ = *o.StudentID
	}
	if studentIDQ != "" {
		qs.Set("studentId", studentIDQ)
	}

	var termIDQ string
	if o.TermID != nil {
		termIDQ = *o.TermID
	}
	if termIDQ != "" {
		qs.Set("termId", termIDQ)
	}

	_result.RawQuery = qs.Encode()

	return &_result, nil
}

// Must is a helper function to panic when the url builder returns an error
func (o *GetAllSubmissionsURL) Must(u *url.URL, err error) *url.URL {
	if err != nil {
		panic(err)
	}
	if u == nil {
		panic("url can't be nil")
	}
	return u
}

// String returns the string representation of the path with query string
func (o *GetAllSubmissionsURL) String() string {
	return o.Must(o.Build()).String()
}

// BuildFull builds a full url with scheme, host, path and query string
func (o *GetAllSubmissionsURL) BuildFull(scheme, host string) (*url.URL, error) {
	if scheme == "" {
		return nil, errors.New("scheme is required for a full url on GetAllSubmissionsURL")
	}
	if host == "" {
		return nil, errors.New("host is required for a full url on GetAllSubmissionsURL")
	}

	base, err := o.Build()
	if err != nil {
		return nil, err
	}

	base.Scheme = scheme
	base.Host = host
	return base, nil
}

// StringFull returns the string representation of a complete url
func (o *GetAllSubmissionsURL) StringFull(scheme, host string) string {
	return o.Must(o.BuildFull(scheme, host)).String()
}
