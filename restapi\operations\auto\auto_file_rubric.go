// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// AutoFileRubricHandlerFunc turns a function with the right signature into a auto file rubric handler
type AutoFileRubricHandlerFunc func(AutoFileRubricParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn AutoFileRubricHandlerFunc) Handle(params AutoFileRubricParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// AutoFileRubricHandler interface for that can handle valid auto file rubric params
type AutoFileRubricHandler interface {
	Handle(AutoFileRubricParams, interface{}) middleware.Responder
}

// NewAutoFileRubric creates a new http.Handler for the auto file rubric operation
func NewAutoFileRubric(ctx *middleware.Context, handler <PERSON>FileR<PERSON>ric<PERSON><PERSON><PERSON>) *AutoFileRubric {
	return &AutoFileRubric{Context: ctx, Handler: handler}
}

/*
	AutoFileRubric swagger:route PUT /institute/{instituteId}/upload/rubric auto autoFileRubric

Add rubric from a file.

Add Rubric from a file
*/
type AutoFileRubric struct {
	Context *middleware.Context
	Handler AutoFileRubricHandler
}

func (o *AutoFileRubric) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewAutoFileRubricParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
