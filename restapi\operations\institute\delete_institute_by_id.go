// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteInstituteByIDHandlerFunc turns a function with the right signature into a delete institute by Id handler
type DeleteInstituteByIDHandlerFunc func(DeleteInstituteByIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteInstituteByIDHandlerFunc) Handle(params DeleteInstituteByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteInstituteByIDHandler interface for that can handle valid delete institute by Id params
type DeleteInstituteByIDHandler interface {
	Handle(DeleteInstituteByIDParams, interface{}) middleware.Responder
}

// NewDeleteInstituteByID creates a new http.Handler for the delete institute by Id operation
func NewDeleteInstituteByID(ctx *middleware.Context, handler DeleteInstituteByIDHandler) *DeleteInstituteByID {
	return &DeleteInstituteByID{Context: ctx, Handler: handler}
}

/*
	DeleteInstituteByID swagger:route DELETE /institute/{instituteId} institute deleteInstituteById

# Delete institute

Delete institute by id
*/
type DeleteInstituteByID struct {
	Context *middleware.Context
	Handler DeleteInstituteByIDHandler
}

func (o *DeleteInstituteByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteInstituteByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
