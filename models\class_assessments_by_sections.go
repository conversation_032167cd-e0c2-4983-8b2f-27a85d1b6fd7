// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassAssessmentsBySections class assessments by sections
//
// swagger:model ClassAssessmentsBySections
type ClassAssessmentsBySections struct {

	// assessments
	Assessments []int32 `json:"assessments"`

	// sections
	Sections []string `json:"sections"`
}

// Validate validates this class assessments by sections
func (m *ClassAssessmentsBySections) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class assessments by sections based on context it is used
func (m *ClassAssessmentsBySections) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassAssessmentsBySections) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassAssessmentsBySections) UnmarshalBinary(b []byte) error {
	var res ClassAssessmentsBySections
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
