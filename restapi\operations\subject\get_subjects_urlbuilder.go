// Code generated by go-swagger; DO NOT EDIT.

package subject

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"errors"
	"net/url"
	golangswaggerpaths "path"
	"strings"

	"github.com/go-openapi/swag"
)

// GetSubjectsURL generates an URL for the get subjects operation
type GetSubjectsURL struct {
	Grade       int32
	InstituteID string

	_basePath string
	// avoid unkeyed usage
	_ struct{}
}

// WithBasePath sets the base path for this url builder, only required when it's different from the
// base path specified in the swagger spec.
// When the value of the base path is an empty string
func (o *GetSubjectsURL) WithBasePath(bp string) *GetSubjectsURL {
	o.SetBasePath(bp)
	return o
}

// SetBasePath sets the base path for this url builder, only required when it's different from the
// base path specified in the swagger spec.
// When the value of the base path is an empty string
func (o *GetSubjectsURL) SetBasePath(bp string) {
	o._basePath = bp
}

// Build a url path and query string
func (o *GetSubjectsURL) Build() (*url.URL, error) {
	var _result url.URL

	var _path = "/institute/{instituteId}/subject/grade/{grade}"

	grade := swag.FormatInt32(o.Grade)
	if grade != "" {
		_path = strings.Replace(_path, "{grade}", grade, -1)
	} else {
		return nil, errors.New("grade is required on GetSubjectsURL")
	}

	instituteID := o.InstituteID
	if instituteID != "" {
		_path = strings.Replace(_path, "{instituteId}", instituteID, -1)
	} else {
		return nil, errors.New("instituteId is required on GetSubjectsURL")
	}

	_basePath := o._basePath
	if _basePath == "" {
		_basePath = "/v1/api"
	}
	_result.Path = golangswaggerpaths.Join(_basePath, _path)

	return &_result, nil
}

// Must is a helper function to panic when the url builder returns an error
func (o *GetSubjectsURL) Must(u *url.URL, err error) *url.URL {
	if err != nil {
		panic(err)
	}
	if u == nil {
		panic("url can't be nil")
	}
	return u
}

// String returns the string representation of the path with query string
func (o *GetSubjectsURL) String() string {
	return o.Must(o.Build()).String()
}

// BuildFull builds a full url with scheme, host, path and query string
func (o *GetSubjectsURL) BuildFull(scheme, host string) (*url.URL, error) {
	if scheme == "" {
		return nil, errors.New("scheme is required for a full url on GetSubjectsURL")
	}
	if host == "" {
		return nil, errors.New("host is required for a full url on GetSubjectsURL")
	}

	base, err := o.Build()
	if err != nil {
		return nil, err
	}

	base.Scheme = scheme
	base.Host = host
	return base, nil
}

// StringFull returns the string representation of a complete url
func (o *GetSubjectsURL) StringFull(scheme, host string) string {
	return o.Must(o.BuildFull(scheme, host)).String()
}
