package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/institute"
	"eddyowl-backend/utils"
	"slices"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getInstituteByIDImpl struct {
	provider          data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetInstituteByIDHandler(provider data_providers.InstituteProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) institute.GetInstituteByIDHandler {
	return &getInstituteByIDImpl{provider: provider, userRolesProvider: userRolesProvider, tracer: tracer}
}

func (impl *getInstituteByIDImpl) Handle(params institute.GetInstituteByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetInstituteByIDHandler")
	defer span.End()

	if params.InstituteID == constants.EmptyString {
		return institute.NewGetInstituteByIDBadRequest().WithPayload("Invalid InstituteID")
	}

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return institute.NewGetInstituteByIDForbidden().WithPayload("Unauthorized")
	}

	instituteObj, err := impl.provider.Get(ctx, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Msg("Error getting institute")
		if utils.IsNoDocumentFound(err) {
			return institute.NewGetInstituteByIDNotFound().WithPayload("Institute Not Found")
		}
		return institute.NewGetInstituteByIDInternalServerError().WithPayload("Unable to get institute")
	}

	response := &models.Institute{
		ID:   *instituteObj.ID,
		Name: *instituteObj.Name,
		Address: &models.Address{
			AddressOne: *instituteObj.Address.AddressOne,
			AddressTwo: *instituteObj.Address.AddressTwo,
			City:       *instituteObj.Address.City,
			State:      *instituteObj.Address.State,
			Pincode:    *instituteObj.Address.Pincode,
		},
		Program: *instituteObj.Program,
	}

	// Start with default sections
	response.SectionList = constants.DefaultSections

	// Add custom sections if they exist
	if instituteObj.AvailableSections != nil {
		// Merge custom sections with defaults, avoiding duplicates
		for _, section := range *instituteObj.AvailableSections {
			exists := slices.Contains(response.SectionList, section)
			if !exists {
				response.SectionList = append(response.SectionList, section)
			}
		}
	}

	return institute.NewGetInstituteByIDOK().WithPayload(response)
}
