// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetStudentOverallStatsHandlerFunc turns a function with the right signature into a get student overall stats handler
type GetStudentOverallStatsHandlerFunc func(GetStudentOverallStatsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetStudentOverallStatsHandlerFunc) Handle(params GetStudentOverallStatsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetStudentOverallStatsHandler interface for that can handle valid get student overall stats params
type GetStudentOverallStatsHandler interface {
	Handle(GetStudentOverallStatsParams, interface{}) middleware.Responder
}

// NewGetStudentOverallStats creates a new http.Handler for the get student overall stats operation
func NewGetStudentOverallStats(ctx *middleware.Context, handler GetStudentOverallStatsHandler) *GetStudentOverallStats {
	return &GetStudentOverallStats{Context: ctx, Handler: handler}
}

/*
	GetStudentOverallStats swagger:route GET /institute/{instituteId}/student/{studentId}/studentOverallStats stats getStudentOverallStats

# Get Student Overall Stats

Get overall stats for student
*/
type GetStudentOverallStats struct {
	Context *middleware.Context
	Handler GetStudentOverallStatsHandler
}

func (o *GetStudentOverallStats) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetStudentOverallStatsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
