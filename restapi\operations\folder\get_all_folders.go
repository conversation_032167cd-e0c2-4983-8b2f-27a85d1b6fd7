// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllFoldersHandlerFunc turns a function with the right signature into a get all folders handler
type GetAllFoldersHandlerFunc func(GetAllFoldersParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetAllFoldersHandlerFunc) Handle(params GetAllFoldersParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllFoldersHandler interface for that can handle valid get all folders params
type GetAllFoldersHandler interface {
	Handle(GetAllFoldersParams, interface{}) middleware.Responder
}

// NewGetAllFolders creates a new http.Handler for the get all folders operation
func NewGetAllFolders(ctx *middleware.Context, handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) *GetAllFolders {
	return &GetAllFolders{Context: ctx, Handler: handler}
}

/*
	GetAllFolders swagger:route GET /institute/{instituteId}/folder folder getAllFolders

Get all folders for an institute
*/
type GetAllFolders struct {
	Context *middleware.Context
	Handler GetAllFoldersHandler
}

func (o *GetAllFolders) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllFoldersParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
