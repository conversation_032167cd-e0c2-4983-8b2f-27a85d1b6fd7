// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// AddInstructorByInstituteIDOKCode is the HTTP code returned for type AddInstructorByInstituteIDOK
const AddInstructorByInstituteIDOKCode int = 200

/*
AddInstructorByInstituteIDOK Successful operation

swagger:response addInstructorByInstituteIdOK
*/
type AddInstructorByInstituteIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDOK creates AddInstructorByInstituteIDOK with default headers values
func NewAddInstructorByInstituteIDOK() *AddInstructorByInstituteIDOK {

	return &AddInstructorByInstituteIDOK{}
}

// WithPayload adds the payload to the add instructor by institute Id o k response
func (o *AddInstructorByInstituteIDOK) WithPayload(payload *models.SuccessResponse) *AddInstructorByInstituteIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id o k response
func (o *AddInstructorByInstituteIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// AddInstructorByInstituteIDBadRequestCode is the HTTP code returned for type AddInstructorByInstituteIDBadRequest
const AddInstructorByInstituteIDBadRequestCode int = 400

/*
AddInstructorByInstituteIDBadRequest Bad Request

swagger:response addInstructorByInstituteIdBadRequest
*/
type AddInstructorByInstituteIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDBadRequest creates AddInstructorByInstituteIDBadRequest with default headers values
func NewAddInstructorByInstituteIDBadRequest() *AddInstructorByInstituteIDBadRequest {

	return &AddInstructorByInstituteIDBadRequest{}
}

// WithPayload adds the payload to the add instructor by institute Id bad request response
func (o *AddInstructorByInstituteIDBadRequest) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id bad request response
func (o *AddInstructorByInstituteIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddInstructorByInstituteIDForbiddenCode is the HTTP code returned for type AddInstructorByInstituteIDForbidden
const AddInstructorByInstituteIDForbiddenCode int = 403

/*
AddInstructorByInstituteIDForbidden Forbidden

swagger:response addInstructorByInstituteIdForbidden
*/
type AddInstructorByInstituteIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDForbidden creates AddInstructorByInstituteIDForbidden with default headers values
func NewAddInstructorByInstituteIDForbidden() *AddInstructorByInstituteIDForbidden {

	return &AddInstructorByInstituteIDForbidden{}
}

// WithPayload adds the payload to the add instructor by institute Id forbidden response
func (o *AddInstructorByInstituteIDForbidden) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id forbidden response
func (o *AddInstructorByInstituteIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddInstructorByInstituteIDNotFoundCode is the HTTP code returned for type AddInstructorByInstituteIDNotFound
const AddInstructorByInstituteIDNotFoundCode int = 404

/*
AddInstructorByInstituteIDNotFound Not Found

swagger:response addInstructorByInstituteIdNotFound
*/
type AddInstructorByInstituteIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDNotFound creates AddInstructorByInstituteIDNotFound with default headers values
func NewAddInstructorByInstituteIDNotFound() *AddInstructorByInstituteIDNotFound {

	return &AddInstructorByInstituteIDNotFound{}
}

// WithPayload adds the payload to the add instructor by institute Id not found response
func (o *AddInstructorByInstituteIDNotFound) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id not found response
func (o *AddInstructorByInstituteIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddInstructorByInstituteIDTooManyRequestsCode is the HTTP code returned for type AddInstructorByInstituteIDTooManyRequests
const AddInstructorByInstituteIDTooManyRequestsCode int = 429

/*
AddInstructorByInstituteIDTooManyRequests Too Many Requests

swagger:response addInstructorByInstituteIdTooManyRequests
*/
type AddInstructorByInstituteIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDTooManyRequests creates AddInstructorByInstituteIDTooManyRequests with default headers values
func NewAddInstructorByInstituteIDTooManyRequests() *AddInstructorByInstituteIDTooManyRequests {

	return &AddInstructorByInstituteIDTooManyRequests{}
}

// WithPayload adds the payload to the add instructor by institute Id too many requests response
func (o *AddInstructorByInstituteIDTooManyRequests) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id too many requests response
func (o *AddInstructorByInstituteIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddInstructorByInstituteIDInternalServerErrorCode is the HTTP code returned for type AddInstructorByInstituteIDInternalServerError
const AddInstructorByInstituteIDInternalServerErrorCode int = 500

/*
AddInstructorByInstituteIDInternalServerError Internal Server Error

swagger:response addInstructorByInstituteIdInternalServerError
*/
type AddInstructorByInstituteIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDInternalServerError creates AddInstructorByInstituteIDInternalServerError with default headers values
func NewAddInstructorByInstituteIDInternalServerError() *AddInstructorByInstituteIDInternalServerError {

	return &AddInstructorByInstituteIDInternalServerError{}
}

// WithPayload adds the payload to the add instructor by institute Id internal server error response
func (o *AddInstructorByInstituteIDInternalServerError) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id internal server error response
func (o *AddInstructorByInstituteIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddInstructorByInstituteIDServiceUnavailableCode is the HTTP code returned for type AddInstructorByInstituteIDServiceUnavailable
const AddInstructorByInstituteIDServiceUnavailableCode int = 503

/*
AddInstructorByInstituteIDServiceUnavailable Service Unvailable

swagger:response addInstructorByInstituteIdServiceUnavailable
*/
type AddInstructorByInstituteIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddInstructorByInstituteIDServiceUnavailable creates AddInstructorByInstituteIDServiceUnavailable with default headers values
func NewAddInstructorByInstituteIDServiceUnavailable() *AddInstructorByInstituteIDServiceUnavailable {

	return &AddInstructorByInstituteIDServiceUnavailable{}
}

// WithPayload adds the payload to the add instructor by institute Id service unavailable response
func (o *AddInstructorByInstituteIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *AddInstructorByInstituteIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add instructor by institute Id service unavailable response
func (o *AddInstructorByInstituteIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddInstructorByInstituteIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
