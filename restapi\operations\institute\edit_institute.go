// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditInstituteHandlerFunc turns a function with the right signature into a edit institute handler
type EditInstituteHandlerFunc func(EditInstituteParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditInstituteHandlerFunc) Handle(params EditInstituteParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditInstituteHandler interface for that can handle valid edit institute params
type EditInstituteHandler interface {
	Handle(EditInstituteParams, interface{}) middleware.Responder
}

// NewEditInstitute creates a new http.Handler for the edit institute operation
func NewEditInstitute(ctx *middleware.Context, handler EditInstituteHandler) *EditInstitute {
	return &EditInstitute{Context: ctx, Handler: handler}
}

/*
	EditInstitute swagger:route PUT /institute/{instituteId} institute editInstitute

# Edit institute

Edit institute information
*/
type EditInstitute struct {
	Context *middleware.Context
	Handler EditInstituteHandler
}

func (o *EditInstitute) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditInstituteParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
