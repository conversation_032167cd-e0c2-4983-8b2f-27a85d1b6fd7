package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

// getAssignmentByIDImpl implements the GetAssignmentByIDHandler interface generated by go-swagger.
type getAssignmentByIDImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

// NewGetAssignmentByIDHandler returns an instance that can handle the GetAssignmentByID endpoint.
func NewGetAssignmentByIDHandler(
	assignmentProvider data_providers.AssignmentProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.GetAssignmentByIDHandler {
	return &getAssignmentByIDImpl{
		assignmentProvider: assignmentProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

// Handle processes the GetAssignmentByID request.
func (impl *getAssignmentByIDImpl) Handle(params assignment.GetAssignmentByIDParams, principal interface{}) middleware.Responder {
	// Start a tracing span if you are using OpenTelemetry
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAssignmentByIDHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewGetAssignmentByIDForbidden().WithPayload("Unauthorized")
	}
	showRubric := false
	err = utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		if !errors.Is(err, utils.ErrUnauthorizedRole) {
			log.Error().Err(err).Msg("Failed to check user roles")
			return assignment.NewGetAssignmentByIDInternalServerError().WithPayload("Unable to fetch user roles")
		}
	}
	if err == nil {
		showRubric = true
	}

	// Validate required parameters
	if params.InstituteID == "" {
		log.Error().Msg("Missing or invalid institute ID")
		return assignment.NewGetAssignmentByIDBadRequest().WithPayload("Missing or invalid institute ID")
	}

	if params.AssignmentID == "" {
		log.Error().Msg("Missing or invalid assignment ID")
		return assignment.NewGetAssignmentByIDBadRequest().WithPayload("Missing or invalid assignment ID")
	}

	// Attempt to fetch the assignment
	assgn, err := impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch assignment")
		// You might have an ErrNotFound that you can check for a 404
		if errors.Is(err, mongo.ErrNoDocuments) {
			return assignment.NewGetAssignmentByIDNotFound().WithPayload("Assignment not found")
		}
		return assignment.NewGetAssignmentByIDInternalServerError().WithPayload("Failed to fetch assignment")
	}

	// Map to swagger model
	responseModel := MapAssignmentToModel(assgn, showRubric)
	return assignment.NewGetAssignmentByIDOK().WithPayload(responseModel)
}

// MapAssignmentToModel converts an entities.Assignment to a models.Assignment.
func MapAssignmentToModel(e *entities.Assignment, showRubric bool) *models.Assignment {
	if e == nil {
		return nil
	}

	// Prepare the top-level Assignment model
	m := &models.Assignment{
		ID:          utils.GetString(e.ID),
		Name:        utils.GetString(e.Name),
		Class:       int32(e.Grade),
		SubjectName: utils.GetString(e.Subject),
		SectionList: utils.GetStringSlice(e.Sections),
		TotalScore:  (e.TotalScore),
	}

	// Map Questions (if you want to include them)
	if e.Questions != nil {
		var qs []*models.Question
		for _, q := range *e.Questions {
			qs = append(qs, MapQuestionToModel(&q, showRubric))
		}
		m.Questions = qs
	}

	return m
}

// MapQuestionToModel converts an entities.Question to a models.Question.
func MapQuestionToModel(eq *entities.Question, showRubric bool) *models.Question {
	if eq == nil {
		return nil
	}

	// Prepare the question
	mq := &models.Question{
		QuestionNumber: int32(eq.QuestionNumber),
		Question:       utils.GetString(eq.Question),
		QuestionScore:  (eq.Score), // or float32 -> float64 as needed
	}

	if showRubric {
		mq.QuestionRubric = utils.GetString(eq.Rubric)
	}

	// Map Topics
	if eq.Topics != nil {
		var tms []*models.ChapterTopics
		for _, t := range *eq.Topics {
			tms = append(tms, MapTopicToModel(&t))
		}
		mq.Topics = tms
	}

	return mq
}

// MapTopicToModel converts an entities.Topic to a models.Topic.
func MapTopicToModel(et *entities.Topic) *models.ChapterTopics {
	if et == nil {
		return nil
	}

	return &models.ChapterTopics{
		Chapter: utils.GetString(et.Chapter),
		Topics:  utils.GetStringSlice(et.Topics),
	}
}
