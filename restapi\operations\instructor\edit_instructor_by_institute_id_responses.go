// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditInstructorByInstituteIDOKCode is the HTTP code returned for type EditInstructorByInstituteIDOK
const EditInstructorByInstituteIDOKCode int = 200

/*
EditInstructorByInstituteIDOK Successful operation

swagger:response editInstructorByInstituteIdOK
*/
type EditInstructorByInstituteIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDOK creates EditInstructorByInstituteIDOK with default headers values
func NewEditInstructorByInstituteIDOK() *EditInstructorByInstituteIDOK {

	return &EditInstructorByInstituteIDOK{}
}

// WithPayload adds the payload to the edit instructor by institute Id o k response
func (o *EditInstructorByInstituteIDOK) WithPayload(payload *models.SuccessResponse) *EditInstructorByInstituteIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id o k response
func (o *EditInstructorByInstituteIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditInstructorByInstituteIDBadRequestCode is the HTTP code returned for type EditInstructorByInstituteIDBadRequest
const EditInstructorByInstituteIDBadRequestCode int = 400

/*
EditInstructorByInstituteIDBadRequest Bad Request

swagger:response editInstructorByInstituteIdBadRequest
*/
type EditInstructorByInstituteIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDBadRequest creates EditInstructorByInstituteIDBadRequest with default headers values
func NewEditInstructorByInstituteIDBadRequest() *EditInstructorByInstituteIDBadRequest {

	return &EditInstructorByInstituteIDBadRequest{}
}

// WithPayload adds the payload to the edit instructor by institute Id bad request response
func (o *EditInstructorByInstituteIDBadRequest) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id bad request response
func (o *EditInstructorByInstituteIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstructorByInstituteIDForbiddenCode is the HTTP code returned for type EditInstructorByInstituteIDForbidden
const EditInstructorByInstituteIDForbiddenCode int = 403

/*
EditInstructorByInstituteIDForbidden Forbidden

swagger:response editInstructorByInstituteIdForbidden
*/
type EditInstructorByInstituteIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDForbidden creates EditInstructorByInstituteIDForbidden with default headers values
func NewEditInstructorByInstituteIDForbidden() *EditInstructorByInstituteIDForbidden {

	return &EditInstructorByInstituteIDForbidden{}
}

// WithPayload adds the payload to the edit instructor by institute Id forbidden response
func (o *EditInstructorByInstituteIDForbidden) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id forbidden response
func (o *EditInstructorByInstituteIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstructorByInstituteIDNotFoundCode is the HTTP code returned for type EditInstructorByInstituteIDNotFound
const EditInstructorByInstituteIDNotFoundCode int = 404

/*
EditInstructorByInstituteIDNotFound Not Found

swagger:response editInstructorByInstituteIdNotFound
*/
type EditInstructorByInstituteIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDNotFound creates EditInstructorByInstituteIDNotFound with default headers values
func NewEditInstructorByInstituteIDNotFound() *EditInstructorByInstituteIDNotFound {

	return &EditInstructorByInstituteIDNotFound{}
}

// WithPayload adds the payload to the edit instructor by institute Id not found response
func (o *EditInstructorByInstituteIDNotFound) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id not found response
func (o *EditInstructorByInstituteIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstructorByInstituteIDTooManyRequestsCode is the HTTP code returned for type EditInstructorByInstituteIDTooManyRequests
const EditInstructorByInstituteIDTooManyRequestsCode int = 429

/*
EditInstructorByInstituteIDTooManyRequests Too Many Requests

swagger:response editInstructorByInstituteIdTooManyRequests
*/
type EditInstructorByInstituteIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDTooManyRequests creates EditInstructorByInstituteIDTooManyRequests with default headers values
func NewEditInstructorByInstituteIDTooManyRequests() *EditInstructorByInstituteIDTooManyRequests {

	return &EditInstructorByInstituteIDTooManyRequests{}
}

// WithPayload adds the payload to the edit instructor by institute Id too many requests response
func (o *EditInstructorByInstituteIDTooManyRequests) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id too many requests response
func (o *EditInstructorByInstituteIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstructorByInstituteIDInternalServerErrorCode is the HTTP code returned for type EditInstructorByInstituteIDInternalServerError
const EditInstructorByInstituteIDInternalServerErrorCode int = 500

/*
EditInstructorByInstituteIDInternalServerError Internal Server Error

swagger:response editInstructorByInstituteIdInternalServerError
*/
type EditInstructorByInstituteIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDInternalServerError creates EditInstructorByInstituteIDInternalServerError with default headers values
func NewEditInstructorByInstituteIDInternalServerError() *EditInstructorByInstituteIDInternalServerError {

	return &EditInstructorByInstituteIDInternalServerError{}
}

// WithPayload adds the payload to the edit instructor by institute Id internal server error response
func (o *EditInstructorByInstituteIDInternalServerError) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id internal server error response
func (o *EditInstructorByInstituteIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstructorByInstituteIDServiceUnavailableCode is the HTTP code returned for type EditInstructorByInstituteIDServiceUnavailable
const EditInstructorByInstituteIDServiceUnavailableCode int = 503

/*
EditInstructorByInstituteIDServiceUnavailable Service Unvailable

swagger:response editInstructorByInstituteIdServiceUnavailable
*/
type EditInstructorByInstituteIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstructorByInstituteIDServiceUnavailable creates EditInstructorByInstituteIDServiceUnavailable with default headers values
func NewEditInstructorByInstituteIDServiceUnavailable() *EditInstructorByInstituteIDServiceUnavailable {

	return &EditInstructorByInstituteIDServiceUnavailable{}
}

// WithPayload adds the payload to the edit instructor by institute Id service unavailable response
func (o *EditInstructorByInstituteIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditInstructorByInstituteIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit instructor by institute Id service unavailable response
func (o *EditInstructorByInstituteIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstructorByInstituteIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
