package handlers

import (
	"context"
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type addTopicsToAssignmentImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	aiComponent        components.AIComponent
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewAddTopicsToAssignmentHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	aiComponent components.AIComponent,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.AddTopicsToAssignmentHandler {
	return &addTopicsToAssignmentImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		aiComponent:        aiComponent,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *addTopicsToAssignmentImpl) Handle(params assignment.AddTopicsToAssignmentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : AddTopicsToAssignmentHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewAddTopicsToAssignmentForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.addTopicsToAssignmentValidator(ctx, params)
	if !valid {
		return validateResp
	}
	err = impl.aiComponent.AssignTopics(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		log.Error().Msg(err.Error())
		return assignment.NewAddTopicsToAssignmentInternalServerError().WithPayload("Unable to add topics")
	}
	return assignment.NewAddTopicsToAssignmentOK().WithPayload(&models.SuccessResponse{
		ID:      params.AssignmentID,
		Message: "Topic Generation Requested Successfully",
	})
}

func (impl *addTopicsToAssignmentImpl) addTopicsToAssignmentValidator(ctx context.Context, params assignment.AddTopicsToAssignmentParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, assignment.NewAddTopicsToAssignmentBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewAddTopicsToAssignmentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewAddTopicsToAssignmentInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, assignment.NewAddTopicsToAssignmentBadRequest().WithPayload("Invalid Assignment ID")
	}
	_, err = impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewAddTopicsToAssignmentBadRequest().WithPayload("Invalid Assignment ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewAddTopicsToAssignmentInternalServerError().WithPayload("Unable to fetch Assignment")
	}
	return true, nil
}
