package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type publishSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewPublishSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.PublishSubmissionHandler {
	return &publishSubmissionImpl{
		submissionProvider: submissionProvider,
		instituteProvider:  instituteProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *publishSubmissionImpl) Handle(params submission.PublishSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : PublishSubmissionHandler")
	defer span.End()

	principalEmail := principal.(string)

	// Check if user has admin/instructor role
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principalEmail, params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewPublishSubmissionForbidden().WithPayload("Unauthorized")
	}

	// Validate institute exists
	_, err = impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return submission.NewPublishSubmissionBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Err(err).Msg("Failed to fetch institute")
		return submission.NewPublishSubmissionInternalServerError().WithPayload("Unable to fetch institute")
	}

	// Publish specific student's submission
	err = impl.submissionProvider.Publish(ctx, params.InstituteID, params.AssignmentID, &params.StudentID, principalEmail)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return submission.NewPublishSubmissionNotFound().WithPayload("Submission not found")
		}
		log.Error().Err(err).Msg("Failed to publish submission")
		return submission.NewPublishSubmissionInternalServerError().WithPayload("Failed to publish submission")
	}

	return submission.NewPublishSubmissionOK().WithPayload(&models.SuccessResponse{
		Message: "Successfully published student submission",
	})
}
