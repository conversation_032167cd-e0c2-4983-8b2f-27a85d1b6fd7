// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditInstructorByInstituteIDHandlerFunc turns a function with the right signature into a edit instructor by institute Id handler
type EditInstructorByInstituteIDHandlerFunc func(EditInstructorByInstituteIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditInstructorByInstituteIDHandlerFunc) Handle(params EditInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditInstructorByInstituteIDHandler interface for that can handle valid edit instructor by institute Id params
type EditInstructorByInstituteIDHandler interface {
	Handle(EditInstructorByInstituteIDParams, interface{}) middleware.Responder
}

// NewEditInstructorByInstituteID creates a new http.Handler for the edit instructor by institute Id operation
func NewEditInstructorByInstituteID(ctx *middleware.Context, handler EditInstructorByInstituteIDHandler) *EditInstructorByInstituteID {
	return &EditInstructorByInstituteID{Context: ctx, Handler: handler}
}

/*
	EditInstructorByInstituteID swagger:route PUT /institute/{instituteId}/instructor instructor editInstructorByInstituteId

# Edit instructor

Edit instructor by institute id
*/
type EditInstructorByInstituteID struct {
	Context *middleware.Context
	Handler EditInstructorByInstituteIDHandler
}

func (o *EditInstructorByInstituteID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditInstructorByInstituteIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
