// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassAssessmentsBySubject class assessments by subject
//
// swagger:model ClassAssessmentsBySubject
type ClassAssessmentsBySubject struct {

	// assessments
	Assessments []int32 `json:"assessments"`

	// subjects
	Subjects []string `json:"subjects"`
}

// Validate validates this class assessments by subject
func (m *ClassAssessmentsBySubject) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class assessments by subject based on context it is used
func (m *ClassAssessmentsBySubject) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassAssessmentsBySubject) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassAssessmentsBySubject) UnmarshalBinary(b []byte) error {
	var res ClassAssessmentsBySubject
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
