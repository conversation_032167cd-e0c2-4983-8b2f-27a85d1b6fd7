package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

// createStudentImpl implements the CreateNewStudentHandler interface
type createStudentImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	userProvider      data_providers.UserProvider
	termProvider      data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

// NewCreateNewStudentHandler creates a new instance of createStudentImpl
func NewCreateNewStudentHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	userProvider data_providers.UserProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) student.CreateNewStudentHandler {
	return &createStudentImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		userProvider:      userProvider,
		termProvider:      termProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

// Handle processes the create new student request
func (impl *createStudentImpl) Handle(params student.CreateNewStudentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateNewStudentHandler")
	defer span.End()

	principal = principal.(string)
	if params.InstituteID == constants.EmptyString {
		return student.NewCreateNewStudentBadRequest().WithPayload("Invalid InstituteID")
	}

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewCreateNewStudentForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters
	valid, validateResp := impl.createStudentValidator(params)
	if !valid {
		return validateResp
	}

	var userId, email *string
	if params.Student.Email != constants.EmptyString {
		email = &params.Student.Email
		user, err := impl.userProvider.GetByEmail(ctx, params.Student.Email)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get user by email")
		}
		if user != nil {
			userId = user.ID
		}
	}

	termID, err := impl.getTermID(ctx, params)
	if err != nil {
		return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Term")
	}

	studentEntity := impl.createStudentEntity(params, email, userId, termID, principal.(string))
	if err := studentEntity.Validate(); err != nil {
		log.Error().Err(err).Msg("Invalid student entity")
		return student.NewCreateNewStudentBadRequest().WithPayload("Invalid Parameters")
	}

	studentIDResp, err := impl.studentProvider.Add(ctx, *studentEntity)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create student")
		return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to create Student")
	}

	return student.NewCreateNewStudentOK().WithPayload(
		&models.SuccessResponse{
			ID:      studentIDResp,
			Message: "Successfully created Student",
		},
	)
}

// createStudentValidator validates the create student request parameters
func (impl *createStudentImpl) createStudentValidator(params student.CreateNewStudentParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "createStudentValidator")
	defer span.End()

	if params.InstituteID == constants.EmptyString {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Institute ID")
	}

	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Err(err).Msg("Failed to fetch institute")
		return false, student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Institute")
	}

	if params.Student == nil {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Student Parameters")
	}

	if params.Student.StudentID == constants.EmptyString {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Student ID")
	}

	if params.Student.RollNumber == 0 {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Roll Number")
	}

	if params.Student.Class == 0 {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Class")
	}

	if params.Student.Section == constants.EmptyString {
		return false, student.NewCreateNewStudentBadRequest().WithPayload("Invalid Section")
	}

	return true, nil
}

// getTermID retrieves the term ID for the student
func (impl *createStudentImpl) getTermID(ctx context.Context, params student.CreateNewStudentParams) (string, error) {
	if params.TermID != nil {
		term, err := impl.termProvider.Get(ctx, *params.TermID, params.InstituteID)
		if err == nil && term != nil {
			return *term.ID, nil
		}
		log.Error().Err(err).Msg("Failed to get term by ID")
	}

	term, err := impl.termProvider.GetCurrent(ctx, params.InstituteID)
	if err == nil && term != nil {
		return *term.ID, nil
	}
	log.Error().Err(err).Msg("Failed to get current term")

	return "", errors.New("unable to fetch Term")
}

// createStudentEntity creates a new student entity from the request parameters
func (impl *createStudentImpl) createStudentEntity(params student.CreateNewStudentParams, email, userId *string, termID, createdBy string) *entities.Student {
	studentEntity := entities.NewStudent(
		params.InstituteID,
		params.Student.StudentID,
		email,
		userId,
		int(params.Student.RollNumber),
		int(params.Student.Class),
		params.Student.Section,
		termID,
		createdBy,
	)
	studentEntity.FirstName = &params.Student.FirstName
	studentEntity.LastName = &params.Student.LastName
	return studentEntity
}
