// Code generated by go-swagger; DO NOT EDIT.

package operations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/loads"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/runtime/security"
	"github.com/go-openapi/spec"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"

	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/restapi/operations/auto"
	"eddyowl-backend/restapi/operations/folder"
	"eddyowl-backend/restapi/operations/institute"
	"eddyowl-backend/restapi/operations/instructor"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/restapi/operations/subject"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/restapi/operations/term"
	"eddyowl-backend/restapi/operations/user"
)

// NewEddyOwlCoreAPIAPI creates a new EddyOwlCoreAPI instance
func NewEddyOwlCoreAPIAPI(spec *loads.Document) *EddyOwlCoreAPIAPI {
	return &EddyOwlCoreAPIAPI{
		handlers:            make(map[string]map[string]http.Handler),
		formats:             strfmt.Default,
		defaultConsumes:     "application/json",
		defaultProduces:     "application/json",
		customConsumers:     make(map[string]runtime.Consumer),
		customProducers:     make(map[string]runtime.Producer),
		PreServerShutdown:   func() {},
		ServerShutdown:      func() {},
		spec:                spec,
		useSwaggerUI:        false,
		ServeError:          errors.ServeError,
		BasicAuthenticator:  security.BasicAuth,
		APIKeyAuthenticator: security.APIKeyAuth,
		BearerAuthenticator: security.BearerAuth,

		JSONConsumer: runtime.JSONConsumer(),

		JSONProducer: runtime.JSONProducer(),

		InstructorAddInstructorByInstituteIDHandler: instructor.AddInstructorByInstituteIDHandlerFunc(func(params instructor.AddInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation instructor.AddInstructorByInstituteID has not yet been implemented")
		}),
		AssignmentAddTopicsToAssignmentHandler: assignment.AddTopicsToAssignmentHandlerFunc(func(params assignment.AddTopicsToAssignmentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.AddTopicsToAssignment has not yet been implemented")
		}),
		AutoAutoAddRubricHandler: auto.AutoAddRubricHandlerFunc(func(params auto.AutoAddRubricParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation auto.AutoAddRubric has not yet been implemented")
		}),
		AutoAutoCreateAssignmentHandler: auto.AutoCreateAssignmentHandlerFunc(func(params auto.AutoCreateAssignmentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation auto.AutoCreateAssignment has not yet been implemented")
		}),
		AutoAutoFileRubricHandler: auto.AutoFileRubricHandlerFunc(func(params auto.AutoFileRubricParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation auto.AutoFileRubric has not yet been implemented")
		}),
		SubmissionBulkCreateSubmissionHandler: submission.BulkCreateSubmissionHandlerFunc(func(params submission.BulkCreateSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.BulkCreateSubmission has not yet been implemented")
		}),
		AssignmentCreateAssignmentHandler: assignment.CreateAssignmentHandlerFunc(func(params assignment.CreateAssignmentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.CreateAssignment has not yet been implemented")
		}),
		FolderCreateFolderHandler: folder.CreateFolderHandlerFunc(func(params folder.CreateFolderParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation folder.CreateFolder has not yet been implemented")
		}),
		InstituteCreateInstituteHandler: institute.CreateInstituteHandlerFunc(func(params institute.CreateInstituteParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation institute.CreateInstitute has not yet been implemented")
		}),
		StudentCreateNewStudentHandler: student.CreateNewStudentHandlerFunc(func(params student.CreateNewStudentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.CreateNewStudent has not yet been implemented")
		}),
		TermCreateNewTermHandler: term.CreateNewTermHandlerFunc(func(params term.CreateNewTermParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation term.CreateNewTerm has not yet been implemented")
		}),
		SubmissionCreateSubmissionHandler: submission.CreateSubmissionHandlerFunc(func(params submission.CreateSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.CreateSubmission has not yet been implemented")
		}),
		AssignmentDeleteAssignmentByIDHandler: assignment.DeleteAssignmentByIDHandlerFunc(func(params assignment.DeleteAssignmentByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.DeleteAssignmentByID has not yet been implemented")
		}),
		FolderDeleteFolderHandler: folder.DeleteFolderHandlerFunc(func(params folder.DeleteFolderParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation folder.DeleteFolder has not yet been implemented")
		}),
		InstituteDeleteInstituteByIDHandler: institute.DeleteInstituteByIDHandlerFunc(func(params institute.DeleteInstituteByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation institute.DeleteInstituteByID has not yet been implemented")
		}),
		InstructorDeleteInstructorByEmailHandler: instructor.DeleteInstructorByEmailHandlerFunc(func(params instructor.DeleteInstructorByEmailParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation instructor.DeleteInstructorByEmail has not yet been implemented")
		}),
		StudentDeleteStudentByIDHandler: student.DeleteStudentByIDHandlerFunc(func(params student.DeleteStudentByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.DeleteStudentByID has not yet been implemented")
		}),
		SubmissionDeleteSubmissionHandler: submission.DeleteSubmissionHandlerFunc(func(params submission.DeleteSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.DeleteSubmission has not yet been implemented")
		}),
		TermDeleteTermByIDHandler: term.DeleteTermByIDHandlerFunc(func(params term.DeleteTermByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation term.DeleteTermByID has not yet been implemented")
		}),
		AssignmentEditAssignmentHandler: assignment.EditAssignmentHandlerFunc(func(params assignment.EditAssignmentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.EditAssignment has not yet been implemented")
		}),
		InstituteEditInstituteHandler: institute.EditInstituteHandlerFunc(func(params institute.EditInstituteParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation institute.EditInstitute has not yet been implemented")
		}),
		InstructorEditInstructorByInstituteIDHandler: instructor.EditInstructorByInstituteIDHandlerFunc(func(params instructor.EditInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation instructor.EditInstructorByInstituteID has not yet been implemented")
		}),
		StudentEditStudentHandler: student.EditStudentHandlerFunc(func(params student.EditStudentParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.EditStudent has not yet been implemented")
		}),
		SubmissionEditSubmissionHandler: submission.EditSubmissionHandlerFunc(func(params submission.EditSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.EditSubmission has not yet been implemented")
		}),
		TermEditTermHandler: term.EditTermHandlerFunc(func(params term.EditTermParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation term.EditTerm has not yet been implemented")
		}),
		AssignmentGetAllAssignmentsHandler: assignment.GetAllAssignmentsHandlerFunc(func(params assignment.GetAllAssignmentsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.GetAllAssignments has not yet been implemented")
		}),
		FolderGetAllFoldersHandler: folder.GetAllFoldersHandlerFunc(func(params folder.GetAllFoldersParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation folder.GetAllFolders has not yet been implemented")
		}),
		StatsGetAllStatsHandler: stats.GetAllStatsHandlerFunc(func(params stats.GetAllStatsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetAllStats has not yet been implemented")
		}),
		StudentGetAllStudentsHandler: student.GetAllStudentsHandlerFunc(func(params student.GetAllStudentsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.GetAllStudents has not yet been implemented")
		}),
		SubmissionGetAllSubmissionsHandler: submission.GetAllSubmissionsHandlerFunc(func(params submission.GetAllSubmissionsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.GetAllSubmissions has not yet been implemented")
		}),
		TermGetAllTermsHandler: term.GetAllTermsHandlerFunc(func(params term.GetAllTermsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation term.GetAllTerms has not yet been implemented")
		}),
		AssignmentGetAssignmentByIDHandler: assignment.GetAssignmentByIDHandlerFunc(func(params assignment.GetAssignmentByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation assignment.GetAssignmentByID has not yet been implemented")
		}),
		StatsGetClassAssessmentsBySectionsHandler: stats.GetClassAssessmentsBySectionsHandlerFunc(func(params stats.GetClassAssessmentsBySectionsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassAssessmentsBySections has not yet been implemented")
		}),
		StatsGetClassAssessmentsBySubjectHandler: stats.GetClassAssessmentsBySubjectHandlerFunc(func(params stats.GetClassAssessmentsBySubjectParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassAssessmentsBySubject has not yet been implemented")
		}),
		StatsGetClassAssessmentsBySubjectMonthlyHandler: stats.GetClassAssessmentsBySubjectMonthlyHandlerFunc(func(params stats.GetClassAssessmentsBySubjectMonthlyParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassAssessmentsBySubjectMonthly has not yet been implemented")
		}),
		StatsGetClassDetailsHandler: stats.GetClassDetailsHandlerFunc(func(params stats.GetClassDetailsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassDetails has not yet been implemented")
		}),
		StatsGetClassStatsHandler: stats.GetClassStatsHandlerFunc(func(params stats.GetClassStatsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassStats has not yet been implemented")
		}),
		StatsGetClassStudentsBySectionsHandler: stats.GetClassStudentsBySectionsHandlerFunc(func(params stats.GetClassStudentsBySectionsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassStudentsBySections has not yet been implemented")
		}),
		StatsGetClassTopPerformingStudentsHandler: stats.GetClassTopPerformingStudentsHandlerFunc(func(params stats.GetClassTopPerformingStudentsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetClassTopPerformingStudents has not yet been implemented")
		}),
		StatsGetGradedSubmissionPerformanceHandler: stats.GetGradedSubmissionPerformanceHandlerFunc(func(params stats.GetGradedSubmissionPerformanceParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetGradedSubmissionPerformance has not yet been implemented")
		}),
		InstituteGetInstituteByIDHandler: institute.GetInstituteByIDHandlerFunc(func(params institute.GetInstituteByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation institute.GetInstituteByID has not yet been implemented")
		}),
		InstructorGetInstructorByInstituteIDHandler: instructor.GetInstructorByInstituteIDHandlerFunc(func(params instructor.GetInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation instructor.GetInstructorByInstituteID has not yet been implemented")
		}),
		StatsGetMonthlyAssessmentsHandler: stats.GetMonthlyAssessmentsHandlerFunc(func(params stats.GetMonthlyAssessmentsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetMonthlyAssessments has not yet been implemented")
		}),
		StudentGetStudentByEmailHandler: student.GetStudentByEmailHandlerFunc(func(params student.GetStudentByEmailParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.GetStudentByEmail has not yet been implemented")
		}),
		StudentGetStudentByIDHandler: student.GetStudentByIDHandlerFunc(func(params student.GetStudentByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation student.GetStudentByID has not yet been implemented")
		}),
		StatsGetStudentOverallStatsHandler: stats.GetStudentOverallStatsHandlerFunc(func(params stats.GetStudentOverallStatsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetStudentOverallStats has not yet been implemented")
		}),
		SubjectGetSubjectsHandler: subject.GetSubjectsHandlerFunc(func(params subject.GetSubjectsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation subject.GetSubjects has not yet been implemented")
		}),
		SubmissionGetSubmissionHandler: submission.GetSubmissionHandlerFunc(func(params submission.GetSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.GetSubmission has not yet been implemented")
		}),
		TermGetTermByIDHandler: term.GetTermByIDHandlerFunc(func(params term.GetTermByIDParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation term.GetTermByID has not yet been implemented")
		}),
		UserGetUserRolesHandler: user.GetUserRolesHandlerFunc(func(params user.GetUserRolesParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation user.GetUserRoles has not yet been implemented")
		}),
		StatsGetWeeklyAssessmentsHandler: stats.GetWeeklyAssessmentsHandlerFunc(func(params stats.GetWeeklyAssessmentsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation stats.GetWeeklyAssessments has not yet been implemented")
		}),
		SubmissionPublishAllSubmissionsHandler: submission.PublishAllSubmissionsHandlerFunc(func(params submission.PublishAllSubmissionsParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.PublishAllSubmissions has not yet been implemented")
		}),
		SubmissionPublishSubmissionHandler: submission.PublishSubmissionHandlerFunc(func(params submission.PublishSubmissionParams, principal interface{}) middleware.Responder {
			return middleware.NotImplemented("operation submission.PublishSubmission has not yet been implemented")
		}),

		EddyowlOktaAuth: func(token string, scopes []string) (interface{}, error) {
			return nil, errors.NotImplemented("oauth2 bearer auth (eddyowl-okta) has not yet been implemented")
		},
		// default authorizer is authorized meaning no requests are blocked
		APIAuthorizer: security.Authorized(),
	}
}

/*EddyOwlCoreAPIAPI [TO DO] */
type EddyOwlCoreAPIAPI struct {
	spec            *loads.Document
	context         *middleware.Context
	handlers        map[string]map[string]http.Handler
	formats         strfmt.Registry
	customConsumers map[string]runtime.Consumer
	customProducers map[string]runtime.Producer
	defaultConsumes string
	defaultProduces string
	Middleware      func(middleware.Builder) http.Handler
	useSwaggerUI    bool

	// BasicAuthenticator generates a runtime.Authenticator from the supplied basic auth function.
	// It has a default implementation in the security package, however you can replace it for your particular usage.
	BasicAuthenticator func(security.UserPassAuthentication) runtime.Authenticator

	// APIKeyAuthenticator generates a runtime.Authenticator from the supplied token auth function.
	// It has a default implementation in the security package, however you can replace it for your particular usage.
	APIKeyAuthenticator func(string, string, security.TokenAuthentication) runtime.Authenticator

	// BearerAuthenticator generates a runtime.Authenticator from the supplied bearer token auth function.
	// It has a default implementation in the security package, however you can replace it for your particular usage.
	BearerAuthenticator func(string, security.ScopedTokenAuthentication) runtime.Authenticator

	// JSONConsumer registers a consumer for the following mime types:
	//   - application/json
	JSONConsumer runtime.Consumer

	// JSONProducer registers a producer for the following mime types:
	//   - application/json
	JSONProducer runtime.Producer

	// EddyowlOktaAuth registers a function that takes an access token and a collection of required scopes and returns a principal
	// it performs authentication based on an oauth2 bearer token provided in the request
	EddyowlOktaAuth func(string, []string) (interface{}, error)

	// APIAuthorizer provides access control (ACL/RBAC/ABAC) by providing access to the request and authenticated principal
	APIAuthorizer runtime.Authorizer

	// InstructorAddInstructorByInstituteIDHandler sets the operation handler for the add instructor by institute Id operation
	InstructorAddInstructorByInstituteIDHandler instructor.AddInstructorByInstituteIDHandler
	// AssignmentAddTopicsToAssignmentHandler sets the operation handler for the add topics to assignment operation
	AssignmentAddTopicsToAssignmentHandler assignment.AddTopicsToAssignmentHandler
	// AutoAutoAddRubricHandler sets the operation handler for the auto add rubric operation
	AutoAutoAddRubricHandler auto.AutoAddRubricHandler
	// AutoAutoCreateAssignmentHandler sets the operation handler for the auto create assignment operation
	AutoAutoCreateAssignmentHandler auto.AutoCreateAssignmentHandler
	// AutoAutoFileRubricHandler sets the operation handler for the auto file rubric operation
	AutoAutoFileRubricHandler auto.AutoFileRubricHandler
	// SubmissionBulkCreateSubmissionHandler sets the operation handler for the bulk create submission operation
	SubmissionBulkCreateSubmissionHandler submission.BulkCreateSubmissionHandler
	// AssignmentCreateAssignmentHandler sets the operation handler for the create assignment operation
	AssignmentCreateAssignmentHandler assignment.CreateAssignmentHandler
	// FolderCreateFolderHandler sets the operation handler for the create folder operation
	FolderCreateFolderHandler folder.CreateFolderHandler
	// InstituteCreateInstituteHandler sets the operation handler for the create institute operation
	InstituteCreateInstituteHandler institute.CreateInstituteHandler
	// StudentCreateNewStudentHandler sets the operation handler for the create new student operation
	StudentCreateNewStudentHandler student.CreateNewStudentHandler
	// TermCreateNewTermHandler sets the operation handler for the create new term operation
	TermCreateNewTermHandler term.CreateNewTermHandler
	// SubmissionCreateSubmissionHandler sets the operation handler for the create submission operation
	SubmissionCreateSubmissionHandler submission.CreateSubmissionHandler
	// AssignmentDeleteAssignmentByIDHandler sets the operation handler for the delete assignment by ID operation
	AssignmentDeleteAssignmentByIDHandler assignment.DeleteAssignmentByIDHandler
	// FolderDeleteFolderHandler sets the operation handler for the delete folder operation
	FolderDeleteFolderHandler folder.DeleteFolderHandler
	// InstituteDeleteInstituteByIDHandler sets the operation handler for the delete institute by Id operation
	InstituteDeleteInstituteByIDHandler institute.DeleteInstituteByIDHandler
	// InstructorDeleteInstructorByEmailHandler sets the operation handler for the delete instructor by email operation
	InstructorDeleteInstructorByEmailHandler instructor.DeleteInstructorByEmailHandler
	// StudentDeleteStudentByIDHandler sets the operation handler for the delete student by Id operation
	StudentDeleteStudentByIDHandler student.DeleteStudentByIDHandler
	// SubmissionDeleteSubmissionHandler sets the operation handler for the delete submission operation
	SubmissionDeleteSubmissionHandler submission.DeleteSubmissionHandler
	// TermDeleteTermByIDHandler sets the operation handler for the delete term by Id operation
	TermDeleteTermByIDHandler term.DeleteTermByIDHandler
	// AssignmentEditAssignmentHandler sets the operation handler for the edit assignment operation
	AssignmentEditAssignmentHandler assignment.EditAssignmentHandler
	// InstituteEditInstituteHandler sets the operation handler for the edit institute operation
	InstituteEditInstituteHandler institute.EditInstituteHandler
	// InstructorEditInstructorByInstituteIDHandler sets the operation handler for the edit instructor by institute Id operation
	InstructorEditInstructorByInstituteIDHandler instructor.EditInstructorByInstituteIDHandler
	// StudentEditStudentHandler sets the operation handler for the edit student operation
	StudentEditStudentHandler student.EditStudentHandler
	// SubmissionEditSubmissionHandler sets the operation handler for the edit submission operation
	SubmissionEditSubmissionHandler submission.EditSubmissionHandler
	// TermEditTermHandler sets the operation handler for the edit term operation
	TermEditTermHandler term.EditTermHandler
	// AssignmentGetAllAssignmentsHandler sets the operation handler for the get all assignments operation
	AssignmentGetAllAssignmentsHandler assignment.GetAllAssignmentsHandler
	// FolderGetAllFoldersHandler sets the operation handler for the get all folders operation
	FolderGetAllFoldersHandler folder.GetAllFoldersHandler
	// StatsGetAllStatsHandler sets the operation handler for the get all stats operation
	StatsGetAllStatsHandler stats.GetAllStatsHandler
	// StudentGetAllStudentsHandler sets the operation handler for the get all students operation
	StudentGetAllStudentsHandler student.GetAllStudentsHandler
	// SubmissionGetAllSubmissionsHandler sets the operation handler for the get all submissions operation
	SubmissionGetAllSubmissionsHandler submission.GetAllSubmissionsHandler
	// TermGetAllTermsHandler sets the operation handler for the get all terms operation
	TermGetAllTermsHandler term.GetAllTermsHandler
	// AssignmentGetAssignmentByIDHandler sets the operation handler for the get assignment by ID operation
	AssignmentGetAssignmentByIDHandler assignment.GetAssignmentByIDHandler
	// StatsGetClassAssessmentsBySectionsHandler sets the operation handler for the get class assessments by sections operation
	StatsGetClassAssessmentsBySectionsHandler stats.GetClassAssessmentsBySectionsHandler
	// StatsGetClassAssessmentsBySubjectHandler sets the operation handler for the get class assessments by subject operation
	StatsGetClassAssessmentsBySubjectHandler stats.GetClassAssessmentsBySubjectHandler
	// StatsGetClassAssessmentsBySubjectMonthlyHandler sets the operation handler for the get class assessments by subject monthly operation
	StatsGetClassAssessmentsBySubjectMonthlyHandler stats.GetClassAssessmentsBySubjectMonthlyHandler
	// StatsGetClassDetailsHandler sets the operation handler for the get class details operation
	StatsGetClassDetailsHandler stats.GetClassDetailsHandler
	// StatsGetClassStatsHandler sets the operation handler for the get class stats operation
	StatsGetClassStatsHandler stats.GetClassStatsHandler
	// StatsGetClassStudentsBySectionsHandler sets the operation handler for the get class students by sections operation
	StatsGetClassStudentsBySectionsHandler stats.GetClassStudentsBySectionsHandler
	// StatsGetClassTopPerformingStudentsHandler sets the operation handler for the get class top performing students operation
	StatsGetClassTopPerformingStudentsHandler stats.GetClassTopPerformingStudentsHandler
	// StatsGetGradedSubmissionPerformanceHandler sets the operation handler for the get graded submission performance operation
	StatsGetGradedSubmissionPerformanceHandler stats.GetGradedSubmissionPerformanceHandler
	// InstituteGetInstituteByIDHandler sets the operation handler for the get institute by Id operation
	InstituteGetInstituteByIDHandler institute.GetInstituteByIDHandler
	// InstructorGetInstructorByInstituteIDHandler sets the operation handler for the get instructor by institute Id operation
	InstructorGetInstructorByInstituteIDHandler instructor.GetInstructorByInstituteIDHandler
	// StatsGetMonthlyAssessmentsHandler sets the operation handler for the get monthly assessments operation
	StatsGetMonthlyAssessmentsHandler stats.GetMonthlyAssessmentsHandler
	// StudentGetStudentByEmailHandler sets the operation handler for the get student by email operation
	StudentGetStudentByEmailHandler student.GetStudentByEmailHandler
	// StudentGetStudentByIDHandler sets the operation handler for the get student by Id operation
	StudentGetStudentByIDHandler student.GetStudentByIDHandler
	// StatsGetStudentOverallStatsHandler sets the operation handler for the get student overall stats operation
	StatsGetStudentOverallStatsHandler stats.GetStudentOverallStatsHandler
	// SubjectGetSubjectsHandler sets the operation handler for the get subjects operation
	SubjectGetSubjectsHandler subject.GetSubjectsHandler
	// SubmissionGetSubmissionHandler sets the operation handler for the get submission operation
	SubmissionGetSubmissionHandler submission.GetSubmissionHandler
	// TermGetTermByIDHandler sets the operation handler for the get term by Id operation
	TermGetTermByIDHandler term.GetTermByIDHandler
	// UserGetUserRolesHandler sets the operation handler for the get user roles operation
	UserGetUserRolesHandler user.GetUserRolesHandler
	// StatsGetWeeklyAssessmentsHandler sets the operation handler for the get weekly assessments operation
	StatsGetWeeklyAssessmentsHandler stats.GetWeeklyAssessmentsHandler
	// SubmissionPublishAllSubmissionsHandler sets the operation handler for the publish all submissions operation
	SubmissionPublishAllSubmissionsHandler submission.PublishAllSubmissionsHandler
	// SubmissionPublishSubmissionHandler sets the operation handler for the publish submission operation
	SubmissionPublishSubmissionHandler submission.PublishSubmissionHandler

	// ServeError is called when an error is received, there is a default handler
	// but you can set your own with this
	ServeError func(http.ResponseWriter, *http.Request, error)

	// PreServerShutdown is called before the HTTP(S) server is shutdown
	// This allows for custom functions to get executed before the HTTP(S) server stops accepting traffic
	PreServerShutdown func()

	// ServerShutdown is called when the HTTP(S) server is shut down and done
	// handling all active connections and does not accept connections any more
	ServerShutdown func()

	// Custom command line argument groups with their descriptions
	CommandLineOptionsGroups []swag.CommandLineOptionsGroup

	// User defined logger function.
	Logger func(string, ...interface{})
}

// UseRedoc for documentation at /docs
func (o *EddyOwlCoreAPIAPI) UseRedoc() {
	o.useSwaggerUI = false
}

// UseSwaggerUI for documentation at /docs
func (o *EddyOwlCoreAPIAPI) UseSwaggerUI() {
	o.useSwaggerUI = true
}

// SetDefaultProduces sets the default produces media type
func (o *EddyOwlCoreAPIAPI) SetDefaultProduces(mediaType string) {
	o.defaultProduces = mediaType
}

// SetDefaultConsumes returns the default consumes media type
func (o *EddyOwlCoreAPIAPI) SetDefaultConsumes(mediaType string) {
	o.defaultConsumes = mediaType
}

// SetSpec sets a spec that will be served for the clients.
func (o *EddyOwlCoreAPIAPI) SetSpec(spec *loads.Document) {
	o.spec = spec
}

// DefaultProduces returns the default produces media type
func (o *EddyOwlCoreAPIAPI) DefaultProduces() string {
	return o.defaultProduces
}

// DefaultConsumes returns the default consumes media type
func (o *EddyOwlCoreAPIAPI) DefaultConsumes() string {
	return o.defaultConsumes
}

// Formats returns the registered string formats
func (o *EddyOwlCoreAPIAPI) Formats() strfmt.Registry {
	return o.formats
}

// RegisterFormat registers a custom format validator
func (o *EddyOwlCoreAPIAPI) RegisterFormat(name string, format strfmt.Format, validator strfmt.Validator) {
	o.formats.Add(name, format, validator)
}

// Validate validates the registrations in the EddyOwlCoreAPIAPI
func (o *EddyOwlCoreAPIAPI) Validate() error {
	var unregistered []string

	if o.JSONConsumer == nil {
		unregistered = append(unregistered, "JSONConsumer")
	}

	if o.JSONProducer == nil {
		unregistered = append(unregistered, "JSONProducer")
	}

	if o.EddyowlOktaAuth == nil {
		unregistered = append(unregistered, "EddyowlOktaAuth")
	}

	if o.InstructorAddInstructorByInstituteIDHandler == nil {
		unregistered = append(unregistered, "instructor.AddInstructorByInstituteIDHandler")
	}
	if o.AssignmentAddTopicsToAssignmentHandler == nil {
		unregistered = append(unregistered, "assignment.AddTopicsToAssignmentHandler")
	}
	if o.AutoAutoAddRubricHandler == nil {
		unregistered = append(unregistered, "auto.AutoAddRubricHandler")
	}
	if o.AutoAutoCreateAssignmentHandler == nil {
		unregistered = append(unregistered, "auto.AutoCreateAssignmentHandler")
	}
	if o.AutoAutoFileRubricHandler == nil {
		unregistered = append(unregistered, "auto.AutoFileRubricHandler")
	}
	if o.SubmissionBulkCreateSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.BulkCreateSubmissionHandler")
	}
	if o.AssignmentCreateAssignmentHandler == nil {
		unregistered = append(unregistered, "assignment.CreateAssignmentHandler")
	}
	if o.FolderCreateFolderHandler == nil {
		unregistered = append(unregistered, "folder.CreateFolderHandler")
	}
	if o.InstituteCreateInstituteHandler == nil {
		unregistered = append(unregistered, "institute.CreateInstituteHandler")
	}
	if o.StudentCreateNewStudentHandler == nil {
		unregistered = append(unregistered, "student.CreateNewStudentHandler")
	}
	if o.TermCreateNewTermHandler == nil {
		unregistered = append(unregistered, "term.CreateNewTermHandler")
	}
	if o.SubmissionCreateSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.CreateSubmissionHandler")
	}
	if o.AssignmentDeleteAssignmentByIDHandler == nil {
		unregistered = append(unregistered, "assignment.DeleteAssignmentByIDHandler")
	}
	if o.FolderDeleteFolderHandler == nil {
		unregistered = append(unregistered, "folder.DeleteFolderHandler")
	}
	if o.InstituteDeleteInstituteByIDHandler == nil {
		unregistered = append(unregistered, "institute.DeleteInstituteByIDHandler")
	}
	if o.InstructorDeleteInstructorByEmailHandler == nil {
		unregistered = append(unregistered, "instructor.DeleteInstructorByEmailHandler")
	}
	if o.StudentDeleteStudentByIDHandler == nil {
		unregistered = append(unregistered, "student.DeleteStudentByIDHandler")
	}
	if o.SubmissionDeleteSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.DeleteSubmissionHandler")
	}
	if o.TermDeleteTermByIDHandler == nil {
		unregistered = append(unregistered, "term.DeleteTermByIDHandler")
	}
	if o.AssignmentEditAssignmentHandler == nil {
		unregistered = append(unregistered, "assignment.EditAssignmentHandler")
	}
	if o.InstituteEditInstituteHandler == nil {
		unregistered = append(unregistered, "institute.EditInstituteHandler")
	}
	if o.InstructorEditInstructorByInstituteIDHandler == nil {
		unregistered = append(unregistered, "instructor.EditInstructorByInstituteIDHandler")
	}
	if o.StudentEditStudentHandler == nil {
		unregistered = append(unregistered, "student.EditStudentHandler")
	}
	if o.SubmissionEditSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.EditSubmissionHandler")
	}
	if o.TermEditTermHandler == nil {
		unregistered = append(unregistered, "term.EditTermHandler")
	}
	if o.AssignmentGetAllAssignmentsHandler == nil {
		unregistered = append(unregistered, "assignment.GetAllAssignmentsHandler")
	}
	if o.FolderGetAllFoldersHandler == nil {
		unregistered = append(unregistered, "folder.GetAllFoldersHandler")
	}
	if o.StatsGetAllStatsHandler == nil {
		unregistered = append(unregistered, "stats.GetAllStatsHandler")
	}
	if o.StudentGetAllStudentsHandler == nil {
		unregistered = append(unregistered, "student.GetAllStudentsHandler")
	}
	if o.SubmissionGetAllSubmissionsHandler == nil {
		unregistered = append(unregistered, "submission.GetAllSubmissionsHandler")
	}
	if o.TermGetAllTermsHandler == nil {
		unregistered = append(unregistered, "term.GetAllTermsHandler")
	}
	if o.AssignmentGetAssignmentByIDHandler == nil {
		unregistered = append(unregistered, "assignment.GetAssignmentByIDHandler")
	}
	if o.StatsGetClassAssessmentsBySectionsHandler == nil {
		unregistered = append(unregistered, "stats.GetClassAssessmentsBySectionsHandler")
	}
	if o.StatsGetClassAssessmentsBySubjectHandler == nil {
		unregistered = append(unregistered, "stats.GetClassAssessmentsBySubjectHandler")
	}
	if o.StatsGetClassAssessmentsBySubjectMonthlyHandler == nil {
		unregistered = append(unregistered, "stats.GetClassAssessmentsBySubjectMonthlyHandler")
	}
	if o.StatsGetClassDetailsHandler == nil {
		unregistered = append(unregistered, "stats.GetClassDetailsHandler")
	}
	if o.StatsGetClassStatsHandler == nil {
		unregistered = append(unregistered, "stats.GetClassStatsHandler")
	}
	if o.StatsGetClassStudentsBySectionsHandler == nil {
		unregistered = append(unregistered, "stats.GetClassStudentsBySectionsHandler")
	}
	if o.StatsGetClassTopPerformingStudentsHandler == nil {
		unregistered = append(unregistered, "stats.GetClassTopPerformingStudentsHandler")
	}
	if o.StatsGetGradedSubmissionPerformanceHandler == nil {
		unregistered = append(unregistered, "stats.GetGradedSubmissionPerformanceHandler")
	}
	if o.InstituteGetInstituteByIDHandler == nil {
		unregistered = append(unregistered, "institute.GetInstituteByIDHandler")
	}
	if o.InstructorGetInstructorByInstituteIDHandler == nil {
		unregistered = append(unregistered, "instructor.GetInstructorByInstituteIDHandler")
	}
	if o.StatsGetMonthlyAssessmentsHandler == nil {
		unregistered = append(unregistered, "stats.GetMonthlyAssessmentsHandler")
	}
	if o.StudentGetStudentByEmailHandler == nil {
		unregistered = append(unregistered, "student.GetStudentByEmailHandler")
	}
	if o.StudentGetStudentByIDHandler == nil {
		unregistered = append(unregistered, "student.GetStudentByIDHandler")
	}
	if o.StatsGetStudentOverallStatsHandler == nil {
		unregistered = append(unregistered, "stats.GetStudentOverallStatsHandler")
	}
	if o.SubjectGetSubjectsHandler == nil {
		unregistered = append(unregistered, "subject.GetSubjectsHandler")
	}
	if o.SubmissionGetSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.GetSubmissionHandler")
	}
	if o.TermGetTermByIDHandler == nil {
		unregistered = append(unregistered, "term.GetTermByIDHandler")
	}
	if o.UserGetUserRolesHandler == nil {
		unregistered = append(unregistered, "user.GetUserRolesHandler")
	}
	if o.StatsGetWeeklyAssessmentsHandler == nil {
		unregistered = append(unregistered, "stats.GetWeeklyAssessmentsHandler")
	}
	if o.SubmissionPublishAllSubmissionsHandler == nil {
		unregistered = append(unregistered, "submission.PublishAllSubmissionsHandler")
	}
	if o.SubmissionPublishSubmissionHandler == nil {
		unregistered = append(unregistered, "submission.PublishSubmissionHandler")
	}

	if len(unregistered) > 0 {
		return fmt.Errorf("missing registration: %s", strings.Join(unregistered, ", "))
	}

	return nil
}

// ServeErrorFor gets a error handler for a given operation id
func (o *EddyOwlCoreAPIAPI) ServeErrorFor(operationID string) func(http.ResponseWriter, *http.Request, error) {
	return o.ServeError
}

// AuthenticatorsFor gets the authenticators for the specified security schemes
func (o *EddyOwlCoreAPIAPI) AuthenticatorsFor(schemes map[string]spec.SecurityScheme) map[string]runtime.Authenticator {
	result := make(map[string]runtime.Authenticator)
	for name := range schemes {
		switch name {
		case "eddyowl-okta":
			result[name] = o.BearerAuthenticator(name, o.EddyowlOktaAuth)

		}
	}
	return result
}

// Authorizer returns the registered authorizer
func (o *EddyOwlCoreAPIAPI) Authorizer() runtime.Authorizer {
	return o.APIAuthorizer
}

// ConsumersFor gets the consumers for the specified media types.
// MIME type parameters are ignored here.
func (o *EddyOwlCoreAPIAPI) ConsumersFor(mediaTypes []string) map[string]runtime.Consumer {
	result := make(map[string]runtime.Consumer, len(mediaTypes))
	for _, mt := range mediaTypes {
		switch mt {
		case "application/json":
			result["application/json"] = o.JSONConsumer
		}

		if c, ok := o.customConsumers[mt]; ok {
			result[mt] = c
		}
	}
	return result
}

// ProducersFor gets the producers for the specified media types.
// MIME type parameters are ignored here.
func (o *EddyOwlCoreAPIAPI) ProducersFor(mediaTypes []string) map[string]runtime.Producer {
	result := make(map[string]runtime.Producer, len(mediaTypes))
	for _, mt := range mediaTypes {
		switch mt {
		case "application/json":
			result["application/json"] = o.JSONProducer
		}

		if p, ok := o.customProducers[mt]; ok {
			result[mt] = p
		}
	}
	return result
}

// HandlerFor gets a http.Handler for the provided operation method and path
func (o *EddyOwlCoreAPIAPI) HandlerFor(method, path string) (http.Handler, bool) {
	if o.handlers == nil {
		return nil, false
	}
	um := strings.ToUpper(method)
	if _, ok := o.handlers[um]; !ok {
		return nil, false
	}
	if path == "/" {
		path = ""
	}
	h, ok := o.handlers[um][path]
	return h, ok
}

// Context returns the middleware context for the eddy owl core API API
func (o *EddyOwlCoreAPIAPI) Context() *middleware.Context {
	if o.context == nil {
		o.context = middleware.NewRoutableContext(o.spec, o, nil)
	}

	return o.context
}

func (o *EddyOwlCoreAPIAPI) initHandlerCache() {
	o.Context() // don't care about the result, just that the initialization happened
	if o.handlers == nil {
		o.handlers = make(map[string]map[string]http.Handler)
	}

	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/instructor"] = instructor.NewAddInstructorByInstituteID(o.context, o.InstructorAddInstructorByInstituteIDHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/assignment/{assignmentId}/topics"] = assignment.NewAddTopicsToAssignment(o.context, o.AssignmentAddTopicsToAssignmentHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/auto/rubric"] = auto.NewAutoAddRubric(o.context, o.AutoAutoAddRubricHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/assignment/auto"] = auto.NewAutoCreateAssignment(o.context, o.AutoAutoCreateAssignmentHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/upload/rubric"] = auto.NewAutoFileRubric(o.context, o.AutoAutoFileRubricHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}/submission/bulk"] = submission.NewBulkCreateSubmission(o.context, o.SubmissionBulkCreateSubmissionHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/assignment"] = assignment.NewCreateAssignment(o.context, o.AssignmentCreateAssignmentHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/folder"] = folder.NewCreateFolder(o.context, o.FolderCreateFolderHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute"] = institute.NewCreateInstitute(o.context, o.InstituteCreateInstituteHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/student"] = student.NewCreateNewStudent(o.context, o.StudentCreateNewStudentHandler)
	if o.handlers["POST"] == nil {
		o.handlers["POST"] = make(map[string]http.Handler)
	}
	o.handlers["POST"]["/institute/{instituteId}/terms"] = term.NewCreateNewTerm(o.context, o.TermCreateNewTermHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission"] = submission.NewCreateSubmission(o.context, o.SubmissionCreateSubmissionHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/assignment/{assignmentId}"] = assignment.NewDeleteAssignmentByID(o.context, o.AssignmentDeleteAssignmentByIDHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/folder/{folderId}"] = folder.NewDeleteFolder(o.context, o.FolderDeleteFolderHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}"] = institute.NewDeleteInstituteByID(o.context, o.InstituteDeleteInstituteByIDHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/instructor"] = instructor.NewDeleteInstructorByEmail(o.context, o.InstructorDeleteInstructorByEmailHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/student/{studentId}"] = student.NewDeleteStudentByID(o.context, o.StudentDeleteStudentByIDHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission"] = submission.NewDeleteSubmission(o.context, o.SubmissionDeleteSubmissionHandler)
	if o.handlers["DELETE"] == nil {
		o.handlers["DELETE"] = make(map[string]http.Handler)
	}
	o.handlers["DELETE"]["/institute/{instituteId}/term/{termId}"] = term.NewDeleteTermByID(o.context, o.TermDeleteTermByIDHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}"] = assignment.NewEditAssignment(o.context, o.AssignmentEditAssignmentHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}"] = institute.NewEditInstitute(o.context, o.InstituteEditInstituteHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/instructor"] = instructor.NewEditInstructorByInstituteID(o.context, o.InstructorEditInstructorByInstituteIDHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/student"] = student.NewEditStudent(o.context, o.StudentEditStudentHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/edit/submission"] = submission.NewEditSubmission(o.context, o.SubmissionEditSubmissionHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/terms"] = term.NewEditTerm(o.context, o.TermEditTermHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assignment"] = assignment.NewGetAllAssignments(o.context, o.AssignmentGetAllAssignmentsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/folder"] = folder.NewGetAllFolders(o.context, o.FolderGetAllFoldersHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/allstats"] = stats.NewGetAllStats(o.context, o.StatsGetAllStatsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/student"] = student.NewGetAllStudents(o.context, o.StudentGetAllStudentsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/submissions"] = submission.NewGetAllSubmissions(o.context, o.SubmissionGetAllSubmissionsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/terms"] = term.NewGetAllTerms(o.context, o.TermGetAllTermsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assignment/{assignmentId}"] = assignment.NewGetAssignmentByID(o.context, o.AssignmentGetAssignmentByIDHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/assessmentsbysections"] = stats.NewGetClassAssessmentsBySections(o.context, o.StatsGetClassAssessmentsBySectionsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/assessmentsbysub"] = stats.NewGetClassAssessmentsBySubject(o.context, o.StatsGetClassAssessmentsBySubjectHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/assessmentsbysub/monthly"] = stats.NewGetClassAssessmentsBySubjectMonthly(o.context, o.StatsGetClassAssessmentsBySubjectMonthlyHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/classdetails"] = stats.NewGetClassDetails(o.context, o.StatsGetClassDetailsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/allstats"] = stats.NewGetClassStats(o.context, o.StatsGetClassStatsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/studentsbysections"] = stats.NewGetClassStudentsBySections(o.context, o.StatsGetClassStudentsBySectionsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/class/{class}/topstudents"] = stats.NewGetClassTopPerformingStudents(o.context, o.StatsGetClassTopPerformingStudentsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/performance"] = stats.NewGetGradedSubmissionPerformance(o.context, o.StatsGetGradedSubmissionPerformanceHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}"] = institute.NewGetInstituteByID(o.context, o.InstituteGetInstituteByIDHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/instructor"] = instructor.NewGetInstructorByInstituteID(o.context, o.InstructorGetInstructorByInstituteIDHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assessments/monthly"] = stats.NewGetMonthlyAssessments(o.context, o.StatsGetMonthlyAssessmentsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/student/email/{email}"] = student.NewGetStudentByEmail(o.context, o.StudentGetStudentByEmailHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/student/{studentId}"] = student.NewGetStudentByID(o.context, o.StudentGetStudentByIDHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/student/{studentId}/studentOverallStats"] = stats.NewGetStudentOverallStats(o.context, o.StatsGetStudentOverallStatsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/subject/grade/{grade}"] = subject.NewGetSubjects(o.context, o.SubjectGetSubjectsHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission"] = submission.NewGetSubmission(o.context, o.SubmissionGetSubmissionHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/term/{termId}"] = term.NewGetTermByID(o.context, o.TermGetTermByIDHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/userRoles"] = user.NewGetUserRoles(o.context, o.UserGetUserRolesHandler)
	if o.handlers["GET"] == nil {
		o.handlers["GET"] = make(map[string]http.Handler)
	}
	o.handlers["GET"]["/institute/{instituteId}/assessments/weekly"] = stats.NewGetWeeklyAssessments(o.context, o.StatsGetWeeklyAssessmentsHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}/publish"] = submission.NewPublishAllSubmissions(o.context, o.SubmissionPublishAllSubmissionsHandler)
	if o.handlers["PUT"] == nil {
		o.handlers["PUT"] = make(map[string]http.Handler)
	}
	o.handlers["PUT"]["/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/publish"] = submission.NewPublishSubmission(o.context, o.SubmissionPublishSubmissionHandler)
}

// Serve creates a http handler to serve the API over HTTP
// can be used directly in http.ListenAndServe(":8000", api.Serve(nil))
func (o *EddyOwlCoreAPIAPI) Serve(builder middleware.Builder) http.Handler {
	o.Init()

	if o.Middleware != nil {
		return o.Middleware(builder)
	}
	if o.useSwaggerUI {
		return o.context.APIHandlerSwaggerUI(builder)
	}
	return o.context.APIHandler(builder)
}

// Init allows you to just initialize the handler cache, you can then recompose the middleware as you see fit
func (o *EddyOwlCoreAPIAPI) Init() {
	if len(o.handlers) == 0 {
		o.initHandlerCache()
	}
}

// RegisterConsumer allows you to add (or override) a consumer for a media type.
func (o *EddyOwlCoreAPIAPI) RegisterConsumer(mediaType string, consumer runtime.Consumer) {
	o.customConsumers[mediaType] = consumer
}

// RegisterProducer allows you to add (or override) a producer for a media type.
func (o *EddyOwlCoreAPIAPI) RegisterProducer(mediaType string, producer runtime.Producer) {
	o.customProducers[mediaType] = producer
}

// AddMiddlewareFor adds a http middleware to existing handler
func (o *EddyOwlCoreAPIAPI) AddMiddlewareFor(method, path string, builder middleware.Builder) {
	um := strings.ToUpper(method)
	if path == "/" {
		path = ""
	}
	o.Init()
	if h, ok := o.handlers[um][path]; ok {
		o.handlers[um][path] = builder(h)
	}
}
