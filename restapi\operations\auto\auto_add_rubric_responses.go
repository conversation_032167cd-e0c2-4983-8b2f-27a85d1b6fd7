// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// AutoAddRubricOKCode is the HTTP code returned for type AutoAddRubricOK
const AutoAddRubricOKCode int = 200

/*
AutoAddRubricOK Successful operation

swagger:response autoAddRubricOK
*/
type AutoAddRubricOK struct {

	/*
	  In: Body
	*/
	Payload *models.QuestionList `json:"body,omitempty"`
}

// NewAutoAddRubricOK creates AutoAddRubricOK with default headers values
func NewAutoAddRubricOK() *AutoAddRubricOK {

	return &AutoAddRubricOK{}
}

// WithPayload adds the payload to the auto add rubric o k response
func (o *AutoAddRubricOK) WithPayload(payload *models.QuestionList) *AutoAddRubricOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric o k response
func (o *AutoAddRubricOK) SetPayload(payload *models.QuestionList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// AutoAddRubricBadRequestCode is the HTTP code returned for type AutoAddRubricBadRequest
const AutoAddRubricBadRequestCode int = 400

/*
AutoAddRubricBadRequest Bad Request

swagger:response autoAddRubricBadRequest
*/
type AutoAddRubricBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricBadRequest creates AutoAddRubricBadRequest with default headers values
func NewAutoAddRubricBadRequest() *AutoAddRubricBadRequest {

	return &AutoAddRubricBadRequest{}
}

// WithPayload adds the payload to the auto add rubric bad request response
func (o *AutoAddRubricBadRequest) WithPayload(payload models.ErrorResponse) *AutoAddRubricBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric bad request response
func (o *AutoAddRubricBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoAddRubricForbiddenCode is the HTTP code returned for type AutoAddRubricForbidden
const AutoAddRubricForbiddenCode int = 403

/*
AutoAddRubricForbidden Forbidden

swagger:response autoAddRubricForbidden
*/
type AutoAddRubricForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricForbidden creates AutoAddRubricForbidden with default headers values
func NewAutoAddRubricForbidden() *AutoAddRubricForbidden {

	return &AutoAddRubricForbidden{}
}

// WithPayload adds the payload to the auto add rubric forbidden response
func (o *AutoAddRubricForbidden) WithPayload(payload models.ErrorResponse) *AutoAddRubricForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric forbidden response
func (o *AutoAddRubricForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoAddRubricNotFoundCode is the HTTP code returned for type AutoAddRubricNotFound
const AutoAddRubricNotFoundCode int = 404

/*
AutoAddRubricNotFound Not Found

swagger:response autoAddRubricNotFound
*/
type AutoAddRubricNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricNotFound creates AutoAddRubricNotFound with default headers values
func NewAutoAddRubricNotFound() *AutoAddRubricNotFound {

	return &AutoAddRubricNotFound{}
}

// WithPayload adds the payload to the auto add rubric not found response
func (o *AutoAddRubricNotFound) WithPayload(payload models.ErrorResponse) *AutoAddRubricNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric not found response
func (o *AutoAddRubricNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoAddRubricTooManyRequestsCode is the HTTP code returned for type AutoAddRubricTooManyRequests
const AutoAddRubricTooManyRequestsCode int = 429

/*
AutoAddRubricTooManyRequests Too Many Requests

swagger:response autoAddRubricTooManyRequests
*/
type AutoAddRubricTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricTooManyRequests creates AutoAddRubricTooManyRequests with default headers values
func NewAutoAddRubricTooManyRequests() *AutoAddRubricTooManyRequests {

	return &AutoAddRubricTooManyRequests{}
}

// WithPayload adds the payload to the auto add rubric too many requests response
func (o *AutoAddRubricTooManyRequests) WithPayload(payload models.ErrorResponse) *AutoAddRubricTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric too many requests response
func (o *AutoAddRubricTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoAddRubricInternalServerErrorCode is the HTTP code returned for type AutoAddRubricInternalServerError
const AutoAddRubricInternalServerErrorCode int = 500

/*
AutoAddRubricInternalServerError Internal Server Error

swagger:response autoAddRubricInternalServerError
*/
type AutoAddRubricInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricInternalServerError creates AutoAddRubricInternalServerError with default headers values
func NewAutoAddRubricInternalServerError() *AutoAddRubricInternalServerError {

	return &AutoAddRubricInternalServerError{}
}

// WithPayload adds the payload to the auto add rubric internal server error response
func (o *AutoAddRubricInternalServerError) WithPayload(payload models.ErrorResponse) *AutoAddRubricInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric internal server error response
func (o *AutoAddRubricInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoAddRubricServiceUnavailableCode is the HTTP code returned for type AutoAddRubricServiceUnavailable
const AutoAddRubricServiceUnavailableCode int = 503

/*
AutoAddRubricServiceUnavailable Service Unvailable

swagger:response autoAddRubricServiceUnavailable
*/
type AutoAddRubricServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoAddRubricServiceUnavailable creates AutoAddRubricServiceUnavailable with default headers values
func NewAutoAddRubricServiceUnavailable() *AutoAddRubricServiceUnavailable {

	return &AutoAddRubricServiceUnavailable{}
}

// WithPayload adds the payload to the auto add rubric service unavailable response
func (o *AutoAddRubricServiceUnavailable) WithPayload(payload models.ErrorResponse) *AutoAddRubricServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto add rubric service unavailable response
func (o *AutoAddRubricServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoAddRubricServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
