package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/instructor"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getInstructorByInstituteIDImpl struct {
	instructorProvider data_providers.InstructorProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewGetInstructorByInstituteIDHandler(instructorProvider data_providers.InstructorProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) instructor.GetInstructorByInstituteIDHandler {
	return &getInstructorByInstituteIDImpl{instructorProvider: instructorProvider, userRolesProvider: userRolesProvider, tracer: tracer}
}

func (impl *getInstructorByInstituteIDImpl) Handle(params instructor.GetInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetInstructorByInstituteIDHandler")
	defer span.End()

	principal = principal.(string)
	if params.InstituteID == constants.EmptyString {
		return instructor.NewGetInstructorByInstituteIDBadRequest().WithPayload("Invalid InstituteID")
	}

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return instructor.NewGetInstructorByInstituteIDForbidden().WithPayload("Unauthorized")
	}

	instituteID := &params.InstituteID
	eddyOwlInstructorList, err := impl.instructorProvider.GetAll(ctx, instituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch instructors")
		return instructor.NewGetInstructorByInstituteIDInternalServerError().WithPayload("Unable to fetch Instructors")
	}

	instructorList := make([]*models.Instructor, 0, len(*eddyOwlInstructorList))
	for _, instructor := range *eddyOwlInstructorList {
		instructorList = append(instructorList, impl.convertInstructorEntityToModel(&instructor))
	}

	return instructor.NewGetInstructorByInstituteIDOK().WithPayload(instructorList)
}

func (impl *getInstructorByInstituteIDImpl) convertInstructorEntityToModel(instructor *entities.Instructor) *models.Instructor {
	if instructor == nil {
		return nil
	}

	instructorResp := &models.Instructor{
		Role: int32(instructor.Role),
	}

	if instructor.Email != nil {
		instructorResp.Email = *instructor.Email
	}
	if instructor.FirstName != nil {
		instructorResp.FirstName = *instructor.FirstName
	}
	if instructor.LastName != nil {
		instructorResp.LastName = *instructor.LastName
	}

	return instructorResp
}
