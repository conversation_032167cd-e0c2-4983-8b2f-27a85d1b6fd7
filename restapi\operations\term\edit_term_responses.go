// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditTermOKCode is the HTTP code returned for type EditTermOK
const EditTermOKCode int = 200

/*
EditTermOK Successful operation

swagger:response editTermOK
*/
type EditTermOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditTermOK creates EditTermOK with default headers values
func NewEditTermOK() *EditTermOK {

	return &EditTermOK{}
}

// WithPayload adds the payload to the edit term o k response
func (o *EditTermOK) WithPayload(payload *models.SuccessResponse) *EditTermOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term o k response
func (o *EditTermOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditTermBadRequestCode is the HTTP code returned for type EditTermBadRequest
const EditTermBadRequestCode int = 400

/*
EditTermBadRequest Bad Request

swagger:response editTermBadRequest
*/
type EditTermBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermBadRequest creates EditTermBadRequest with default headers values
func NewEditTermBadRequest() *EditTermBadRequest {

	return &EditTermBadRequest{}
}

// WithPayload adds the payload to the edit term bad request response
func (o *EditTermBadRequest) WithPayload(payload models.ErrorResponse) *EditTermBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term bad request response
func (o *EditTermBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditTermForbiddenCode is the HTTP code returned for type EditTermForbidden
const EditTermForbiddenCode int = 403

/*
EditTermForbidden Forbidden

swagger:response editTermForbidden
*/
type EditTermForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermForbidden creates EditTermForbidden with default headers values
func NewEditTermForbidden() *EditTermForbidden {

	return &EditTermForbidden{}
}

// WithPayload adds the payload to the edit term forbidden response
func (o *EditTermForbidden) WithPayload(payload models.ErrorResponse) *EditTermForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term forbidden response
func (o *EditTermForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditTermNotFoundCode is the HTTP code returned for type EditTermNotFound
const EditTermNotFoundCode int = 404

/*
EditTermNotFound Not Found

swagger:response editTermNotFound
*/
type EditTermNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermNotFound creates EditTermNotFound with default headers values
func NewEditTermNotFound() *EditTermNotFound {

	return &EditTermNotFound{}
}

// WithPayload adds the payload to the edit term not found response
func (o *EditTermNotFound) WithPayload(payload models.ErrorResponse) *EditTermNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term not found response
func (o *EditTermNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditTermTooManyRequestsCode is the HTTP code returned for type EditTermTooManyRequests
const EditTermTooManyRequestsCode int = 429

/*
EditTermTooManyRequests Too Many Requests

swagger:response editTermTooManyRequests
*/
type EditTermTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermTooManyRequests creates EditTermTooManyRequests with default headers values
func NewEditTermTooManyRequests() *EditTermTooManyRequests {

	return &EditTermTooManyRequests{}
}

// WithPayload adds the payload to the edit term too many requests response
func (o *EditTermTooManyRequests) WithPayload(payload models.ErrorResponse) *EditTermTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term too many requests response
func (o *EditTermTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditTermInternalServerErrorCode is the HTTP code returned for type EditTermInternalServerError
const EditTermInternalServerErrorCode int = 500

/*
EditTermInternalServerError Internal Server Error

swagger:response editTermInternalServerError
*/
type EditTermInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermInternalServerError creates EditTermInternalServerError with default headers values
func NewEditTermInternalServerError() *EditTermInternalServerError {

	return &EditTermInternalServerError{}
}

// WithPayload adds the payload to the edit term internal server error response
func (o *EditTermInternalServerError) WithPayload(payload models.ErrorResponse) *EditTermInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term internal server error response
func (o *EditTermInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditTermServiceUnavailableCode is the HTTP code returned for type EditTermServiceUnavailable
const EditTermServiceUnavailableCode int = 503

/*
EditTermServiceUnavailable Service Unvailable

swagger:response editTermServiceUnavailable
*/
type EditTermServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditTermServiceUnavailable creates EditTermServiceUnavailable with default headers values
func NewEditTermServiceUnavailable() *EditTermServiceUnavailable {

	return &EditTermServiceUnavailable{}
}

// WithPayload adds the payload to the edit term service unavailable response
func (o *EditTermServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditTermServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit term service unavailable response
func (o *EditTermServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditTermServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
