// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllTermsOKCode is the HTTP code returned for type GetAllTermsOK
const GetAllTermsOKCode int = 200

/*
GetAllTermsOK Successful operation

swagger:response getAllTermsOK
*/
type GetAllTermsOK struct {

	/*
	  In: Body
	*/
	Payload models.TermList `json:"body,omitempty"`
}

// NewGetAllTermsOK creates GetAllTermsOK with default headers values
func NewGetAllTermsOK() *GetAllTermsOK {

	return &GetAllTermsOK{}
}

// WithPayload adds the payload to the get all terms o k response
func (o *GetAllTermsOK) WithPayload(payload models.TermList) *GetAllTermsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms o k response
func (o *GetAllTermsOK) SetPayload(payload models.TermList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.TermList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsBadRequestCode is the HTTP code returned for type GetAllTermsBadRequest
const GetAllTermsBadRequestCode int = 400

/*
GetAllTermsBadRequest Bad Request

swagger:response getAllTermsBadRequest
*/
type GetAllTermsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsBadRequest creates GetAllTermsBadRequest with default headers values
func NewGetAllTermsBadRequest() *GetAllTermsBadRequest {

	return &GetAllTermsBadRequest{}
}

// WithPayload adds the payload to the get all terms bad request response
func (o *GetAllTermsBadRequest) WithPayload(payload models.ErrorResponse) *GetAllTermsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms bad request response
func (o *GetAllTermsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsForbiddenCode is the HTTP code returned for type GetAllTermsForbidden
const GetAllTermsForbiddenCode int = 403

/*
GetAllTermsForbidden Forbidden

swagger:response getAllTermsForbidden
*/
type GetAllTermsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsForbidden creates GetAllTermsForbidden with default headers values
func NewGetAllTermsForbidden() *GetAllTermsForbidden {

	return &GetAllTermsForbidden{}
}

// WithPayload adds the payload to the get all terms forbidden response
func (o *GetAllTermsForbidden) WithPayload(payload models.ErrorResponse) *GetAllTermsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms forbidden response
func (o *GetAllTermsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsNotFoundCode is the HTTP code returned for type GetAllTermsNotFound
const GetAllTermsNotFoundCode int = 404

/*
GetAllTermsNotFound Not Found

swagger:response getAllTermsNotFound
*/
type GetAllTermsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsNotFound creates GetAllTermsNotFound with default headers values
func NewGetAllTermsNotFound() *GetAllTermsNotFound {

	return &GetAllTermsNotFound{}
}

// WithPayload adds the payload to the get all terms not found response
func (o *GetAllTermsNotFound) WithPayload(payload models.ErrorResponse) *GetAllTermsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms not found response
func (o *GetAllTermsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsTooManyRequestsCode is the HTTP code returned for type GetAllTermsTooManyRequests
const GetAllTermsTooManyRequestsCode int = 429

/*
GetAllTermsTooManyRequests Too Many Requests

swagger:response getAllTermsTooManyRequests
*/
type GetAllTermsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsTooManyRequests creates GetAllTermsTooManyRequests with default headers values
func NewGetAllTermsTooManyRequests() *GetAllTermsTooManyRequests {

	return &GetAllTermsTooManyRequests{}
}

// WithPayload adds the payload to the get all terms too many requests response
func (o *GetAllTermsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAllTermsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms too many requests response
func (o *GetAllTermsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsInternalServerErrorCode is the HTTP code returned for type GetAllTermsInternalServerError
const GetAllTermsInternalServerErrorCode int = 500

/*
GetAllTermsInternalServerError Internal Server Error

swagger:response getAllTermsInternalServerError
*/
type GetAllTermsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsInternalServerError creates GetAllTermsInternalServerError with default headers values
func NewGetAllTermsInternalServerError() *GetAllTermsInternalServerError {

	return &GetAllTermsInternalServerError{}
}

// WithPayload adds the payload to the get all terms internal server error response
func (o *GetAllTermsInternalServerError) WithPayload(payload models.ErrorResponse) *GetAllTermsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms internal server error response
func (o *GetAllTermsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllTermsServiceUnavailableCode is the HTTP code returned for type GetAllTermsServiceUnavailable
const GetAllTermsServiceUnavailableCode int = 503

/*
GetAllTermsServiceUnavailable Service Unvailable

swagger:response getAllTermsServiceUnavailable
*/
type GetAllTermsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllTermsServiceUnavailable creates GetAllTermsServiceUnavailable with default headers values
func NewGetAllTermsServiceUnavailable() *GetAllTermsServiceUnavailable {

	return &GetAllTermsServiceUnavailable{}
}

// WithPayload adds the payload to the get all terms service unavailable response
func (o *GetAllTermsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAllTermsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all terms service unavailable response
func (o *GetAllTermsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllTermsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
