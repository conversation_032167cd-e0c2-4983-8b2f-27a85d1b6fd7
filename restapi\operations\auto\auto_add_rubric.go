// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// AutoAddRubricHandlerFunc turns a function with the right signature into a auto add rubric handler
type AutoAddRubricHandlerFunc func(AutoAddRubricParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn AutoAddRubricHandlerFunc) Handle(params AutoAddRubricParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// AutoAddRubricHandler interface for that can handle valid auto add rubric params
type AutoAddRubricHandler interface {
	Handle(AutoAddRubricParams, interface{}) middleware.Responder
}

// NewAutoAddRubric creates a new http.Handler for the auto add rubric operation
func NewAutoAddRubric(ctx *middleware.Context, handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) *AutoAddRubric {
	return &AutoAddRubric{Context: ctx, Handler: handler}
}

/*
	AutoAddRubric swagger:route PUT /institute/{instituteId}/auto/rubric auto autoAddRubric

# Auto Add Rubric

Auto Add Rubric
*/
type AutoAddRubric struct {
	Context *middleware.Context
	Handler AutoAddRubricHandler
}

func (o *AutoAddRubric) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewAutoAddRubricParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
