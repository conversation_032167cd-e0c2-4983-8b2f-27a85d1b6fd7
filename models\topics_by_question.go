// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// TopicsByQuestion topics by question
//
// swagger:model TopicsByQuestion
type TopicsByQuestion struct {

	// question number
	QuestionNumber int32 `json:"questionNumber,omitempty"`

	// topics
	Topics []*ChapterTopics `json:"topics"`
}

// Validate validates this topics by question
func (m *TopicsByQuestion) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateTopics(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *TopicsByQuestion) validateTopics(formats strfmt.Registry) error {
	if swag.IsZero(m.Topics) { // not required
		return nil
	}

	for i := 0; i < len(m.Topics); i++ {
		if swag.IsZero(m.Topics[i]) { // not required
			continue
		}

		if m.Topics[i] != nil {
			if err := m.Topics[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this topics by question based on the context it is used
func (m *TopicsByQuestion) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateTopics(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *TopicsByQuestion) contextValidateTopics(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Topics); i++ {

		if m.Topics[i] != nil {

			if swag.IsZero(m.Topics[i]) { // not required
				return nil
			}

			if err := m.Topics[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *TopicsByQuestion) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *TopicsByQuestion) UnmarshalBinary(b []byte) error {
	var res TopicsByQuestion
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
