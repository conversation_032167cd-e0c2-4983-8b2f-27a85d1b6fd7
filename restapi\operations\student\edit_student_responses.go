// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditStudentOKCode is the HTTP code returned for type EditStudentOK
const EditStudentOKCode int = 200

/*
EditStudentOK Successful operation

swagger:response editStudentOK
*/
type EditStudentOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditStudentOK creates EditStudentOK with default headers values
func NewEditStudentOK() *EditStudentOK {

	return &EditStudentOK{}
}

// WithPayload adds the payload to the edit student o k response
func (o *EditStudentOK) WithPayload(payload *models.SuccessResponse) *EditStudentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student o k response
func (o *EditStudentOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditStudentBadRequestCode is the HTTP code returned for type EditStudentBadRequest
const EditStudentBadRequestCode int = 400

/*
EditStudentBadRequest Bad Request

swagger:response editStudentBadRequest
*/
type EditStudentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentBadRequest creates EditStudentBadRequest with default headers values
func NewEditStudentBadRequest() *EditStudentBadRequest {

	return &EditStudentBadRequest{}
}

// WithPayload adds the payload to the edit student bad request response
func (o *EditStudentBadRequest) WithPayload(payload models.ErrorResponse) *EditStudentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student bad request response
func (o *EditStudentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditStudentForbiddenCode is the HTTP code returned for type EditStudentForbidden
const EditStudentForbiddenCode int = 403

/*
EditStudentForbidden Forbidden

swagger:response editStudentForbidden
*/
type EditStudentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentForbidden creates EditStudentForbidden with default headers values
func NewEditStudentForbidden() *EditStudentForbidden {

	return &EditStudentForbidden{}
}

// WithPayload adds the payload to the edit student forbidden response
func (o *EditStudentForbidden) WithPayload(payload models.ErrorResponse) *EditStudentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student forbidden response
func (o *EditStudentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditStudentNotFoundCode is the HTTP code returned for type EditStudentNotFound
const EditStudentNotFoundCode int = 404

/*
EditStudentNotFound Not Found

swagger:response editStudentNotFound
*/
type EditStudentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentNotFound creates EditStudentNotFound with default headers values
func NewEditStudentNotFound() *EditStudentNotFound {

	return &EditStudentNotFound{}
}

// WithPayload adds the payload to the edit student not found response
func (o *EditStudentNotFound) WithPayload(payload models.ErrorResponse) *EditStudentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student not found response
func (o *EditStudentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditStudentTooManyRequestsCode is the HTTP code returned for type EditStudentTooManyRequests
const EditStudentTooManyRequestsCode int = 429

/*
EditStudentTooManyRequests Too Many Requests

swagger:response editStudentTooManyRequests
*/
type EditStudentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentTooManyRequests creates EditStudentTooManyRequests with default headers values
func NewEditStudentTooManyRequests() *EditStudentTooManyRequests {

	return &EditStudentTooManyRequests{}
}

// WithPayload adds the payload to the edit student too many requests response
func (o *EditStudentTooManyRequests) WithPayload(payload models.ErrorResponse) *EditStudentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student too many requests response
func (o *EditStudentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditStudentInternalServerErrorCode is the HTTP code returned for type EditStudentInternalServerError
const EditStudentInternalServerErrorCode int = 500

/*
EditStudentInternalServerError Internal Server Error

swagger:response editStudentInternalServerError
*/
type EditStudentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentInternalServerError creates EditStudentInternalServerError with default headers values
func NewEditStudentInternalServerError() *EditStudentInternalServerError {

	return &EditStudentInternalServerError{}
}

// WithPayload adds the payload to the edit student internal server error response
func (o *EditStudentInternalServerError) WithPayload(payload models.ErrorResponse) *EditStudentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student internal server error response
func (o *EditStudentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditStudentServiceUnavailableCode is the HTTP code returned for type EditStudentServiceUnavailable
const EditStudentServiceUnavailableCode int = 503

/*
EditStudentServiceUnavailable Service Unvailable

swagger:response editStudentServiceUnavailable
*/
type EditStudentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditStudentServiceUnavailable creates EditStudentServiceUnavailable with default headers values
func NewEditStudentServiceUnavailable() *EditStudentServiceUnavailable {

	return &EditStudentServiceUnavailable{}
}

// WithPayload adds the payload to the edit student service unavailable response
func (o *EditStudentServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditStudentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit student service unavailable response
func (o *EditStudentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditStudentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
