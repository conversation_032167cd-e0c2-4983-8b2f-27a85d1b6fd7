// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetSubmissionOKCode is the HTTP code returned for type GetSubmissionOK
const GetSubmissionOKCode int = 200

/*
GetSubmissionOK Successful operation

swagger:response getSubmissionOK
*/
type GetSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.StudentSubmission `json:"body,omitempty"`
}

// NewGetSubmissionOK creates GetSubmissionOK with default headers values
func NewGetSubmissionOK() *GetSubmissionOK {

	return &GetSubmissionOK{}
}

// WithPayload adds the payload to the get submission o k response
func (o *GetSubmissionOK) WithPayload(payload *models.StudentSubmission) *GetSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission o k response
func (o *GetSubmissionOK) SetPayload(payload *models.StudentSubmission) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetSubmissionBadRequestCode is the HTTP code returned for type GetSubmissionBadRequest
const GetSubmissionBadRequestCode int = 400

/*
GetSubmissionBadRequest Bad Request

swagger:response getSubmissionBadRequest
*/
type GetSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionBadRequest creates GetSubmissionBadRequest with default headers values
func NewGetSubmissionBadRequest() *GetSubmissionBadRequest {

	return &GetSubmissionBadRequest{}
}

// WithPayload adds the payload to the get submission bad request response
func (o *GetSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *GetSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission bad request response
func (o *GetSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubmissionForbiddenCode is the HTTP code returned for type GetSubmissionForbidden
const GetSubmissionForbiddenCode int = 403

/*
GetSubmissionForbidden Forbidden

swagger:response getSubmissionForbidden
*/
type GetSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionForbidden creates GetSubmissionForbidden with default headers values
func NewGetSubmissionForbidden() *GetSubmissionForbidden {

	return &GetSubmissionForbidden{}
}

// WithPayload adds the payload to the get submission forbidden response
func (o *GetSubmissionForbidden) WithPayload(payload models.ErrorResponse) *GetSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission forbidden response
func (o *GetSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubmissionNotFoundCode is the HTTP code returned for type GetSubmissionNotFound
const GetSubmissionNotFoundCode int = 404

/*
GetSubmissionNotFound Not Found

swagger:response getSubmissionNotFound
*/
type GetSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionNotFound creates GetSubmissionNotFound with default headers values
func NewGetSubmissionNotFound() *GetSubmissionNotFound {

	return &GetSubmissionNotFound{}
}

// WithPayload adds the payload to the get submission not found response
func (o *GetSubmissionNotFound) WithPayload(payload models.ErrorResponse) *GetSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission not found response
func (o *GetSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubmissionTooManyRequestsCode is the HTTP code returned for type GetSubmissionTooManyRequests
const GetSubmissionTooManyRequestsCode int = 429

/*
GetSubmissionTooManyRequests Too Many Requests

swagger:response getSubmissionTooManyRequests
*/
type GetSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionTooManyRequests creates GetSubmissionTooManyRequests with default headers values
func NewGetSubmissionTooManyRequests() *GetSubmissionTooManyRequests {

	return &GetSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the get submission too many requests response
func (o *GetSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *GetSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission too many requests response
func (o *GetSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubmissionInternalServerErrorCode is the HTTP code returned for type GetSubmissionInternalServerError
const GetSubmissionInternalServerErrorCode int = 500

/*
GetSubmissionInternalServerError Internal Server Error

swagger:response getSubmissionInternalServerError
*/
type GetSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionInternalServerError creates GetSubmissionInternalServerError with default headers values
func NewGetSubmissionInternalServerError() *GetSubmissionInternalServerError {

	return &GetSubmissionInternalServerError{}
}

// WithPayload adds the payload to the get submission internal server error response
func (o *GetSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *GetSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission internal server error response
func (o *GetSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubmissionServiceUnavailableCode is the HTTP code returned for type GetSubmissionServiceUnavailable
const GetSubmissionServiceUnavailableCode int = 503

/*
GetSubmissionServiceUnavailable Service Unvailable

swagger:response getSubmissionServiceUnavailable
*/
type GetSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubmissionServiceUnavailable creates GetSubmissionServiceUnavailable with default headers values
func NewGetSubmissionServiceUnavailable() *GetSubmissionServiceUnavailable {

	return &GetSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the get submission service unavailable response
func (o *GetSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get submission service unavailable response
func (o *GetSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
