// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAssignmentByIDOKCode is the HTTP code returned for type GetAssignmentByIDOK
const GetAssignmentByIDOKCode int = 200

/*
GetAssignmentByIDOK Successful operation

swagger:response getAssignmentByIdOK
*/
type GetAssignmentByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.Assignment `json:"body,omitempty"`
}

// NewGetAssignmentByIDOK creates GetAssignmentByIDOK with default headers values
func NewGetAssignmentByIDOK() *GetAssignmentByIDOK {

	return &GetAssignmentByIDOK{}
}

// WithPayload adds the payload to the get assignment by Id o k response
func (o *GetAssignmentByIDOK) WithPayload(payload *models.Assignment) *GetAssignmentByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id o k response
func (o *GetAssignmentByIDOK) SetPayload(payload *models.Assignment) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetAssignmentByIDBadRequestCode is the HTTP code returned for type GetAssignmentByIDBadRequest
const GetAssignmentByIDBadRequestCode int = 400

/*
GetAssignmentByIDBadRequest Bad Request

swagger:response getAssignmentByIdBadRequest
*/
type GetAssignmentByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDBadRequest creates GetAssignmentByIDBadRequest with default headers values
func NewGetAssignmentByIDBadRequest() *GetAssignmentByIDBadRequest {

	return &GetAssignmentByIDBadRequest{}
}

// WithPayload adds the payload to the get assignment by Id bad request response
func (o *GetAssignmentByIDBadRequest) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id bad request response
func (o *GetAssignmentByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAssignmentByIDForbiddenCode is the HTTP code returned for type GetAssignmentByIDForbidden
const GetAssignmentByIDForbiddenCode int = 403

/*
GetAssignmentByIDForbidden Forbidden

swagger:response getAssignmentByIdForbidden
*/
type GetAssignmentByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDForbidden creates GetAssignmentByIDForbidden with default headers values
func NewGetAssignmentByIDForbidden() *GetAssignmentByIDForbidden {

	return &GetAssignmentByIDForbidden{}
}

// WithPayload adds the payload to the get assignment by Id forbidden response
func (o *GetAssignmentByIDForbidden) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id forbidden response
func (o *GetAssignmentByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAssignmentByIDNotFoundCode is the HTTP code returned for type GetAssignmentByIDNotFound
const GetAssignmentByIDNotFoundCode int = 404

/*
GetAssignmentByIDNotFound Not Found

swagger:response getAssignmentByIdNotFound
*/
type GetAssignmentByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDNotFound creates GetAssignmentByIDNotFound with default headers values
func NewGetAssignmentByIDNotFound() *GetAssignmentByIDNotFound {

	return &GetAssignmentByIDNotFound{}
}

// WithPayload adds the payload to the get assignment by Id not found response
func (o *GetAssignmentByIDNotFound) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id not found response
func (o *GetAssignmentByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAssignmentByIDTooManyRequestsCode is the HTTP code returned for type GetAssignmentByIDTooManyRequests
const GetAssignmentByIDTooManyRequestsCode int = 429

/*
GetAssignmentByIDTooManyRequests Too Many Requests

swagger:response getAssignmentByIdTooManyRequests
*/
type GetAssignmentByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDTooManyRequests creates GetAssignmentByIDTooManyRequests with default headers values
func NewGetAssignmentByIDTooManyRequests() *GetAssignmentByIDTooManyRequests {

	return &GetAssignmentByIDTooManyRequests{}
}

// WithPayload adds the payload to the get assignment by Id too many requests response
func (o *GetAssignmentByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id too many requests response
func (o *GetAssignmentByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAssignmentByIDInternalServerErrorCode is the HTTP code returned for type GetAssignmentByIDInternalServerError
const GetAssignmentByIDInternalServerErrorCode int = 500

/*
GetAssignmentByIDInternalServerError Internal Server Error

swagger:response getAssignmentByIdInternalServerError
*/
type GetAssignmentByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDInternalServerError creates GetAssignmentByIDInternalServerError with default headers values
func NewGetAssignmentByIDInternalServerError() *GetAssignmentByIDInternalServerError {

	return &GetAssignmentByIDInternalServerError{}
}

// WithPayload adds the payload to the get assignment by Id internal server error response
func (o *GetAssignmentByIDInternalServerError) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id internal server error response
func (o *GetAssignmentByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAssignmentByIDServiceUnavailableCode is the HTTP code returned for type GetAssignmentByIDServiceUnavailable
const GetAssignmentByIDServiceUnavailableCode int = 503

/*
GetAssignmentByIDServiceUnavailable Service Unvailable

swagger:response getAssignmentByIdServiceUnavailable
*/
type GetAssignmentByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAssignmentByIDServiceUnavailable creates GetAssignmentByIDServiceUnavailable with default headers values
func NewGetAssignmentByIDServiceUnavailable() *GetAssignmentByIDServiceUnavailable {

	return &GetAssignmentByIDServiceUnavailable{}
}

// WithPayload adds the payload to the get assignment by Id service unavailable response
func (o *GetAssignmentByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAssignmentByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get assignment by Id service unavailable response
func (o *GetAssignmentByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAssignmentByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
