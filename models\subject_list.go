// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
)

// SubjectList subject list
//
// swagger:model SubjectList
type SubjectList []string

// Validate validates this subject list
func (m SubjectList) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this subject list based on context it is used
func (m SubjectList) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}
