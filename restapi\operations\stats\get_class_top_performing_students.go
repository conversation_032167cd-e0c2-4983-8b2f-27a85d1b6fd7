// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassTopPerformingStudentsHandlerFunc turns a function with the right signature into a get class top performing students handler
type GetClassTopPerformingStudentsHandlerFunc func(GetClassTopPerformingStudentsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassTopPerformingStudentsHandlerFunc) Handle(params GetClassTopPerformingStudentsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassTopPerformingStudentsHandler interface for that can handle valid get class top performing students params
type GetClassTopPerformingStudentsHandler interface {
	Handle(GetClassTopPerformingStudentsParams, interface{}) middleware.Responder
}

// NewGetClassTopPerformingStudents creates a new http.Handler for the get class top performing students operation
func NewGetClassTopPerformingStudents(ctx *middleware.Context, handler GetClassTopPerformingStudentsHandler) *GetClassTopPerformingStudents {
	return &GetClassTopPerformingStudents{Context: ctx, Handler: handler}
}

/*
	GetClassTopPerformingStudents swagger:route GET /institute/{instituteId}/class/{class}/topstudents stats getClassTopPerformingStudents

# Get Class Top Performing Students

Get Class Top Performing Students
*/
type GetClassTopPerformingStudents struct {
	Context *middleware.Context
	Handler GetClassTopPerformingStudentsHandler
}

func (o *GetClassTopPerformingStudents) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassTopPerformingStudentsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
