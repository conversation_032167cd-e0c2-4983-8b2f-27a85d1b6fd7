db.createView("section_data_view", "submissions_view", [
  {
    $match: {
      created_at: { $ne: null },
    },
  },
  {
    $group: {
      _id: {
        institute_id: "$institute_id",
        term_id: "$term_id",
        grade: "$grade",
        section: "$student_section",
      },
      submission_count: { $sum: 1 },
    },
  },
  {
    $lookup: {
      from: "assignments",
      let: {
        inst_id: "$_id.institute_id",
        term_id: "$_id.term_id",
        grade: "$_id.grade",
        section: "$_id.section",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$inst_id"] },
                { $eq: ["$term_id", "$$term_id"] },
                { $eq: ["$grade", "$$grade"] },
                { $in: ["$$section", "$sections"] },
              ],
            },
          },
        },
      ],
      as: "matched_assignments",
    },
  },
  {
    $lookup: {
      from: "students",
      let: {
        inst_id: "$_id.institute_id",
        term_id: "$_id.term_id",
        grade: "$_id.grade",
        section: "$_id.section",
      },
      pipeline: [
        { $unwind: "$academic_history" },
        {
          $addFields: {
            term_id: {
              $toString: {
                $arrayElemAt: [{ $objectToArray: "$academic_history.k" }, 0],
              },
            },
            term_data: {
              $arrayElemAt: [{ $objectToArray: "$academic_history.v" }, 0],
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                "$$ROOT",
                {
                  term_id: {
                    $arrayElemAt: [
                      {
                        $map: {
                          input: { $objectToArray: "$academic_history" },
                          as: "entry",
                          in: "$$entry.k",
                        },
                      },
                      0,
                    ],
                  },
                  grade: {
                    $arrayElemAt: [
                      {
                        $map: {
                          input: { $objectToArray: "$academic_history" },
                          as: "entry",
                          in: "$$entry.v.grade",
                        },
                      },
                      0,
                    ],
                  },
                  section: {
                    $arrayElemAt: [
                      {
                        $map: {
                          input: { $objectToArray: "$academic_history" },
                          as: "entry",
                          in: "$$entry.v.section",
                        },
                      },
                      0,
                    ],
                  },
                },
              ],
            },
          },
        },
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$inst_id"] },
                { $eq: ["$term_id", "$$term_id"] },
                { $eq: ["$grade", "$$grade"] },
                { $eq: ["$section", "$$section"] },
              ],
            },
          },
        },
      ],
      as: "matched_students",
    },
  },
  {
    $addFields: {
      assignment_count: { $size: "$matched_assignments" },
      student_count: { $size: "$matched_students" },
    },
  },
  {
    $sort: {
      "_id.institute_id": 1,
      "_id.term_id": 1,
      "_id.grade": 1,
      "_id.section": 1,
    },
  },
  {
    $project: {
      _id: 1,
      submission_count: 1,
      assignment_count: 1,
      student_count: 1,
    },
  },
]);
