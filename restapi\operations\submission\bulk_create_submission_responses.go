// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// BulkCreateSubmissionOKCode is the HTTP code returned for type BulkCreateSubmissionOK
const BulkCreateSubmissionOKCode int = 200

/*
BulkCreateSubmissionOK Successful operation

swagger:response bulkCreateSubmissionOK
*/
type BulkCreateSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionOK creates BulkCreateSubmissionOK with default headers values
func NewBulkCreateSubmissionOK() *BulkCreateSubmissionOK {

	return &BulkCreateSubmissionOK{}
}

// WithPayload adds the payload to the bulk create submission o k response
func (o *BulkCreateSubmissionOK) WithPayload(payload *models.SuccessResponse) *BulkCreateSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission o k response
func (o *BulkCreateSubmissionOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// BulkCreateSubmissionBadRequestCode is the HTTP code returned for type BulkCreateSubmissionBadRequest
const BulkCreateSubmissionBadRequestCode int = 400

/*
BulkCreateSubmissionBadRequest Bad Request

swagger:response bulkCreateSubmissionBadRequest
*/
type BulkCreateSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionBadRequest creates BulkCreateSubmissionBadRequest with default headers values
func NewBulkCreateSubmissionBadRequest() *BulkCreateSubmissionBadRequest {

	return &BulkCreateSubmissionBadRequest{}
}

// WithPayload adds the payload to the bulk create submission bad request response
func (o *BulkCreateSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission bad request response
func (o *BulkCreateSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// BulkCreateSubmissionForbiddenCode is the HTTP code returned for type BulkCreateSubmissionForbidden
const BulkCreateSubmissionForbiddenCode int = 403

/*
BulkCreateSubmissionForbidden Forbidden

swagger:response bulkCreateSubmissionForbidden
*/
type BulkCreateSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionForbidden creates BulkCreateSubmissionForbidden with default headers values
func NewBulkCreateSubmissionForbidden() *BulkCreateSubmissionForbidden {

	return &BulkCreateSubmissionForbidden{}
}

// WithPayload adds the payload to the bulk create submission forbidden response
func (o *BulkCreateSubmissionForbidden) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission forbidden response
func (o *BulkCreateSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// BulkCreateSubmissionNotFoundCode is the HTTP code returned for type BulkCreateSubmissionNotFound
const BulkCreateSubmissionNotFoundCode int = 404

/*
BulkCreateSubmissionNotFound Not Found

swagger:response bulkCreateSubmissionNotFound
*/
type BulkCreateSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionNotFound creates BulkCreateSubmissionNotFound with default headers values
func NewBulkCreateSubmissionNotFound() *BulkCreateSubmissionNotFound {

	return &BulkCreateSubmissionNotFound{}
}

// WithPayload adds the payload to the bulk create submission not found response
func (o *BulkCreateSubmissionNotFound) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission not found response
func (o *BulkCreateSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// BulkCreateSubmissionTooManyRequestsCode is the HTTP code returned for type BulkCreateSubmissionTooManyRequests
const BulkCreateSubmissionTooManyRequestsCode int = 429

/*
BulkCreateSubmissionTooManyRequests Too Many Requests

swagger:response bulkCreateSubmissionTooManyRequests
*/
type BulkCreateSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionTooManyRequests creates BulkCreateSubmissionTooManyRequests with default headers values
func NewBulkCreateSubmissionTooManyRequests() *BulkCreateSubmissionTooManyRequests {

	return &BulkCreateSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the bulk create submission too many requests response
func (o *BulkCreateSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission too many requests response
func (o *BulkCreateSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// BulkCreateSubmissionInternalServerErrorCode is the HTTP code returned for type BulkCreateSubmissionInternalServerError
const BulkCreateSubmissionInternalServerErrorCode int = 500

/*
BulkCreateSubmissionInternalServerError Internal Server Error

swagger:response bulkCreateSubmissionInternalServerError
*/
type BulkCreateSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionInternalServerError creates BulkCreateSubmissionInternalServerError with default headers values
func NewBulkCreateSubmissionInternalServerError() *BulkCreateSubmissionInternalServerError {

	return &BulkCreateSubmissionInternalServerError{}
}

// WithPayload adds the payload to the bulk create submission internal server error response
func (o *BulkCreateSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission internal server error response
func (o *BulkCreateSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// BulkCreateSubmissionServiceUnavailableCode is the HTTP code returned for type BulkCreateSubmissionServiceUnavailable
const BulkCreateSubmissionServiceUnavailableCode int = 503

/*
BulkCreateSubmissionServiceUnavailable Service Unavailable

swagger:response bulkCreateSubmissionServiceUnavailable
*/
type BulkCreateSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewBulkCreateSubmissionServiceUnavailable creates BulkCreateSubmissionServiceUnavailable with default headers values
func NewBulkCreateSubmissionServiceUnavailable() *BulkCreateSubmissionServiceUnavailable {

	return &BulkCreateSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the bulk create submission service unavailable response
func (o *BulkCreateSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *BulkCreateSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the bulk create submission service unavailable response
func (o *BulkCreateSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *BulkCreateSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
