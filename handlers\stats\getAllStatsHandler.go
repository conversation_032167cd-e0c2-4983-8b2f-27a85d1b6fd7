package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getAllStatsImpl struct {
	statsProvider     data_providers.StatsProvider
	instituteProvider data_providers.InstituteProvider
	termProvider      data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetAllStatsHandler(
	statsProvider data_providers.StatsProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) stats.GetAllStatsHandler {
	return &getAllStatsImpl{
		statsProvider:     statsProvider,
		instituteProvider: instituteProvider,
		termProvider:      termProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *getAllStatsImpl) Handle(params stats.GetAllStatsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAllStatsHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return stats.NewGetAllStatsForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.getAllStatsValidator(ctx, params)
	if !valid {
		return validateResp
	}

	// Resolve current term if not specified
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		if errors.Is(err, utils.ErrTermNotFound) {
			return stats.NewGetAllStatsNotFound().WithPayload("Term not found")
		}
		return stats.NewGetAllStatsInternalServerError().WithPayload("Unable to resolve term")
	}
	// Get institute overall stats
	instituteStats, err := impl.statsProvider.GetInstituteOverallStats(ctx, params.InstituteID, termID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch institute overall stats")
		return stats.NewGetAllStatsInternalServerError().WithPayload("Failed to fetch stats")
	}

	// Construct response
	response := &models.AllStats{
		Students:                  int32(instituteStats.Students),
		Instructors:               int32(instituteStats.Instructors),
		Assessments:               int32(instituteStats.Assessments),
		OverallStudentPerformance: int32(instituteStats.AveragePercentageScore),
	}

	return stats.NewGetAllStatsOK().WithPayload(response)
}

func (impl *getAllStatsImpl) getAllStatsValidator(ctx context.Context, params stats.GetAllStatsParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(ctx, "getAllStatsValidator")
	defer span.End()

	if params.InstituteID == constants.EmptyString {
		return false, stats.NewGetAllStatsBadRequest().WithPayload("Invalid Institute ID")
	}

	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, stats.NewGetAllStatsBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Err(err).Msg("Failed to fetch institute")
		return false, stats.NewGetAllStatsInternalServerError().WithPayload("Unable to fetch institute")
	}

	return true, nil
}
