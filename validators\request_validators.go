package validators

// func AutoAddRubricValidator(params core.AutoAddRubricParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Questions.Class < 6 || params.Questions.Class > 12 || params.Questions.Subject == constants.EmptyString || len(params.Questions.Questions.QuestionList) == 0 {
// 		return false
// 	}
// 	return true
// }

// func GetSubjectBySubjectIDValidator(params core.GetSubjectBySubjectIDParams) bool {
// 	if params.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetChapterListBySubjectIDValidator(params core.GetChapterListBySubjectIDParams) bool {
// 	if params.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetClassStudentsBySectionsValidator(params core.GetClassStudentsBySectionsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func CreateSubjectValidator(param core.CreateSubjectParams) bool {
// 	if param.NewSubject.Name == constants.EmptyString {
// 		return false
// 	}
// 	if param.NewSubject.Class <= 0 || param.NewSubject.Class > 12 {
// 		return false
// 	}
// 	if param.NewSubject.Name == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteSubjectBySubjectIDValidator(params core.DeleteSubjectBySubjectIDParams) bool {
// 	if params.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteSubmissionValidator(params core.DeleteSubmissionParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func CreateChapterBySubjectIDValidator(param core.CreateChapterBySubjectIDParams) bool {
// 	if param.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	if param.NewChapter.Number <= 0 {
// 		return false
// 	}
// 	if param.NewChapter.Name == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }
// func DeleteChapterByChapterIDValidator(params core.DeleteChapterByChapterIDParams) bool {
// 	if params.ChapterID == constants.EmptyString || params.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetProgramListValidator(params core.GetProgramListParams) bool {
// 	return true
// }

// func CreateProgramValidator(params core.CreateProgramParams) bool {
// 	if params.NewProgram.Name == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteProgramByProgramIDValidator(params core.DeleteProgramByProgramIDParams) bool {
// 	if params.ProgramID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetTestTypeByIDValidator(params core.GetTestTypeByIDParams) bool {
// 	if params.TestTypeID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func CreateTestTypeValidator(params core.CreateTestTypeParams) bool {
// 	if params.NewTestType.Duration <= 0 {
// 		return false
// 	}
// 	if params.NewTestType.MaxQuestions <= 0 {
// 		return false
// 	}
// 	if params.NewTestType.TotalScore <= 0 {
// 		return false
// 	}
// 	if params.NewTestType.Name == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteTestTypeByIDValidator(params core.DeleteTestTypeByIDParams) bool {
// 	if params.TestTypeID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetTestValidator(params core.GetTestParams) bool {
// 	if params.SubjectID == constants.EmptyString {
// 		return false
// 	}
// 	if params.TestTypeID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func AddTopicsToTestValidator(params core.AddTopicsToTestParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	if params.TestID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GradeTestValidator(params core.GradeTestParams) bool {
// 	if params.TestID == constants.EmptyString {
// 		return false
// 	}
// 	if len(params.StudentTestSolution.Responses) <= 0 {
// 		return false
// 	}
// 	for _, response := range params.StudentTestSolution.Responses {
// 		if response.QuestionNumber <= 0 {
// 			return false
// 		}
// 	}
// 	return true
// }

// func CreateInstituteValidator(params core.CreateInstituteParams) bool {
// 	if params.NewInstitute.Name == constants.EmptyString || params.NewInstitute.ProgramID == constants.EmptyString {
// 		return false
// 	}
// 	if params.NewInstitute.Address == nil || params.NewInstitute.Address.AddressOne == constants.EmptyString || params.NewInstitute.Address.City == constants.EmptyString || params.NewInstitute.Address.Pincode == constants.EmptyString || params.NewInstitute.Address.State == constants.EmptyString {
// 		if params.NewInstitute.Address == constants.EmptyString {
// 			return false
// 		} else {
// 			return true
// 		}
// 	}
// 	return true
// }

// func GetSubjecyByClassValidator(params core.GetSubjectByClassParams) bool {
// 	if params.ClassNumber < 0 || params.ClassNumber > 12 {
// 		return false
// 	}
// 	return true
// }

// func CreateNewStudentValidator(params core.CreateNewStudentParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Student.FirstName == constants.EmptyString || params.Student.StudentID == constants.EmptyString || params.Student.RollNumber <= 0 || params.Student.Class <= 0 || params.Student.Section == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func EditStudentValidator(params core.EditStudentParams) bool {
// 	if params.Student.StudentID == constants.EmptyString || params.InstituteID == constants.EmptyString || params.Student.FirstName == constants.EmptyString || params.Student.StudentID == constants.EmptyString || params.Student.RollNumber <= 0 || params.Student.Class <= 0 || params.Student.Section == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetAllStudentsValidator(params core.GetAllStudentsParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetInstituteByIDValidator(params core.GetInstituteByIDParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetAllStatsValidator(params core.GetAllStatsParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func CreateNewTestValidator(params core.CreateNewTestParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Test.Class <= 0 || params.Test.Questions == nil || params.Test.SubjectName == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetAllTestsByInstituteIDValidator(params core.GetAllTestsByInstituteIDParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetStudentsToGradeValidator(params core.GetStudentsToGradeParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func AssignSectionsValidator(params core.AssignSectionsParams) bool {
// 	if params.TestID == constants.EmptyString || len(params.SectionList) == 0 {
// 		return false
// 	}
// 	return true
// }

// func AddInstructorByInstituteIDValidator(params core.AddInstructorByInstituteIDParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Instructor.Email == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetInstructorByInstituteIDValidator(params core.GetInstructorByInstituteIDParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetTestByTestIDValidator(params core.GetTestByTestIDParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func PutSolutionsValidator(params core.PutSolutionsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func PutSolutionsV2Validator(params core.PutSolutionsV2Params) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString || params.ZipFile == nil {
// 		return false
// 	}
// 	return true
// }

// func CreateNewTestAutoValidator(params core.CreateNewTestAutoParams) bool {
// 	if params.InstituteID == constants.EmptyString || len(params.Files.Files) <= 0 {
// 		return false
// 	}
// 	return true
// }

// func DeleteStudent(params core.DeleteStudentByIDParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetStudentByIDValidator(params core.GetStudentByIDParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteInstructorByIDValidator(params core.DeleteInstructorByIDParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.InstructorID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func EditInstituteValidator(params core.EditInstituteParams) bool {
// 	if params.Institute.Name == constants.EmptyString || params.Institute.ProgramID == constants.EmptyString || params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	if params.Institute.Address == nil || params.Institute.Address.AddressOne == constants.EmptyString || params.Institute.Address.City == constants.EmptyString || params.Institute.Address.Pincode == constants.EmptyString || params.Institute.Address.State == constants.EmptyString {
// 		if params.Institute.Address == constants.EmptyString {
// 			return false
// 		} else {
// 			return true
// 		}
// 	}
// 	return true
// }

// func DeleteInstituteByIDValidator(params core.DeleteInstituteByIDParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func EditTestValidator(params core.EditTestParams) bool {
// 	if params.TestID == constants.EmptyString || params.InstituteID == constants.EmptyString || params.Test.Class <= 0 || params.Test.Questions == nil || params.Test.SubjectName == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func DeleteTestByTestIDValidator(params core.DeleteTestByTestIDParams) bool {
// 	if params.TestID == constants.EmptyString || params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetWeeklyAssessmentsValidator(params core.GetWeeklyAssessmentsParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetMonthlyAssessmentsValidator(params core.GetMonthlyAssessmentsParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetClassStatsValidator(params core.GetClassStatsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func GetClassAssessmentsBySubjectValidator(params core.GetClassAssessmentsBySubjectParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func GetClassAssessmentsBySubjectMonthlyValidator(params core.GetClassAssessmentsBySubjectMonthlyParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func GetClassAssessmentsBySectionsValidator(params core.GetClassAssessmentsBySectionsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func GetClassTopPerformingStudentsValidator(params core.GetClassTopPerformingStudentsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Class <= 0 {
// 		return false
// 	}
// 	return true
// }

// func GetClassDetailsValidator(params core.GetClassDetailsParams) bool {
// 	if params.InstituteID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetGradedSubmissionValidator(params core.GetGradedSubmissionParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func EditGradedSubmissionValidator(params core.EditGradedSubmissionParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString || params.EditData == nil || params.EditData.StudentResponseList == nil || len(params.EditData.StudentResponseList) == 0 {
// 		return false
// 	}
// 	return true
// }

// func GetTestMetricsValidator(params core.GetTestMetricsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || len(params.Sections) == 0 {
// 		return false
// 	}
// 	return true
// }

// func GetSubmissionPerformanceValidator(params core.GetGradedSubmissionPerformanceParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.TestID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetStudentPerformanceValidator(params core.GetStudentPerformanceParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetStudentOverallStatsValidator(params core.GetStudentOverallStatsParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetClassPerformanceValidator(params core.GetClassPerformanceParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.ClassNumber <= 0 || params.ClassNumber > 12 {
// 		return false
// 	}
// 	return true
// }

// func GetStudentMonthlyAssessmentsBySubjectValidator(params core.GetStudentMonthlyAssessmentsBySubjectParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func GetStudentAvgMonthlyAssessmentsScoreBySubject(params core.GetStudentAvgMonthlyAssessmentsScoreBySubjectParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.StudentID == constants.EmptyString {
// 		return false
// 	}
// 	return true
// }

// func UploadRubricFromFileValidator(params core.UploadRubricFromFileParams) bool {
// 	if params.InstituteID == constants.EmptyString || params.Options.Class < 6 || params.Options.Class > 12 || params.Options.Subject == constants.EmptyString || len(params.Options.Questions.QuestionList) == 0 || len(params.Options.FilePaths) <= 0 {
// 		return false
// 	}
// 	return true
// }
