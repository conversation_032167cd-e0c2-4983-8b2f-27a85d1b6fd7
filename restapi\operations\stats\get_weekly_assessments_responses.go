// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetWeeklyAssessmentsOKCode is the HTTP code returned for type GetWeeklyAssessmentsOK
const GetWeeklyAssessmentsOKCode int = 200

/*
GetWeeklyAssessmentsOK Successful operation

swagger:response getWeeklyAssessmentsOK
*/
type GetWeeklyAssessmentsOK struct {

	/*
	  In: Body
	*/
	Payload *models.WeeklyAssessmentsGraded `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsOK creates GetWeeklyAssessmentsOK with default headers values
func NewGetWeeklyAssessmentsOK() *GetWeeklyAssessmentsOK {

	return &GetWeeklyAssessmentsOK{}
}

// WithPayload adds the payload to the get weekly assessments o k response
func (o *GetWeeklyAssessmentsOK) WithPayload(payload *models.WeeklyAssessmentsGraded) *GetWeeklyAssessmentsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments o k response
func (o *GetWeeklyAssessmentsOK) SetPayload(payload *models.WeeklyAssessmentsGraded) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetWeeklyAssessmentsBadRequestCode is the HTTP code returned for type GetWeeklyAssessmentsBadRequest
const GetWeeklyAssessmentsBadRequestCode int = 400

/*
GetWeeklyAssessmentsBadRequest Bad Request

swagger:response getWeeklyAssessmentsBadRequest
*/
type GetWeeklyAssessmentsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsBadRequest creates GetWeeklyAssessmentsBadRequest with default headers values
func NewGetWeeklyAssessmentsBadRequest() *GetWeeklyAssessmentsBadRequest {

	return &GetWeeklyAssessmentsBadRequest{}
}

// WithPayload adds the payload to the get weekly assessments bad request response
func (o *GetWeeklyAssessmentsBadRequest) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments bad request response
func (o *GetWeeklyAssessmentsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetWeeklyAssessmentsForbiddenCode is the HTTP code returned for type GetWeeklyAssessmentsForbidden
const GetWeeklyAssessmentsForbiddenCode int = 403

/*
GetWeeklyAssessmentsForbidden Forbidden

swagger:response getWeeklyAssessmentsForbidden
*/
type GetWeeklyAssessmentsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsForbidden creates GetWeeklyAssessmentsForbidden with default headers values
func NewGetWeeklyAssessmentsForbidden() *GetWeeklyAssessmentsForbidden {

	return &GetWeeklyAssessmentsForbidden{}
}

// WithPayload adds the payload to the get weekly assessments forbidden response
func (o *GetWeeklyAssessmentsForbidden) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments forbidden response
func (o *GetWeeklyAssessmentsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetWeeklyAssessmentsNotFoundCode is the HTTP code returned for type GetWeeklyAssessmentsNotFound
const GetWeeklyAssessmentsNotFoundCode int = 404

/*
GetWeeklyAssessmentsNotFound Not Found

swagger:response getWeeklyAssessmentsNotFound
*/
type GetWeeklyAssessmentsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsNotFound creates GetWeeklyAssessmentsNotFound with default headers values
func NewGetWeeklyAssessmentsNotFound() *GetWeeklyAssessmentsNotFound {

	return &GetWeeklyAssessmentsNotFound{}
}

// WithPayload adds the payload to the get weekly assessments not found response
func (o *GetWeeklyAssessmentsNotFound) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments not found response
func (o *GetWeeklyAssessmentsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetWeeklyAssessmentsTooManyRequestsCode is the HTTP code returned for type GetWeeklyAssessmentsTooManyRequests
const GetWeeklyAssessmentsTooManyRequestsCode int = 429

/*
GetWeeklyAssessmentsTooManyRequests Too Many Requests

swagger:response getWeeklyAssessmentsTooManyRequests
*/
type GetWeeklyAssessmentsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsTooManyRequests creates GetWeeklyAssessmentsTooManyRequests with default headers values
func NewGetWeeklyAssessmentsTooManyRequests() *GetWeeklyAssessmentsTooManyRequests {

	return &GetWeeklyAssessmentsTooManyRequests{}
}

// WithPayload adds the payload to the get weekly assessments too many requests response
func (o *GetWeeklyAssessmentsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments too many requests response
func (o *GetWeeklyAssessmentsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetWeeklyAssessmentsInternalServerErrorCode is the HTTP code returned for type GetWeeklyAssessmentsInternalServerError
const GetWeeklyAssessmentsInternalServerErrorCode int = 500

/*
GetWeeklyAssessmentsInternalServerError Internal Server Error

swagger:response getWeeklyAssessmentsInternalServerError
*/
type GetWeeklyAssessmentsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsInternalServerError creates GetWeeklyAssessmentsInternalServerError with default headers values
func NewGetWeeklyAssessmentsInternalServerError() *GetWeeklyAssessmentsInternalServerError {

	return &GetWeeklyAssessmentsInternalServerError{}
}

// WithPayload adds the payload to the get weekly assessments internal server error response
func (o *GetWeeklyAssessmentsInternalServerError) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments internal server error response
func (o *GetWeeklyAssessmentsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetWeeklyAssessmentsServiceUnavailableCode is the HTTP code returned for type GetWeeklyAssessmentsServiceUnavailable
const GetWeeklyAssessmentsServiceUnavailableCode int = 503

/*
GetWeeklyAssessmentsServiceUnavailable Service Unvailable

swagger:response getWeeklyAssessmentsServiceUnavailable
*/
type GetWeeklyAssessmentsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetWeeklyAssessmentsServiceUnavailable creates GetWeeklyAssessmentsServiceUnavailable with default headers values
func NewGetWeeklyAssessmentsServiceUnavailable() *GetWeeklyAssessmentsServiceUnavailable {

	return &GetWeeklyAssessmentsServiceUnavailable{}
}

// WithPayload adds the payload to the get weekly assessments service unavailable response
func (o *GetWeeklyAssessmentsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetWeeklyAssessmentsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get weekly assessments service unavailable response
func (o *GetWeeklyAssessmentsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetWeeklyAssessmentsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
