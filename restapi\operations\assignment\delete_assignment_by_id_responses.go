// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteAssignmentByIDOKCode is the HTTP code returned for type DeleteAssignmentByIDOK
const DeleteAssignmentByIDOKCode int = 200

/*
DeleteAssignmentByIDOK Successful operation

swagger:response deleteAssignmentByIdOK
*/
type DeleteAssignmentByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDOK creates DeleteAssignmentByIDOK with default headers values
func NewDeleteAssignmentByIDOK() *DeleteAssignmentByIDOK {

	return &DeleteAssignmentByIDOK{}
}

// WithPayload adds the payload to the delete assignment by Id o k response
func (o *DeleteAssignmentByIDOK) WithPayload(payload *models.SuccessResponse) *DeleteAssignmentByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id o k response
func (o *DeleteAssignmentByIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteAssignmentByIDBadRequestCode is the HTTP code returned for type DeleteAssignmentByIDBadRequest
const DeleteAssignmentByIDBadRequestCode int = 400

/*
DeleteAssignmentByIDBadRequest Bad Request

swagger:response deleteAssignmentByIdBadRequest
*/
type DeleteAssignmentByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDBadRequest creates DeleteAssignmentByIDBadRequest with default headers values
func NewDeleteAssignmentByIDBadRequest() *DeleteAssignmentByIDBadRequest {

	return &DeleteAssignmentByIDBadRequest{}
}

// WithPayload adds the payload to the delete assignment by Id bad request response
func (o *DeleteAssignmentByIDBadRequest) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id bad request response
func (o *DeleteAssignmentByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteAssignmentByIDForbiddenCode is the HTTP code returned for type DeleteAssignmentByIDForbidden
const DeleteAssignmentByIDForbiddenCode int = 403

/*
DeleteAssignmentByIDForbidden Forbidden

swagger:response deleteAssignmentByIdForbidden
*/
type DeleteAssignmentByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDForbidden creates DeleteAssignmentByIDForbidden with default headers values
func NewDeleteAssignmentByIDForbidden() *DeleteAssignmentByIDForbidden {

	return &DeleteAssignmentByIDForbidden{}
}

// WithPayload adds the payload to the delete assignment by Id forbidden response
func (o *DeleteAssignmentByIDForbidden) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id forbidden response
func (o *DeleteAssignmentByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteAssignmentByIDNotFoundCode is the HTTP code returned for type DeleteAssignmentByIDNotFound
const DeleteAssignmentByIDNotFoundCode int = 404

/*
DeleteAssignmentByIDNotFound Not Found

swagger:response deleteAssignmentByIdNotFound
*/
type DeleteAssignmentByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDNotFound creates DeleteAssignmentByIDNotFound with default headers values
func NewDeleteAssignmentByIDNotFound() *DeleteAssignmentByIDNotFound {

	return &DeleteAssignmentByIDNotFound{}
}

// WithPayload adds the payload to the delete assignment by Id not found response
func (o *DeleteAssignmentByIDNotFound) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id not found response
func (o *DeleteAssignmentByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteAssignmentByIDTooManyRequestsCode is the HTTP code returned for type DeleteAssignmentByIDTooManyRequests
const DeleteAssignmentByIDTooManyRequestsCode int = 429

/*
DeleteAssignmentByIDTooManyRequests Too Many Requests

swagger:response deleteAssignmentByIdTooManyRequests
*/
type DeleteAssignmentByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDTooManyRequests creates DeleteAssignmentByIDTooManyRequests with default headers values
func NewDeleteAssignmentByIDTooManyRequests() *DeleteAssignmentByIDTooManyRequests {

	return &DeleteAssignmentByIDTooManyRequests{}
}

// WithPayload adds the payload to the delete assignment by Id too many requests response
func (o *DeleteAssignmentByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id too many requests response
func (o *DeleteAssignmentByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteAssignmentByIDInternalServerErrorCode is the HTTP code returned for type DeleteAssignmentByIDInternalServerError
const DeleteAssignmentByIDInternalServerErrorCode int = 500

/*
DeleteAssignmentByIDInternalServerError Internal Server Error

swagger:response deleteAssignmentByIdInternalServerError
*/
type DeleteAssignmentByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDInternalServerError creates DeleteAssignmentByIDInternalServerError with default headers values
func NewDeleteAssignmentByIDInternalServerError() *DeleteAssignmentByIDInternalServerError {

	return &DeleteAssignmentByIDInternalServerError{}
}

// WithPayload adds the payload to the delete assignment by Id internal server error response
func (o *DeleteAssignmentByIDInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id internal server error response
func (o *DeleteAssignmentByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteAssignmentByIDServiceUnavailableCode is the HTTP code returned for type DeleteAssignmentByIDServiceUnavailable
const DeleteAssignmentByIDServiceUnavailableCode int = 503

/*
DeleteAssignmentByIDServiceUnavailable Service Unvailable

swagger:response deleteAssignmentByIdServiceUnavailable
*/
type DeleteAssignmentByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteAssignmentByIDServiceUnavailable creates DeleteAssignmentByIDServiceUnavailable with default headers values
func NewDeleteAssignmentByIDServiceUnavailable() *DeleteAssignmentByIDServiceUnavailable {

	return &DeleteAssignmentByIDServiceUnavailable{}
}

// WithPayload adds the payload to the delete assignment by Id service unavailable response
func (o *DeleteAssignmentByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteAssignmentByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete assignment by Id service unavailable response
func (o *DeleteAssignmentByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteAssignmentByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
