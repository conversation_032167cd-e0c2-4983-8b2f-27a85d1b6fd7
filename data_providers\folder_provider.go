package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type FolderProvider interface {
	Create(ctx context.Context, folder *entities.Folder) error
	GetAll(ctx context.Context, instituteID string) (*[]entities.Folder, error)
	GetByID(ctx context.Context, folderID string) (*entities.Folder, error)
	Update(ctx context.Context, folder *entities.Folder) error
	Delete(ctx context.Context, folderID string) error
}
