// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllStatsHandlerFunc turns a function with the right signature into a get all stats handler
type GetAllStatsHandlerFunc func(GetAllStatsParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetAllStatsHandlerFunc) Handle(params GetAllStatsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllStatsHandler interface for that can handle valid get all stats params
type GetAllStatsHandler interface {
	Handle(GetAllStatsParams, interface{}) middleware.Responder
}

// NewGetAllStats creates a new http.Handler for the get all stats operation
func NewGetAllStats(ctx *middleware.Context, handler GetAll<PERSON>tat<PERSON><PERSON>andler) *GetAllStats {
	return &GetAllStats{Context: ctx, Handler: handler}
}

/*
	GetAllStats swagger:route GET /institute/{instituteId}/allstats stats getAllStats

# Get all stats

Get all stats for students, instructors, and assessments
*/
type GetAllStats struct {
	Context *middleware.Context
	Handler GetAllStatsHandler
}

func (o *GetAllStats) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllStatsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
