// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// FileList file list
//
// swagger:model FileList
type FileList struct {

	// files
	Files []string `json:"files"`
}

// Validate validates this file list
func (m *FileList) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this file list based on context it is used
func (m *FileList) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *FileList) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *FileList) UnmarshalBinary(b []byte) error {
	var res FileList
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
