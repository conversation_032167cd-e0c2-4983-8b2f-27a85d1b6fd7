package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type statsProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Daily submissions
func (s *statsProvider) GetDailySubmissionsCount(ctx context.Context, instituteID, termID string, date string) (*entities.DailySubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetDailySubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.date":         date,
	}

	var result entities.DailySubmissionsCountView
	err := s.mongoClient.Database(s.dbName).
		Collection("daily_submissions_count_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get daily submissions count")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllDailySubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.DailySubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllDailySubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
	}

	var results []entities.DailySubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("daily_submissions_count_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all daily submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode daily submissions count")
		return nil, err
	}

	return &results, nil
}

// Monthly submissions
func (s *statsProvider) GetMonthlySubmissionsCount(ctx context.Context, instituteID, termID string, date string) (*entities.MonthlySubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetMonthlySubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.date":         date,
	}

	var result entities.MonthlySubmissionsCountView
	err := s.mongoClient.Database(s.dbName).
		Collection("monthly_submissions_count_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get monthly submissions count")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllMonthlySubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.MonthlySubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllMonthlySubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
	}

	var results []entities.MonthlySubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("monthly_submissions_count_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all monthly submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode monthly submissions count")
		return nil, err
	}

	return &results, nil
}

// Grade submissions
func (s *statsProvider) GetGradeSubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, date string) (*entities.GradeSubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetGradeSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.grade":        grade,
		"_id.date":         date,
	}

	var result entities.GradeSubmissionsCountView
	err := s.mongoClient.Database(s.dbName).
		Collection("grade_submissions_count_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade submissions count")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllGradeSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllGradeSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
	}

	var results []entities.GradeSubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("grade_submissions_count_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all grade submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade submissions count")
		return nil, err
	}

	return &results, nil
}

func (s *statsProvider) GetSubjectSubmissionsCount(ctx context.Context, instituteID, termID string, subject string) (*entities.SubjectSubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetSubjectSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.subject":      subject,
	}

	var result entities.SubjectSubmissionsCountView
	err := s.mongoClient.Database(s.dbName).
		Collection("subject_submissions_count_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get subject submissions count")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllSubjectSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.SubjectSubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllSubjectSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
	}

	var results []entities.SubjectSubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("subject_submissions_count_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all subject submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode subject submissions count")
		return nil, err
	}

	return &results, nil
}

// Grade and subject submissions
func (s *statsProvider) GetGradeSubjectSubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, subject *string) (*[]entities.GradeSubjectSubmissionDataView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetGradeSubjectSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
		"grade":        grade,
	}

	if subject != nil {
		filter["subject"] = *subject
	}

	var results []entities.GradeSubjectSubmissionDataView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewGradeSubjectSubmissionData).
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade subject submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade subject submissions count")
		return nil, err
	}

	return &results, nil
}

func (s *statsProvider) GetAllGradeSubjectSubmissionsCount(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubjectSubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllGradeSubjectSubmissionsCount")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
	}

	var results []entities.GradeSubjectSubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("grade_subject_submissions_count_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all grade subject submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade subject submissions count")
		return nil, err
	}

	return &results, nil
}

// Student performance
func (s *statsProvider) GetStudentOverallPerformance(ctx context.Context, instituteID, termID, studentID string) (*entities.StudentOverallPercentageView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetStudentOverallPerformance")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.student_id":   studentID,
	}

	var result entities.StudentOverallPercentageView
	err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewStudentOverallPercentage).
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get student overall performance")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllStudentOverallPerformance(ctx context.Context, instituteID, termID string, grade int32) (*[]entities.StudentOverallPercentageView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllStudentOverallPerformance")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"grade":            grade,
	}

	var results []entities.StudentOverallPercentageView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewStudentOverallPercentage).
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all student overall performance")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode student overall performance")
		return nil, err
	}

	return &results, nil
}

// Grade subject data
func (s *statsProvider) GetGradeSubjectData(ctx context.Context, instituteID, termID string, grade int32, subject string) (*entities.GradeSubjectSubmissionDataView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetGradeSubjectData")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
		"grade":        grade,
		"subject":      subject,
	}

	var result entities.GradeSubjectSubmissionDataView
	err := s.mongoClient.Database(s.dbName).
		Collection("grade_subject_submission_data_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade subject data")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllGradeSubjectData(ctx context.Context, instituteID, termID string) (*[]entities.GradeSubjectSubmissionDataView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllGradeSubjectData")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
	}

	var results []entities.GradeSubjectSubmissionDataView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("grade_subject_submission_data_view").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all grade subject data")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade subject data")
		return nil, err
	}

	return &results, nil
}

// Grade overall stats
func (s *statsProvider) GetGradeOverallStats(ctx context.Context, instituteID, termID string, grade int32) (*entities.GradeOverallStats, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetGradeOverallStats")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
		"grade":        grade,
	}

	var result entities.GradeOverallStats
	err := s.mongoClient.Database(s.dbName).
		Collection("grade_overall_stats").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade overall stats")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllGradeOverallStats(ctx context.Context, instituteID, termID string) (*[]entities.GradeOverallStats, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllGradeOverallStats")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
	}

	var results []entities.GradeOverallStats
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection("grade_overall_stats").
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all grade overall stats")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade overall stats")
		return nil, err
	}

	return &results, nil
}

// Section submission count
func (s *statsProvider) GetSectionData(ctx context.Context, instituteID, termID string, grade int32, section string) (*entities.SectionDataView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetSectionSubmissionCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.grade":        grade,
		"_id.section":      section,
	}

	var result entities.SectionDataView
	err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewSectionData).
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get section submission count")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllSectionData(ctx context.Context, instituteID, termID string, grade int32) (*[]entities.SectionDataView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllSectionSubmissionCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.grade":        grade,
	}

	var results []entities.SectionDataView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewSectionData).
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all section submission count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode section submission count")
		return nil, err
	}

	return &results, nil
}

// Institute overall stats
func (s *statsProvider) GetInstituteOverallStats(ctx context.Context, instituteID, termID string) (*entities.InstituteOverallStatsView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetInstituteOverallStats")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteID,
		"term_id":      termID,
	}

	var result entities.InstituteOverallStatsView
	err := s.mongoClient.Database(s.dbName).
		Collection("institute_all_stats_view").
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get institute overall stats")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetGradeMonthlySubmissionsCount(ctx context.Context, instituteID, termID string, grade int32, date string, subject *string) (*[]entities.GradeMonthlySubmissionsCountView, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetGradeMonthlySubmissionsCount")
	defer span.End()

	filter := bson.M{
		"_id.institute_id": instituteID,
		"_id.term_id":      termID,
		"_id.grade":        grade,
		"_id.date":         date,
	}
	if subject != nil {
		filter["_id.subject"] = *subject
	}
	var results []entities.GradeMonthlySubmissionsCountView
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewMonthlyGradeSubjectSubmissionsCount).
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade monthly submissions count")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode grade monthly submissions count")
		return nil, err
	}

	return &results, nil

}

// Student Topic Performance
func (s *statsProvider) GetStudentTopicPerformance(ctx context.Context, assignmentID, termID, studentID string) (*entities.StudentTopicPerformance, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetStudentTopicPerformance")
	defer span.End()

	filter := bson.M{
		"assignment_id": assignmentID,
		"term_id":       termID,
		"student_id":    studentID,
	}

	var result entities.StudentTopicPerformance
	err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewStudentChapterPerformance).
		FindOne(ctx, filter).
		Decode(&result)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get student topic performance")
		return nil, err
	}

	return &result, nil
}

func (s *statsProvider) GetAllStudentTopicPerformance(ctx context.Context, termID string, studentID string) (*[]entities.StudentTopicPerformance, error) {
	ctx, span := s.tracer.Start(ctx, "StatsProvider.GetAllStudentTopicPerformance")
	defer span.End()

	filter := bson.M{
		"term_id":    termID,
		"student_id": studentID,
	}

	var results []entities.StudentTopicPerformance
	cursor, err := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBViewStudentChapterPerformance).
		Find(ctx, filter)

	if err != nil {
		log.Error().Err(err).Msg("Failed to get all student topic performance")
		return nil, err
	}

	if err = cursor.All(ctx, &results); err != nil {
		log.Error().Err(err).Msg("Failed to decode student topic performance")
		return nil, err
	}

	return &results, nil
}

// NewStatsProvider creates a new instance of StatsProvider
func NewStatsProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) StatsProvider {
	return &statsProvider{
		mongoClient: mongoClient,
		dbName:      databaseName,
		tracer:      tracer,
	}
}
