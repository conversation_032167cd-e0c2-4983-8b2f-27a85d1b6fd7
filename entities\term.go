package entities

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

type Term struct {
	ID          *string    `json:"id" bson:"_id"`
	InstituteID *string    `json:"institute_id" bson:"institute_id"`
	Name        *string    `json:"name" bson:"name"` // e.g. "Term 1", "Term 2"
	StartDate   *time.Time `json:"start_date,omitempty" bson:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty" bson:"end_date,omitempty"`
	// TODO: Created details
}

// NewTerm initializes a new term and determines if it's the current term
func NewTerm(instituteId string, name string, startDate, endDate *time.Time) *Term {
	id := uuid.New().String()

	return &Term{
		ID:          &id,
		InstituteID: &instituteId,
		Name:        &name,
		StartDate:   startDate,
		EndDate:     endDate,
	}
}

// Validate ensures Term data is correct
func (t *Term) Validate() error {
	if t.Name == nil || *t.Name == "" {
		return errors.New("term name cannot be empty")
	}
	if t.StartDate != nil && t.EndDate != nil && t.StartDate.After(*t.EndDate) {
		return errors.New("start date cannot be after end date")
	}
	return nil
}
