// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetMonthlyAssessmentsHandlerFunc turns a function with the right signature into a get monthly assessments handler
type GetMonthlyAssessmentsHandlerFunc func(GetMonthlyAssessmentsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetMonthlyAssessmentsHandlerFunc) Handle(params GetMonthlyAssessmentsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetMonthlyAssessmentsHandler interface for that can handle valid get monthly assessments params
type GetMonthlyAssessmentsHandler interface {
	Handle(GetMonthlyAssessmentsParams, interface{}) middleware.Responder
}

// NewGetMonthlyAssessments creates a new http.Handler for the get monthly assessments operation
func NewGetMonthlyAssessments(ctx *middleware.Context, handler GetMonthlyAssessm<PERSON>Hand<PERSON>) *GetMonthlyAssessments {
	return &GetMonthlyAssessments{Context: ctx, Handler: handler}
}

/*
	GetMonthlyAssessments swagger:route GET /institute/{instituteId}/assessments/monthly stats getMonthlyAssessments

# Get monthly assessments

Get all the assessments graded last 6 months for each month
*/
type GetMonthlyAssessments struct {
	Context *middleware.Context
	Handler GetMonthlyAssessmentsHandler
}

func (o *GetMonthlyAssessments) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetMonthlyAssessmentsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
