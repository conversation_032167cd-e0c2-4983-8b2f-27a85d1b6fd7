// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// AddTopicsToAssignmentHandlerFunc turns a function with the right signature into a add topics to assignment handler
type AddTopicsToAssignmentHandlerFunc func(AddTopicsToAssignmentParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn AddTopicsToAssignmentHandlerFunc) Handle(params AddTopicsToAssignmentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// AddTopicsToAssignmentHandler interface for that can handle valid add topics to assignment params
type AddTopicsToAssignmentHandler interface {
	Handle(AddTopicsToAssignmentParams, interface{}) middleware.Responder
}

// NewAddTopicsToAssignment creates a new http.Handler for the add topics to assignment operation
func NewAddTopicsToAssignment(ctx *middleware.Context, handler AddTopicsToAssignmentHandler) *AddTopicsToAssignment {
	return &AddTopicsToAssignment{Context: ctx, Handler: handler}
}

/*
	AddTopicsToAssignment swagger:route POST /institute/{instituteId}/assignment/{assignmentId}/topics assignment addTopicsToAssignment

# Add topics to assignment

Add topics by question number
*/
type AddTopicsToAssignment struct {
	Context *middleware.Context
	Handler AddTopicsToAssignmentHandler
}

func (o *AddTopicsToAssignment) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewAddTopicsToAssignmentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
