// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// StudentPerformance student performance
//
// swagger:model StudentPerformance
type StudentPerformance struct {

	// assessment performance
	AssessmentPerformance []*SubjectAssessmentPerformance `json:"assessmentPerformance"`

	// highest performance
	HighestPerformance float32 `json:"highestPerformance,omitempty"`

	// lowest performance
	LowestPerformance float32 `json:"lowestPerformance,omitempty"`

	// overall performance
	OverallPerformance *StudentOverallPerformance `json:"overallPerformance,omitempty"`
}

// Validate validates this student performance
func (m *StudentPerformance) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateAssessmentPerformance(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateOverallPerformance(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentPerformance) validateAssessmentPerformance(formats strfmt.Registry) error {
	if swag.IsZero(m.AssessmentPerformance) { // not required
		return nil
	}

	for i := 0; i < len(m.AssessmentPerformance); i++ {
		if swag.IsZero(m.AssessmentPerformance[i]) { // not required
			continue
		}

		if m.AssessmentPerformance[i] != nil {
			if err := m.AssessmentPerformance[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("assessmentPerformance" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("assessmentPerformance" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentPerformance) validateOverallPerformance(formats strfmt.Registry) error {
	if swag.IsZero(m.OverallPerformance) { // not required
		return nil
	}

	if m.OverallPerformance != nil {
		if err := m.OverallPerformance.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("overallPerformance")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("overallPerformance")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this student performance based on the context it is used
func (m *StudentPerformance) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateAssessmentPerformance(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateOverallPerformance(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentPerformance) contextValidateAssessmentPerformance(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.AssessmentPerformance); i++ {

		if m.AssessmentPerformance[i] != nil {

			if swag.IsZero(m.AssessmentPerformance[i]) { // not required
				return nil
			}

			if err := m.AssessmentPerformance[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("assessmentPerformance" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("assessmentPerformance" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentPerformance) contextValidateOverallPerformance(ctx context.Context, formats strfmt.Registry) error {

	if m.OverallPerformance != nil {

		if swag.IsZero(m.OverallPerformance) { // not required
			return nil
		}

		if err := m.OverallPerformance.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("overallPerformance")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("overallPerformance")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *StudentPerformance) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentPerformance) UnmarshalBinary(b []byte) error {
	var res StudentPerformance
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
