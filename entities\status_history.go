package entities

import (
	"eddyowl-backend/constants"
	"errors"
	"fmt"
	"time"
)

type StatusHistory struct {
	Status    int        `json:"status" bson:"status"`
	Timestamp *time.Time `json:"timestamp" bson:"timestamp"`
	UpdatedBy *string    `json:"updated_by" bson:"updated_by"`
}

// NewStatusHistory initializes a new assignment history.
func NewStatusHistory(status int, updatedBy string) *StatusHistory {
	now := time.Now()
	return &StatusHistory{
		Status:    status,
		Timestamp: &now,
		UpdatedBy: &updatedBy,
	}
}

// Validate ensures StatusHistory data is correct.
func (ah *StatusHistory) Validate() error {
	if ah.Status < constants.AssignmentHistoryStatusCreated || ah.Status > constants.AssignmentHistoryStatusDealineReached {
		return fmt.Errorf("invalid assignment status: %d", ah.Status)
	}
	if ah.Timestamp == nil || ah.Timestamp.IsZero() {
		return errors.New("timestamp is required")
	}
	if ah.UpdatedBy == nil || *ah.UpdatedBy == "" {
		return errors.New("updated by is required")
	}
	return nil
}
