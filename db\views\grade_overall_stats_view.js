db.createView("grade_overall_stats", "terms", [
  // 1. Lookup Institutes
  {
    $lookup: {
      from: "institutes",
      localField: "institute_id",
      foreignField: "_id",
      as: "institute",
    },
  },
  { $unwind: "$institute" },

  // 2. Generate grades (custom range: 1-12, modify as needed)
  {
    $addFields: {
      grades: [6, 7, 8, 9, 10, 11, 12], // grades 1 to 12
    },
  },
  { $unwind: "$grades" },

  // 3. Count students by grade, term & institute
  {
    $lookup: {
      from: "students",
      let: { termId: "$_id", grade: "$grades", instId: "$institute_id" },
      pipeline: [
        { $match: { $expr: { $eq: ["$institute_id", "$$instId"] } } },
        {
          $project: {
            academic_history: { $objectToArray: "$academic_history" },
          },
        },
        { $unwind: "$academic_history" },
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$academic_history.k", "$$termId"] },
                { $eq: ["$academic_history.v.grade", "$$grade"] },
              ],
            },
          },
        },
        { $count: "count" },
      ],
      as: "student_stats",
    },
  },

  // 4. Count instructors by institute
  {
    $lookup: {
      from: "instructors",
      let: { instId: "$institute_id" },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$instId"] },
                { $in: ["$role", [1, 2]] }, // only active instructors
                { $eq: ["$status", 1] },
              ],
            },
          },
        },
        { $count: "count" },
      ],
      as: "instructor_stats",
    },
  },

  // 5. Count assignments
  {
    $lookup: {
      from: "assignments",
      let: { instId: "$institute_id", grade: "$grades", termId: "$_id" },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$instId"] },
                { $eq: ["$grade", "$$grade"] },
                { $eq: ["$term_id", "$$termId"] },
              ],
            },
          },
        },
        { $count: "count" },
      ],
      as: "assignment_stats",
    },
  },

  // 6. Calculate average submission %
  {
    $lookup: {
      from: "submissions",
      let: { instId: "$institute_id", grade: "$grades", termId: "$_id" },
      pipeline: [
        {
          $lookup: {
            from: "assignments",
            localField: "assignment_id",
            foreignField: "_id",
            as: "assignment",
          },
        },
        { $unwind: "$assignment" },
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$instId"] },
                { $eq: ["$assignment.grade", "$$grade"] },
                { $eq: ["$assignment.term_id", "$$termId"] },
              ],
            },
          },
        },
        {
          $group: {
            _id: null,
            total_scored: { $sum: "$total_achieved_score" },
            total_possible: { $sum: "$assignment.total_score" },
          },
        },
        {
          $project: {
            _id: 0,
            avg_percentage: {
              $cond: [
                { $gt: ["$total_possible", 0] },
                {
                  $multiply: [
                    { $divide: ["$total_scored", "$total_possible"] },
                    100,
                  ],
                },
                0,
              ],
            },
          },
        },
      ],
      as: "submission_stats",
    },
  },

  // 7. Final projection
  {
    $project: {
      _id: 0,
      institute_id: "$institute_id",
      term_id: "$_id",
      grade: "$grades",
      students: { $ifNull: [{ $arrayElemAt: ["$student_stats.count", 0] }, 0] },
      instructors: {
        $ifNull: [{ $arrayElemAt: ["$instructor_stats.count", 0] }, 0],
      },
      assessments: {
        $ifNull: [{ $arrayElemAt: ["$assignment_stats.count", 0] }, 0],
      },
      average_submission_percentage: {
        $round: [
          {
            $ifNull: [
              { $arrayElemAt: ["$submission_stats.avg_percentage", 0] },
              0,
            ],
          },
          2,
        ],
      },
    },
  },
]);
