// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditTermHandlerFunc turns a function with the right signature into a edit term handler
type EditTermHandlerFunc func(EditTermParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditTermHandlerFunc) Handle(params EditTermParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditTermHandler interface for that can handle valid edit term params
type EditTermHandler interface {
	Handle(EditTermParams, interface{}) middleware.Responder
}

// NewEditTerm creates a new http.Handler for the edit term operation
func NewEditTerm(ctx *middleware.Context, handler EditTermHandler) *EditTerm {
	return &EditTerm{Context: ctx, Handler: handler}
}

/*
	EditTerm swagger:route PUT /institute/{instituteId}/terms term editTerm

# Edit term

Edit term
*/
type EditTerm struct {
	Context *middleware.Context
	Handler EditTermHandler
}

func (o *EditTerm) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditTermParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
