// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// CreateNewStudentHandlerFunc turns a function with the right signature into a create new student handler
type CreateNewStudentHandlerFunc func(CreateNewStudentParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn CreateNewStudentHandlerFunc) Handle(params CreateNewStudentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateNewStudentHandler interface for that can handle valid create new student params
type CreateNewStudentHandler interface {
	Handle(CreateNewStudentParams, interface{}) middleware.Responder
}

// NewCreateNewStudent creates a new http.Handler for the create new student operation
func NewCreateNewStudent(ctx *middleware.Context, handler <PERSON><PERSON><PERSON><PERSON><PERSON>tude<PERSON><PERSON><PERSON><PERSON>) *CreateNewStudent {
	return &CreateNewStudent{Context: ctx, Handler: handler}
}

/*
	CreateNewStudent swagger:route POST /institute/{instituteId}/student student createNewStudent

# Create new student

Create new student
*/
type CreateNewStudent struct {
	Context *middleware.Context
	Handler CreateNewStudentHandler
}

func (o *CreateNewStudent) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateNewStudentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
