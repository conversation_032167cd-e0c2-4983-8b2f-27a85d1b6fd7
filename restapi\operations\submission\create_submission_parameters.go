// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/validate"

	"eddyowl-backend/models"
)

// NewCreateSubmissionParams creates a new CreateSubmissionParams object
//
// There are no default values defined in the spec.
func NewCreateSubmissionParams() CreateSubmissionParams {

	return CreateSubmissionParams{}
}

// CreateSubmissionParams contains all the bound params for the create submission operation
// typically these are obtained from a http.Request
//
// swagger:parameters CreateSubmission
type CreateSubmissionParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	AssignmentID string
	/*
	  In: body
	*/
	Files *models.FileList
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  Required: true
	  In: path
	*/
	StudentID string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewCreateSubmissionParams() beforehand.
func (o *CreateSubmissionParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rAssignmentID, rhkAssignmentID, _ := route.Params.GetOK("assignmentId")
	if err := o.bindAssignmentID(rAssignmentID, rhkAssignmentID, route.Formats); err != nil {
		res = append(res, err)
	}

	if runtime.HasBody(r) {
		defer r.Body.Close()
		var body models.FileList
		if err := route.Consumer.Consume(r.Body, &body); err != nil {
			res = append(res, errors.NewParseError("files", "body", "", err))
		} else {
			// validate body object
			if err := body.Validate(route.Formats); err != nil {
				res = append(res, err)
			}

			ctx := validate.WithOperationRequest(r.Context())
			if err := body.ContextValidate(ctx, route.Formats); err != nil {
				res = append(res, err)
			}

			if len(res) == 0 {
				o.Files = &body
			}
		}
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	rStudentID, rhkStudentID, _ := route.Params.GetOK("studentId")
	if err := o.bindStudentID(rStudentID, rhkStudentID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindAssignmentID binds and validates parameter AssignmentID from path.
func (o *CreateSubmissionParams) bindAssignmentID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.AssignmentID = raw

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *CreateSubmissionParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}

// bindStudentID binds and validates parameter StudentID from path.
func (o *CreateSubmissionParams) bindStudentID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.StudentID = raw

	return nil
}
