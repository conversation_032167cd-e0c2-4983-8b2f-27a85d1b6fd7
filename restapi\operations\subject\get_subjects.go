// Code generated by go-swagger; DO NOT EDIT.

package subject

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetSubjectsHandlerFunc turns a function with the right signature into a get subjects handler
type GetSubjectsHandlerFunc func(GetSubjectsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetSubjectsHandlerFunc) Handle(params GetSubjectsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetSubjectsHandler interface for that can handle valid get subjects params
type GetSubjectsHandler interface {
	Handle(GetSubjectsParams, interface{}) middleware.Responder
}

// NewGetSubjects creates a new http.Handler for the get subjects operation
func NewGetSubjects(ctx *middleware.Context, handler GetSubjectsHandler) *GetSubjects {
	return &GetSubjects{Context: ctx, Handler: handler}
}

/*
	GetSubjects swagger:route GET /institute/{instituteId}/subject/grade/{grade} subject getSubjects

# Get subjects by grade

Returns list of subjects by grade
*/
type GetSubjects struct {
	Context *middleware.Context
	Handler GetSubjectsHandler
}

func (o *GetSubjects) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetSubjectsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
