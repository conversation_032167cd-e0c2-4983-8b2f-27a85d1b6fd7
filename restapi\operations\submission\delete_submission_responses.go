// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteSubmissionOKCode is the HTTP code returned for type DeleteSubmissionOK
const DeleteSubmissionOKCode int = 200

/*
DeleteSubmissionOK Successful operation

swagger:response deleteSubmissionOK
*/
type DeleteSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionOK creates DeleteSubmissionOK with default headers values
func NewDeleteSubmissionOK() *DeleteSubmissionOK {

	return &DeleteSubmissionOK{}
}

// WithPayload adds the payload to the delete submission o k response
func (o *DeleteSubmissionOK) WithPayload(payload *models.SuccessResponse) *DeleteSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission o k response
func (o *DeleteSubmissionOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteSubmissionBadRequestCode is the HTTP code returned for type DeleteSubmissionBadRequest
const DeleteSubmissionBadRequestCode int = 400

/*
DeleteSubmissionBadRequest Bad Request

swagger:response deleteSubmissionBadRequest
*/
type DeleteSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionBadRequest creates DeleteSubmissionBadRequest with default headers values
func NewDeleteSubmissionBadRequest() *DeleteSubmissionBadRequest {

	return &DeleteSubmissionBadRequest{}
}

// WithPayload adds the payload to the delete submission bad request response
func (o *DeleteSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *DeleteSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission bad request response
func (o *DeleteSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteSubmissionForbiddenCode is the HTTP code returned for type DeleteSubmissionForbidden
const DeleteSubmissionForbiddenCode int = 403

/*
DeleteSubmissionForbidden Forbidden

swagger:response deleteSubmissionForbidden
*/
type DeleteSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionForbidden creates DeleteSubmissionForbidden with default headers values
func NewDeleteSubmissionForbidden() *DeleteSubmissionForbidden {

	return &DeleteSubmissionForbidden{}
}

// WithPayload adds the payload to the delete submission forbidden response
func (o *DeleteSubmissionForbidden) WithPayload(payload models.ErrorResponse) *DeleteSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission forbidden response
func (o *DeleteSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteSubmissionNotFoundCode is the HTTP code returned for type DeleteSubmissionNotFound
const DeleteSubmissionNotFoundCode int = 404

/*
DeleteSubmissionNotFound Not Found

swagger:response deleteSubmissionNotFound
*/
type DeleteSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionNotFound creates DeleteSubmissionNotFound with default headers values
func NewDeleteSubmissionNotFound() *DeleteSubmissionNotFound {

	return &DeleteSubmissionNotFound{}
}

// WithPayload adds the payload to the delete submission not found response
func (o *DeleteSubmissionNotFound) WithPayload(payload models.ErrorResponse) *DeleteSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission not found response
func (o *DeleteSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteSubmissionTooManyRequestsCode is the HTTP code returned for type DeleteSubmissionTooManyRequests
const DeleteSubmissionTooManyRequestsCode int = 429

/*
DeleteSubmissionTooManyRequests Too Many Requests

swagger:response deleteSubmissionTooManyRequests
*/
type DeleteSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionTooManyRequests creates DeleteSubmissionTooManyRequests with default headers values
func NewDeleteSubmissionTooManyRequests() *DeleteSubmissionTooManyRequests {

	return &DeleteSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the delete submission too many requests response
func (o *DeleteSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission too many requests response
func (o *DeleteSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteSubmissionInternalServerErrorCode is the HTTP code returned for type DeleteSubmissionInternalServerError
const DeleteSubmissionInternalServerErrorCode int = 500

/*
DeleteSubmissionInternalServerError Internal Server Error

swagger:response deleteSubmissionInternalServerError
*/
type DeleteSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionInternalServerError creates DeleteSubmissionInternalServerError with default headers values
func NewDeleteSubmissionInternalServerError() *DeleteSubmissionInternalServerError {

	return &DeleteSubmissionInternalServerError{}
}

// WithPayload adds the payload to the delete submission internal server error response
func (o *DeleteSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission internal server error response
func (o *DeleteSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteSubmissionServiceUnavailableCode is the HTTP code returned for type DeleteSubmissionServiceUnavailable
const DeleteSubmissionServiceUnavailableCode int = 503

/*
DeleteSubmissionServiceUnavailable Service Unvailable

swagger:response deleteSubmissionServiceUnavailable
*/
type DeleteSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteSubmissionServiceUnavailable creates DeleteSubmissionServiceUnavailable with default headers values
func NewDeleteSubmissionServiceUnavailable() *DeleteSubmissionServiceUnavailable {

	return &DeleteSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the delete submission service unavailable response
func (o *DeleteSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete submission service unavailable response
func (o *DeleteSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
