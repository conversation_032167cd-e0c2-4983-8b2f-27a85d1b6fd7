package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/restapi/operations/subject"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getSubjectsImpl struct {
	provider          data_providers.SubjectProvider
	instituteProvider data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetSubjectsHandler(provider data_providers.SubjectProvider, instituteProvider data_providers.InstituteProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) subject.GetSubjectsHandler {
	return &getSubjectsImpl{provider: provider, instituteProvider: instituteProvider, userRolesProvider: userRolesProvider, tracer: tracer}
}

func (impl *getSubjectsImpl) Handle(params subject.GetSubjectsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetSubjectsHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return subject.NewGetSubjectsForbidden().WithPayload("Unauthorized")
	}

	//Get Institute
	institue, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		log.Error().Msg(err.Error())
		return subject.NewGetSubjectsNotFound().WithPayload("Institute not found")
	}

	subjects, err := impl.provider.Get(ctx, *institue.Program, int(params.Grade))
	if err != nil {
		log.Error().Msg(err.Error())
		return subject.NewGetSubjectsInternalServerError().WithPayload("Unable to get subjects")
	}
	return subject.NewGetSubjectsOK().WithPayload(*subjects)
}
