package constants

const (
	TopicScoreScaledMax = 10
	CustomDateFormat    = "2006-01-02 15:04:05"
	EmptyString         = ""
	DefaultTestName     = "Practice Test"
	DefaultProgram      = "a42c5b78-71f4-490e-9474-04b8bd55a716"
	MinSchoolGrade      = 4
	MaxSchoolGrade      = 12
)

// MongoDB Collections
const (
	MongoDBCollectionInstitutes                = "institutes"
	MongoDBCollectionInstitutesArcive          = "institutes_archive"
	MongoDBCollectionEddyOwlUsers              = "users"
	MongoDBCollectionEddyOwlUsersArchive       = "users_archive"
	MongoDBCollectionEddyOwlInstructors        = "instructors"
	MongoDBCollectionEddyOwlInstructorsArchive = "instructors_archive"
	MongoDBCollectionTerms                     = "terms"
	MongoDBCollectionTermsArchive              = "terms_archive"
	MongoDBCollectionStudents                  = "students"
	MongoDBCollectionStudentsArchive           = "students_archive"
	MongoDBCollectionAssignments               = "assignments"
	MongoDBCollectionAssignmentsArchive        = "assignments_archive"
	MongoDBCollectionSubmissions               = "submissions"
	MongoDBCollectionSubmissionsArchive        = "submissions_archive"
)

// MongoDB View Collections
const (
	MongoDBViewDailySubmissionsCount               = "daily_submissions_count_view"
	MongoDBViewMonthlySubmissionsCount             = "monthly_submissions_count_view"
	MongoDBViewGradeSubmissionsCount               = "grade_submissions_count_view"
	MongoDBViewSubjectSubmissionsCount             = "subject_submissions_count_view"
	MongoDBViewStudentOverallPercentage            = "student_overall_percentage_view"
	MongoDBViewGradeSubjectSubmissionData          = "grade_subject_submission_data_view"
	MongoDBViewGradeOverallStats                   = "grade_overall_stats"
	MongoDBViewMonthlyGradeSubjectSubmissionsCount = "monthly_grade_subject_submissions_count_view"
	MongoDBViewSectionData                         = "section_data_view"
	MongoDBViewStudentChapterPerformance           = "student_chapter_performance_view"
	MongoDBViewSubmissions                         = "submissions_view"
	MongoDBViewSubjects                            = "subjects"
	MongoDBViewUserRoles                           = "user_roles_view"
)

// Roles
const (
	AdminRole      = 1
	InstructorRole = 2
	StudentRole    = 3
	AllRoles       = -1
)

var (
	RoleMap = map[int]string{
		AdminRole:      "AdminRole",
		InstructorRole: "InstructorRole",
		StudentRole:    "StudentRole",
		AllRoles:       "AllRoles",
	}
)

// AcademicStatus
const (
	AcademicStatusActive   = 0
	AcademicStatusInactive = 1
)

// User Status
const (
	UserStatusActive   = 0
	UserStatusInactive = 1
	UserStatusBlocked  = 2
)

// Assignment History Status
const (
	AssignmentHistoryStatusCreated               = 0
	AssignmentHistoryStatusRubricAIGenerated     = 1
	AssignmentHistoryStatusRubricFileGenerated   = 2
	AssignmentHistoryStatusRubricManualGenerated = 3
	AssignmentHistoryStatusTopicAssigned         = 4
	AssignmentHistoryStatusPublished             = 5
	AssignmentHistoryStatusUnPublished           = 6
	AssignmentHistoryStatusDealineReached        = 7
)

// Submission Status
const (
	SubmissionStatusDue        = 0
	SubmissionStatusSubmitted  = 1
	SubmissionStatusProcessing = 2
	SubmissionStatusGraded     = 3
	SubmissionStatusFailed     = 4
	SubmissionStatusPublished  = 5
)

var (
	StatusMap = map[int]string{
		SubmissionStatusDue:       "due",
		SubmissionStatusSubmitted: "processing",
		SubmissionStatusGraded:    "graded",
		SubmissionStatusFailed:    "failed",
		SubmissionStatusPublished: "published",
	}
)

var (
	Classes         = []int{6, 7, 8, 9, 10, 11, 12}
	ClassNames      = []string{"Grade 6", "Grade 7", "Grade 8", "Grade 9", "Grade 10", "Grade 11", "Grade 12"}
	DefaultSections = []string{"A", "B", "C", "D"}
)

// Span Names for Tracing
const (
	SpanGetAllDailySubmissionsCount        = "StatsDataProvider.GetAllDailySubmissionsCount"
	SpanGetMonthlySubmissionsCount         = "StatsDataProvider.GetMonthlySubmissionsCount"
	SpanGetAllMonthlySubmissionsCount      = "StatsDataProvider.GetAllMonthlySubmissionsCount"
	SpanGetGradeSubmissionsCount           = "StatsDataProvider.GetGradeSubmissionsCount"
	SpanGetAllGradeSubmissionsCount        = "StatsDataProvider.GetAllGradeSubmissionsCount"
	SpanGetSubjectSubmissionsCount         = "StatsDataProvider.GetSubjectSubmissionsCount"
	SpanGetAllSubjectSubmissionsCount      = "StatsDataProvider.GetAllSubjectSubmissionsCount"
	SpanGetGradeSubjectSubmissionsCount    = "StatsDataProvider.GetGradeSubjectSubmissionsCount"
	SpanGetAllGradeSubjectSubmissionsCount = "StatsDataProvider.GetAllGradeSubjectSubmissionsCount"
	SpanGetStudentOverallPerformance       = "StatsDataProvider.GetStudentOverallPerformance"
	SpanGetAllStudentOverallPerformance    = "StatsDataProvider.GetAllStudentOverallPerformance"
	SpanGetGradeSubjectData                = "StatsDataProvider.GetGradeSubjectData"
	SpanGetAllGradeSubjectData             = "StatsDataProvider.GetAllGradeSubjectData"
	SpanGetGradeOverallStats               = "StatsDataProvider.GetGradeOverallStats"
	SpanGetAllGradeOverallStats            = "StatsDataProvider.GetAllGradeOverallStats"
)
