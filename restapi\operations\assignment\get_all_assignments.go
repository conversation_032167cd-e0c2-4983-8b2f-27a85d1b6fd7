// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllAssignmentsHandlerFunc turns a function with the right signature into a get all assignments handler
type GetAllAssignmentsHandlerFunc func(GetAllAssignmentsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetAllAssignmentsHandlerFunc) Handle(params GetAllAssignmentsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllAssignmentsHandler interface for that can handle valid get all assignments params
type GetAllAssignmentsHandler interface {
	Handle(GetAllAssignmentsParams, interface{}) middleware.Responder
}

// NewGetAllAssignments creates a new http.Handler for the get all assignments operation
func NewGetAllAssignments(ctx *middleware.Context, handler GetAllAssignmentsHandler) *GetAllAssignments {
	return &GetAllAssignments{Context: ctx, Handler: handler}
}

/*
	GetAllAssignments swagger:route GET /institute/{instituteId}/assignment assignment getAllAssignments

# Get all assignments

Get all assignments by Institute Id
*/
type GetAllAssignments struct {
	Context *middleware.Context
	Handler GetAllAssignmentsHandler
}

func (o *GetAllAssignments) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllAssignmentsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
