// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAllTermsHandlerFunc turns a function with the right signature into a get all terms handler
type GetAllTermsHandlerFunc func(GetAllTermsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetAllTermsHandlerFunc) Handle(params GetAllTermsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAllTermsHandler interface for that can handle valid get all terms params
type GetAllTermsHandler interface {
	Handle(GetAllTermsParams, interface{}) middleware.Responder
}

// NewGetAllTerms creates a new http.Handler for the get all terms operation
func NewGetAllTerms(ctx *middleware.Context, handler GetAllTermsHandler) *GetAllTerms {
	return &GetAllTerms{Context: ctx, Handler: handler}
}

/*
	GetAllTerms swagger:route GET /institute/{instituteId}/terms term getAllTerms

# Get all terms

Get all terms by Institute Id
*/
type GetAllTerms struct {
	Context *middleware.Context
	Handler GetAllTermsHandler
}

func (o *GetAllTerms) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAllTermsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
