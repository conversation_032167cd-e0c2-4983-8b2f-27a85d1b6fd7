// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllFoldersOKCode is the HTTP code returned for type GetAllFoldersOK
const GetAllFoldersOKCode int = 200

/*
GetAllFoldersOK List of folders

swagger:response getAllFoldersOK
*/
type GetAllFoldersOK struct {

	/*
	  In: Body
	*/
	Payload []*models.Folder `json:"body,omitempty"`
}

// NewGetAllFoldersOK creates GetAllFoldersOK with default headers values
func NewGetAllFoldersOK() *GetAllFoldersOK {

	return &GetAllFoldersOK{}
}

// WithPayload adds the payload to the get all folders o k response
func (o *GetAllFoldersOK) WithPayload(payload []*models.Folder) *GetAllFoldersOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all folders o k response
func (o *GetAllFoldersOK) SetPayload(payload []*models.Folder) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllFoldersOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = make([]*models.Folder, 0, 50)
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllFoldersBadRequestCode is the HTTP code returned for type GetAllFoldersBadRequest
const GetAllFoldersBadRequestCode int = 400

/*
GetAllFoldersBadRequest Bad Request

swagger:response getAllFoldersBadRequest
*/
type GetAllFoldersBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllFoldersBadRequest creates GetAllFoldersBadRequest with default headers values
func NewGetAllFoldersBadRequest() *GetAllFoldersBadRequest {

	return &GetAllFoldersBadRequest{}
}

// WithPayload adds the payload to the get all folders bad request response
func (o *GetAllFoldersBadRequest) WithPayload(payload models.ErrorResponse) *GetAllFoldersBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all folders bad request response
func (o *GetAllFoldersBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllFoldersBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
