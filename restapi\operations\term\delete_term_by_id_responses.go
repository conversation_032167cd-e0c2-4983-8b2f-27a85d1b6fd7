// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteTermByIDOKCode is the HTTP code returned for type DeleteTermByIDOK
const DeleteTermByIDOKCode int = 200

/*
DeleteTermByIDOK Successful operation

swagger:response deleteTermByIdOK
*/
type DeleteTermByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDOK creates DeleteTermByIDOK with default headers values
func NewDeleteTermByIDOK() *DeleteTermByIDOK {

	return &DeleteTermByIDOK{}
}

// WithPayload adds the payload to the delete term by Id o k response
func (o *DeleteTermByIDOK) WithPayload(payload *models.SuccessResponse) *DeleteTermByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id o k response
func (o *DeleteTermByIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteTermByIDBadRequestCode is the HTTP code returned for type DeleteTermByIDBadRequest
const DeleteTermByIDBadRequestCode int = 400

/*
DeleteTermByIDBadRequest Bad Request

swagger:response deleteTermByIdBadRequest
*/
type DeleteTermByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDBadRequest creates DeleteTermByIDBadRequest with default headers values
func NewDeleteTermByIDBadRequest() *DeleteTermByIDBadRequest {

	return &DeleteTermByIDBadRequest{}
}

// WithPayload adds the payload to the delete term by Id bad request response
func (o *DeleteTermByIDBadRequest) WithPayload(payload models.ErrorResponse) *DeleteTermByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id bad request response
func (o *DeleteTermByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteTermByIDForbiddenCode is the HTTP code returned for type DeleteTermByIDForbidden
const DeleteTermByIDForbiddenCode int = 403

/*
DeleteTermByIDForbidden Forbidden

swagger:response deleteTermByIdForbidden
*/
type DeleteTermByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDForbidden creates DeleteTermByIDForbidden with default headers values
func NewDeleteTermByIDForbidden() *DeleteTermByIDForbidden {

	return &DeleteTermByIDForbidden{}
}

// WithPayload adds the payload to the delete term by Id forbidden response
func (o *DeleteTermByIDForbidden) WithPayload(payload models.ErrorResponse) *DeleteTermByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id forbidden response
func (o *DeleteTermByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteTermByIDNotFoundCode is the HTTP code returned for type DeleteTermByIDNotFound
const DeleteTermByIDNotFoundCode int = 404

/*
DeleteTermByIDNotFound Not Found

swagger:response deleteTermByIdNotFound
*/
type DeleteTermByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDNotFound creates DeleteTermByIDNotFound with default headers values
func NewDeleteTermByIDNotFound() *DeleteTermByIDNotFound {

	return &DeleteTermByIDNotFound{}
}

// WithPayload adds the payload to the delete term by Id not found response
func (o *DeleteTermByIDNotFound) WithPayload(payload models.ErrorResponse) *DeleteTermByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id not found response
func (o *DeleteTermByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteTermByIDTooManyRequestsCode is the HTTP code returned for type DeleteTermByIDTooManyRequests
const DeleteTermByIDTooManyRequestsCode int = 429

/*
DeleteTermByIDTooManyRequests Too Many Requests

swagger:response deleteTermByIdTooManyRequests
*/
type DeleteTermByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDTooManyRequests creates DeleteTermByIDTooManyRequests with default headers values
func NewDeleteTermByIDTooManyRequests() *DeleteTermByIDTooManyRequests {

	return &DeleteTermByIDTooManyRequests{}
}

// WithPayload adds the payload to the delete term by Id too many requests response
func (o *DeleteTermByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteTermByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id too many requests response
func (o *DeleteTermByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteTermByIDInternalServerErrorCode is the HTTP code returned for type DeleteTermByIDInternalServerError
const DeleteTermByIDInternalServerErrorCode int = 500

/*
DeleteTermByIDInternalServerError Internal Server Error

swagger:response deleteTermByIdInternalServerError
*/
type DeleteTermByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDInternalServerError creates DeleteTermByIDInternalServerError with default headers values
func NewDeleteTermByIDInternalServerError() *DeleteTermByIDInternalServerError {

	return &DeleteTermByIDInternalServerError{}
}

// WithPayload adds the payload to the delete term by Id internal server error response
func (o *DeleteTermByIDInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteTermByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id internal server error response
func (o *DeleteTermByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteTermByIDServiceUnavailableCode is the HTTP code returned for type DeleteTermByIDServiceUnavailable
const DeleteTermByIDServiceUnavailableCode int = 503

/*
DeleteTermByIDServiceUnavailable Service Unvailable

swagger:response deleteTermByIdServiceUnavailable
*/
type DeleteTermByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteTermByIDServiceUnavailable creates DeleteTermByIDServiceUnavailable with default headers values
func NewDeleteTermByIDServiceUnavailable() *DeleteTermByIDServiceUnavailable {

	return &DeleteTermByIDServiceUnavailable{}
}

// WithPayload adds the payload to the delete term by Id service unavailable response
func (o *DeleteTermByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteTermByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete term by Id service unavailable response
func (o *DeleteTermByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteTermByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
