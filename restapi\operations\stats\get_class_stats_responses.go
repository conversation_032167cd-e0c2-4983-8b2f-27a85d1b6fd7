// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassStatsOKCode is the HTTP code returned for type GetClassStatsOK
const GetClassStatsOKCode int = 200

/*
GetClassStatsOK Successful operation

swagger:response getClassStatsOK
*/
type GetClassStatsOK struct {

	/*
	  In: Body
	*/
	Payload *models.AllStats `json:"body,omitempty"`
}

// NewGetClassStatsOK creates GetClassStatsOK with default headers values
func NewGetClassStatsOK() *GetClassStatsOK {

	return &GetClassStatsOK{}
}

// WithPayload adds the payload to the get class stats o k response
func (o *GetClassStatsOK) WithPayload(payload *models.AllStats) *GetClassStatsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats o k response
func (o *GetClassStatsOK) SetPayload(payload *models.AllStats) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassStatsBadRequestCode is the HTTP code returned for type GetClassStatsBadRequest
const GetClassStatsBadRequestCode int = 400

/*
GetClassStatsBadRequest Bad Request

swagger:response getClassStatsBadRequest
*/
type GetClassStatsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsBadRequest creates GetClassStatsBadRequest with default headers values
func NewGetClassStatsBadRequest() *GetClassStatsBadRequest {

	return &GetClassStatsBadRequest{}
}

// WithPayload adds the payload to the get class stats bad request response
func (o *GetClassStatsBadRequest) WithPayload(payload models.ErrorResponse) *GetClassStatsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats bad request response
func (o *GetClassStatsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStatsForbiddenCode is the HTTP code returned for type GetClassStatsForbidden
const GetClassStatsForbiddenCode int = 403

/*
GetClassStatsForbidden Forbidden

swagger:response getClassStatsForbidden
*/
type GetClassStatsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsForbidden creates GetClassStatsForbidden with default headers values
func NewGetClassStatsForbidden() *GetClassStatsForbidden {

	return &GetClassStatsForbidden{}
}

// WithPayload adds the payload to the get class stats forbidden response
func (o *GetClassStatsForbidden) WithPayload(payload models.ErrorResponse) *GetClassStatsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats forbidden response
func (o *GetClassStatsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStatsNotFoundCode is the HTTP code returned for type GetClassStatsNotFound
const GetClassStatsNotFoundCode int = 404

/*
GetClassStatsNotFound Not Found

swagger:response getClassStatsNotFound
*/
type GetClassStatsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsNotFound creates GetClassStatsNotFound with default headers values
func NewGetClassStatsNotFound() *GetClassStatsNotFound {

	return &GetClassStatsNotFound{}
}

// WithPayload adds the payload to the get class stats not found response
func (o *GetClassStatsNotFound) WithPayload(payload models.ErrorResponse) *GetClassStatsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats not found response
func (o *GetClassStatsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStatsTooManyRequestsCode is the HTTP code returned for type GetClassStatsTooManyRequests
const GetClassStatsTooManyRequestsCode int = 429

/*
GetClassStatsTooManyRequests Too Many Requests

swagger:response getClassStatsTooManyRequests
*/
type GetClassStatsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsTooManyRequests creates GetClassStatsTooManyRequests with default headers values
func NewGetClassStatsTooManyRequests() *GetClassStatsTooManyRequests {

	return &GetClassStatsTooManyRequests{}
}

// WithPayload adds the payload to the get class stats too many requests response
func (o *GetClassStatsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassStatsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats too many requests response
func (o *GetClassStatsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStatsInternalServerErrorCode is the HTTP code returned for type GetClassStatsInternalServerError
const GetClassStatsInternalServerErrorCode int = 500

/*
GetClassStatsInternalServerError Internal Server Error

swagger:response getClassStatsInternalServerError
*/
type GetClassStatsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsInternalServerError creates GetClassStatsInternalServerError with default headers values
func NewGetClassStatsInternalServerError() *GetClassStatsInternalServerError {

	return &GetClassStatsInternalServerError{}
}

// WithPayload adds the payload to the get class stats internal server error response
func (o *GetClassStatsInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassStatsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats internal server error response
func (o *GetClassStatsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStatsServiceUnavailableCode is the HTTP code returned for type GetClassStatsServiceUnavailable
const GetClassStatsServiceUnavailableCode int = 503

/*
GetClassStatsServiceUnavailable Service Unvailable

swagger:response getClassStatsServiceUnavailable
*/
type GetClassStatsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStatsServiceUnavailable creates GetClassStatsServiceUnavailable with default headers values
func NewGetClassStatsServiceUnavailable() *GetClassStatsServiceUnavailable {

	return &GetClassStatsServiceUnavailable{}
}

// WithPayload adds the payload to the get class stats service unavailable response
func (o *GetClassStatsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassStatsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class stats service unavailable response
func (o *GetClassStatsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStatsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
