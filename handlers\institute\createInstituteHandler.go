package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/utils"
	"eddyowl-backend/validators"
	"time"

	"eddyowl-backend/restapi/operations/institute"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type createInstituteImpl struct {
	provider           data_providers.InstituteProvider
	tokenInfoProvider  utils.TokenInfoProvider
	instructorProvider data_providers.InstructorProvider
	termProvider       data_providers.TermProvider
	tracer             trace.Tracer
}

func NewCreateInstituteHandler(provider data_providers.InstituteProvider, tokenInfoProvider utils.TokenInfoProvider, instructorProvider data_providers.InstructorProvider, termProvider data_providers.TermProvider, tracer trace.Tracer) institute.CreateInstituteHandler {
	return &createInstituteImpl{provider: provider, tokenInfoProvider: tokenInfoProvider, instructorProvider: instructor<PERSON>rovider, termProvider: termProvider, tracer: tracer}
}

func (impl *createInstituteImpl) Handle(params institute.CreateInstituteParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateInstituteHandler")
	defer span.End()

	userId := principal.(string)

	// Validate request parameters
	valid := validators.CreateInstituteValidator(params)
	if !valid {
		return institute.NewCreateInstituteBadRequest().WithPayload("Invalid Institute Parameters")
	}

	// Create and validate institute entity
	instituteAddress := entities.NewAddress(
		params.NewInstitute.Address.AddressOne,
		params.NewInstitute.Address.AddressTwo,
		params.NewInstitute.Address.City,
		params.NewInstitute.Address.State,
		params.NewInstitute.Address.Pincode,
	)

	instituteEntity := entities.NewInstitute(
		params.NewInstitute.Name,
		params.NewInstitute.Program,
		instituteAddress,
		userId,
	)

	if err := instituteEntity.Validate(); err != nil {
		log.Error().Err(err).Msg("Invalid institute entity")
		return institute.NewCreateInstituteBadRequest().WithPayload("Invalid Institute Data")
	}

	// Create and validate admin instructor
	instructor := entities.NewInstructor(*instituteEntity.ID, userId, constants.AdminRole, userId)
	if err := instructor.Validate(); err != nil {
		log.Error().Err(err).Msg("Invalid instructor entity")
		return institute.NewCreateInstituteBadRequest().WithPayload("Invalid Instructor Data")
	}

	// Validate terms if provided
	var termEntities []*entities.Term
	if len(params.NewInstitute.Terms) > 0 {
		for _, termParam := range params.NewInstitute.Terms {
			startDate := time.Time(termParam.StartDate)
			endDate := time.Time(termParam.EndDate)

			if startDate.After(endDate) {
				return institute.NewCreateInstituteBadRequest().WithPayload("Term start date must be before end date")
			}

			termEntity := entities.NewTerm("", termParam.Name, &startDate, &endDate)
			termEntities = append(termEntities, termEntity)
		}

		// Check for overlapping terms
		for i := 0; i < len(termEntities); i++ {
			for j := i + 1; j < len(termEntities); j++ {
				ok, err := impl.termProvider.IsOverlapDateRange(ctx, *instituteEntity.ID, *termEntities[i].StartDate, *termEntities[i].EndDate)
				if err != nil {
					log.Error().Err(err).Msg("Failed to check term overlap")
					return institute.NewCreateInstituteInternalServerError().WithPayload("Unable to check term overlap")
				}
				if ok {
					return institute.NewCreateInstituteBadRequest().WithPayload("Terms overlap")
				}
			}
		}
	}

	// All validations passed, now call providers

	// Add institute
	instituteId, err := impl.provider.Add(ctx, instituteEntity)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create institute")
		return institute.NewCreateInstituteInternalServerError().WithPayload("Unable to create Institute")
	}

	// Update and add instructor
	instructor.InstituteID = &instituteId
	if _, err = impl.instructorProvider.Add(ctx, instructor); err != nil {
		log.Error().Err(err).
			Str("instituteId", instituteId).
			Str("userId", userId).
			Msg("Failed to add instructor")
		return institute.NewCreateInstituteInternalServerError().WithPayload("Unable to add Instructor to Institute")
	}

	// Add terms
	for _, termEntity := range termEntities {
		termEntity.InstituteID = &instituteId
		if _, err := impl.termProvider.Add(ctx, termEntity); err != nil {
			log.Error().Err(err).
				Str("instituteId", instituteId).
				Str("termName", *termEntity.Name).
				Msg("Failed to add term")
			return institute.NewCreateInstituteInternalServerError().WithPayload("Unable to add Terms to Institute")
		}
	}

	return institute.NewCreateInstituteOK().WithPayload(&models.SuccessResponse{
		ID:      instituteId,
		Message: "Successfully created Institute",
	})
}
