// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassMonthlyAssessmentsBySubject class monthly assessments by subject
//
// swagger:model ClassMonthlyAssessmentsBySubject
type ClassMonthlyAssessmentsBySubject struct {

	// assessments
	Assessments []int32 `json:"assessments"`

	// subjects
	Subjects string `json:"subjects,omitempty"`
}

// Validate validates this class monthly assessments by subject
func (m *ClassMonthlyAssessmentsBySubject) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class monthly assessments by subject based on context it is used
func (m *ClassMonthlyAssessmentsBySubject) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassMonthlyAssessmentsBySubject) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassMonthlyAssessmentsBySubject) UnmarshalBinary(b []byte) error {
	var res ClassMonthlyAssessmentsBySubject
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
