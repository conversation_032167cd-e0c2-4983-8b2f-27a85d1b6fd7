// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// AutoCreateAssignmentOKCode is the HTTP code returned for type AutoCreateAssignmentOK
const AutoCreateAssignmentOKCode int = 200

/*
AutoCreateAssignmentOK Successful operation

swagger:response autoCreateAssignmentOK
*/
type AutoCreateAssignmentOK struct {

	/*
	  In: Body
	*/
	Payload *models.QuestionList `json:"body,omitempty"`
}

// NewAutoCreateAssignmentOK creates AutoCreateAssignmentOK with default headers values
func NewAutoCreateAssignmentOK() *AutoCreateAssignmentOK {

	return &AutoCreateAssignmentOK{}
}

// WithPayload adds the payload to the auto create assignment o k response
func (o *AutoCreateAssignmentOK) WithPayload(payload *models.QuestionList) *AutoCreateAssignmentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment o k response
func (o *AutoCreateAssignmentOK) SetPayload(payload *models.QuestionList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// AutoCreateAssignmentBadRequestCode is the HTTP code returned for type AutoCreateAssignmentBadRequest
const AutoCreateAssignmentBadRequestCode int = 400

/*
AutoCreateAssignmentBadRequest Bad Request

swagger:response autoCreateAssignmentBadRequest
*/
type AutoCreateAssignmentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentBadRequest creates AutoCreateAssignmentBadRequest with default headers values
func NewAutoCreateAssignmentBadRequest() *AutoCreateAssignmentBadRequest {

	return &AutoCreateAssignmentBadRequest{}
}

// WithPayload adds the payload to the auto create assignment bad request response
func (o *AutoCreateAssignmentBadRequest) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment bad request response
func (o *AutoCreateAssignmentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoCreateAssignmentForbiddenCode is the HTTP code returned for type AutoCreateAssignmentForbidden
const AutoCreateAssignmentForbiddenCode int = 403

/*
AutoCreateAssignmentForbidden Forbidden

swagger:response autoCreateAssignmentForbidden
*/
type AutoCreateAssignmentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentForbidden creates AutoCreateAssignmentForbidden with default headers values
func NewAutoCreateAssignmentForbidden() *AutoCreateAssignmentForbidden {

	return &AutoCreateAssignmentForbidden{}
}

// WithPayload adds the payload to the auto create assignment forbidden response
func (o *AutoCreateAssignmentForbidden) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment forbidden response
func (o *AutoCreateAssignmentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoCreateAssignmentNotFoundCode is the HTTP code returned for type AutoCreateAssignmentNotFound
const AutoCreateAssignmentNotFoundCode int = 404

/*
AutoCreateAssignmentNotFound Not Found

swagger:response autoCreateAssignmentNotFound
*/
type AutoCreateAssignmentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentNotFound creates AutoCreateAssignmentNotFound with default headers values
func NewAutoCreateAssignmentNotFound() *AutoCreateAssignmentNotFound {

	return &AutoCreateAssignmentNotFound{}
}

// WithPayload adds the payload to the auto create assignment not found response
func (o *AutoCreateAssignmentNotFound) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment not found response
func (o *AutoCreateAssignmentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoCreateAssignmentTooManyRequestsCode is the HTTP code returned for type AutoCreateAssignmentTooManyRequests
const AutoCreateAssignmentTooManyRequestsCode int = 429

/*
AutoCreateAssignmentTooManyRequests Too Many Requests

swagger:response autoCreateAssignmentTooManyRequests
*/
type AutoCreateAssignmentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentTooManyRequests creates AutoCreateAssignmentTooManyRequests with default headers values
func NewAutoCreateAssignmentTooManyRequests() *AutoCreateAssignmentTooManyRequests {

	return &AutoCreateAssignmentTooManyRequests{}
}

// WithPayload adds the payload to the auto create assignment too many requests response
func (o *AutoCreateAssignmentTooManyRequests) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment too many requests response
func (o *AutoCreateAssignmentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoCreateAssignmentInternalServerErrorCode is the HTTP code returned for type AutoCreateAssignmentInternalServerError
const AutoCreateAssignmentInternalServerErrorCode int = 500

/*
AutoCreateAssignmentInternalServerError Internal Server Error

swagger:response autoCreateAssignmentInternalServerError
*/
type AutoCreateAssignmentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentInternalServerError creates AutoCreateAssignmentInternalServerError with default headers values
func NewAutoCreateAssignmentInternalServerError() *AutoCreateAssignmentInternalServerError {

	return &AutoCreateAssignmentInternalServerError{}
}

// WithPayload adds the payload to the auto create assignment internal server error response
func (o *AutoCreateAssignmentInternalServerError) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment internal server error response
func (o *AutoCreateAssignmentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AutoCreateAssignmentServiceUnavailableCode is the HTTP code returned for type AutoCreateAssignmentServiceUnavailable
const AutoCreateAssignmentServiceUnavailableCode int = 503

/*
AutoCreateAssignmentServiceUnavailable Service Unvailable

swagger:response autoCreateAssignmentServiceUnavailable
*/
type AutoCreateAssignmentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAutoCreateAssignmentServiceUnavailable creates AutoCreateAssignmentServiceUnavailable with default headers values
func NewAutoCreateAssignmentServiceUnavailable() *AutoCreateAssignmentServiceUnavailable {

	return &AutoCreateAssignmentServiceUnavailable{}
}

// WithPayload adds the payload to the auto create assignment service unavailable response
func (o *AutoCreateAssignmentServiceUnavailable) WithPayload(payload models.ErrorResponse) *AutoCreateAssignmentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the auto create assignment service unavailable response
func (o *AutoCreateAssignmentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AutoCreateAssignmentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
