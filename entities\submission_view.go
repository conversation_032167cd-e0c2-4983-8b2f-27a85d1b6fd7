package entities

import (
	"time"
)

type SubmissionView struct {
	ID                 *string                `json:"id" bson:"_id"`
	InstituteID        *string                `json:"institute_id" bson:"institute_id"`
	AssignmentID       *string                `json:"assignment_id" bson:"assignment_id"`
	StudentID          *string                `json:"student_id" bson:"student_id"`
	History            *[]StatusHistory       `json:"history" bson:"history"`
	StudentResponses   *[]FullStudentResponse `json:"student_responses" bson:"student_responses"`
	MissedQuestions    *[]int32               `json:"missed_questions" bson:"missed_questions"`
	ImageIds           *[]string              `json:"image_ids" bson:"image_ids"`
	CreatedAt          *time.Time             `json:"created_at" bson:"created_at"`
	CreatedBy          *string                `json:"created_by" bson:"created_by"`
	UpdatedAt          *time.Time             `json:"updated_at" bson:"updated_at"`
	UpdatedBy          *string                `json:"updated_by" bson:"updated_by"`
	TotalAchievedScore *float64               `json:"total_achieved_score" bson:"total_achieved_score"`

	// Student details
	StudentGrade      *int32  `json:"student_grade" bson:"student_grade"`
	StudentSection    *string `json:"student_section" bson:"student_section"`
	StudentRollNumber *int32  `json:"student_roll_number" bson:"student_roll_number"`
	StudentFirstName  *string `json:"student_first_name" bson:"student_first_name"`
	StudentLastName   *string `json:"student_last_name" bson:"student_last_name"`
	StudentEmail      *string `json:"student_email" bson:"student_email"`

	// Assignment details
	AssignmentName *string `json:"assignment_name" bson:"assignment_name"`
	Grade          *int32  `json:"grade" bson:"grade"`
	Subject        *string `json:"subject" bson:"subject"`
	TermID         *string `json:"term_id" bson:"term_id"`
	TotalScore     *int32  `json:"total_score" bson:"total_score"`
}

type FullStudentResponse struct {
	QuestionNumber int      `json:"question_number" bson:"question_number"`
	Response       *string  `json:"response" bson:"response"`
	Score          *float64 `json:"score" bson:"score"`
	Feedback       *string  `json:"feedback" bson:"feedback"`
	Question       *string  `json:"question" bson:"question"`
	QuestionScore  *int32   `json:"questionScore" bson:"questionScore"`
	Rubric         *string  `json:"rubric" bson:"rubric"`
	Topics         *[]Topic `json:"topics" bson:"topics"`
}
