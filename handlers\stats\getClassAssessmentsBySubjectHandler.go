package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassAssessmentsBySubjectImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassAssessmentsBySubjectHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassAssessmentsBySubjectHandler {
	return &getClassAssessmentsBySubjectImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassAssessmentsBySubjectImpl) Handle(params stats.GetClassAssessmentsBySubjectParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassAssessmentsBySubjectHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassAssessmentsBySubjectInternalServerError().WithPayload("Unable to resolve term")
	}

	// Initialize response
	response := &models.ClassAssessmentsBySubject{
		Subjects:    make([]string, 0),
		Assessments: make([]int32, 0),
	}

	// Get submissions count for all subjects in the class
	result, err := impl.statsProvider.GetGradeSubjectSubmissionsCount(ctx, params.InstituteID, termID, params.Class, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get submissions count")
		return stats.NewGetClassAssessmentsBySubjectInternalServerError().WithPayload("Unable to get submissions count")
	}

	for _, subject := range *result {
		response.Subjects = append(response.Subjects, subject.Subject)
		response.Assessments = append(response.Assessments, subject.SubmissionCount)
	}

	return stats.NewGetClassAssessmentsBySubjectOK().WithPayload(response)
}
