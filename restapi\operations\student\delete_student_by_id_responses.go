// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteStudentByIDOKCode is the HTTP code returned for type DeleteStudentByIDOK
const DeleteStudentByIDOKCode int = 200

/*
DeleteStudentByIDOK Successful operation

swagger:response deleteStudentByIdOK
*/
type DeleteStudentByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDOK creates DeleteStudentByIDOK with default headers values
func NewDeleteStudentByIDOK() *DeleteStudentByIDOK {

	return &DeleteStudentByIDOK{}
}

// WithPayload adds the payload to the delete student by Id o k response
func (o *DeleteStudentByIDOK) WithPayload(payload *models.SuccessResponse) *DeleteStudentByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id o k response
func (o *DeleteStudentByIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteStudentByIDBadRequestCode is the HTTP code returned for type DeleteStudentByIDBadRequest
const DeleteStudentByIDBadRequestCode int = 400

/*
DeleteStudentByIDBadRequest Bad Request

swagger:response deleteStudentByIdBadRequest
*/
type DeleteStudentByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDBadRequest creates DeleteStudentByIDBadRequest with default headers values
func NewDeleteStudentByIDBadRequest() *DeleteStudentByIDBadRequest {

	return &DeleteStudentByIDBadRequest{}
}

// WithPayload adds the payload to the delete student by Id bad request response
func (o *DeleteStudentByIDBadRequest) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id bad request response
func (o *DeleteStudentByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteStudentByIDForbiddenCode is the HTTP code returned for type DeleteStudentByIDForbidden
const DeleteStudentByIDForbiddenCode int = 403

/*
DeleteStudentByIDForbidden Forbidden

swagger:response deleteStudentByIdForbidden
*/
type DeleteStudentByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDForbidden creates DeleteStudentByIDForbidden with default headers values
func NewDeleteStudentByIDForbidden() *DeleteStudentByIDForbidden {

	return &DeleteStudentByIDForbidden{}
}

// WithPayload adds the payload to the delete student by Id forbidden response
func (o *DeleteStudentByIDForbidden) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id forbidden response
func (o *DeleteStudentByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteStudentByIDNotFoundCode is the HTTP code returned for type DeleteStudentByIDNotFound
const DeleteStudentByIDNotFoundCode int = 404

/*
DeleteStudentByIDNotFound Not Found

swagger:response deleteStudentByIdNotFound
*/
type DeleteStudentByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDNotFound creates DeleteStudentByIDNotFound with default headers values
func NewDeleteStudentByIDNotFound() *DeleteStudentByIDNotFound {

	return &DeleteStudentByIDNotFound{}
}

// WithPayload adds the payload to the delete student by Id not found response
func (o *DeleteStudentByIDNotFound) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id not found response
func (o *DeleteStudentByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteStudentByIDTooManyRequestsCode is the HTTP code returned for type DeleteStudentByIDTooManyRequests
const DeleteStudentByIDTooManyRequestsCode int = 429

/*
DeleteStudentByIDTooManyRequests Too Many Requests

swagger:response deleteStudentByIdTooManyRequests
*/
type DeleteStudentByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDTooManyRequests creates DeleteStudentByIDTooManyRequests with default headers values
func NewDeleteStudentByIDTooManyRequests() *DeleteStudentByIDTooManyRequests {

	return &DeleteStudentByIDTooManyRequests{}
}

// WithPayload adds the payload to the delete student by Id too many requests response
func (o *DeleteStudentByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id too many requests response
func (o *DeleteStudentByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteStudentByIDInternalServerErrorCode is the HTTP code returned for type DeleteStudentByIDInternalServerError
const DeleteStudentByIDInternalServerErrorCode int = 500

/*
DeleteStudentByIDInternalServerError Internal Server Error

swagger:response deleteStudentByIdInternalServerError
*/
type DeleteStudentByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDInternalServerError creates DeleteStudentByIDInternalServerError with default headers values
func NewDeleteStudentByIDInternalServerError() *DeleteStudentByIDInternalServerError {

	return &DeleteStudentByIDInternalServerError{}
}

// WithPayload adds the payload to the delete student by Id internal server error response
func (o *DeleteStudentByIDInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id internal server error response
func (o *DeleteStudentByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteStudentByIDServiceUnavailableCode is the HTTP code returned for type DeleteStudentByIDServiceUnavailable
const DeleteStudentByIDServiceUnavailableCode int = 503

/*
DeleteStudentByIDServiceUnavailable Service Unvailable

swagger:response deleteStudentByIdServiceUnavailable
*/
type DeleteStudentByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteStudentByIDServiceUnavailable creates DeleteStudentByIDServiceUnavailable with default headers values
func NewDeleteStudentByIDServiceUnavailable() *DeleteStudentByIDServiceUnavailable {

	return &DeleteStudentByIDServiceUnavailable{}
}

// WithPayload adds the payload to the delete student by Id service unavailable response
func (o *DeleteStudentByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteStudentByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete student by Id service unavailable response
func (o *DeleteStudentByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteStudentByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
