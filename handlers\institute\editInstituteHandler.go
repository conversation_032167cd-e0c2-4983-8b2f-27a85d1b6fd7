package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/institute"
	"eddyowl-backend/utils"

	"slices"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type editInstituteImpl struct {
	provider          data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewEditInstituteHandler(provider data_providers.InstituteProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) institute.EditInstituteHandler {
	return &editInstituteImpl{
		provider:          provider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *editInstituteImpl) Handle(params institute.EditInstituteParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : EditInstituteHandler")
	defer span.End()

	principal = principal.(string)

	if params.InstituteID == constants.EmptyString {
		return institute.NewEditInstituteBadRequest().WithPayload("Invalid InstituteID")
	}

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return institute.NewEditInstituteForbidden().WithPayload("Unauthorized")
	}

	// Get existing institute first
	existingInstitute, err := impl.provider.Get(ctx, params.InstituteID)
	if err != nil {
		log.Error().
			Str("instituteId", params.InstituteID).
			Err(err).
			Msg("Failed to get institute")

		if utils.IsNoDocumentFound(err) {
			return institute.NewEditInstituteNotFound().WithPayload("Institute not found")
		}

		return institute.NewEditInstituteInternalServerError().WithPayload("Unable to get institute")
	}

	userId := principal.(string)

	// Update only provided fields
	if params.Institute.Name != "" {
		existingInstitute.Name = &params.Institute.Name
	}

	if params.Institute.Address != nil {
		// Validate address fields
		if params.Institute.Address.AddressOne == "" ||
			params.Institute.Address.City == "" ||
			params.Institute.Address.State == "" ||
			params.Institute.Address.Pincode == "" {
			return institute.NewEditInstituteBadRequest().WithPayload("All address fields (address line 1, city, state, pincode) are required")
		}

		address := entities.NewAddress(
			params.Institute.Address.AddressOne,
			params.Institute.Address.AddressTwo,
			params.Institute.Address.City,
			params.Institute.Address.State,
			params.Institute.Address.Pincode,
		)
		if err := address.Validate(); err != nil {
			return institute.NewEditInstituteBadRequest().WithPayload("Invalid address data")
		}
		existingInstitute.Address = address
	}

	// Handle AvailableSections if provided in request
	if params.Institute.AvailableSections != nil {
		var mergedSections []string
		
		// If existing sections present, start with their capacity
		if existingInstitute.AvailableSections != nil {
			mergedSections = *existingInstitute.AvailableSections
		}
		
		// Append new sections only if they don't exist
		for _, newSection := range params.Institute.AvailableSections {
			exists := slices.Contains(mergedSections, newSection)
			if !exists {
				mergedSections = append(mergedSections, newSection)
			}
		}
		
		existingInstitute.AvailableSections = &mergedSections
	}

	existingInstitute.UpdatedBy = &userId

	// Validate updated institute model
	if err := existingInstitute.Validate(); err != nil {
		log.Error().
			Str("instituteId", params.InstituteID).
			Err(err).
			Msg("Invalid institute data")
		return institute.NewEditInstituteBadRequest().WithPayload("Invalid institute data")
	}

	// Update institute
	err = impl.provider.Edit(ctx, params.InstituteID, existingInstitute)
	if err != nil {
		log.Error().
			Str("instituteId", params.InstituteID).
			Err(err).
			Msg("Failed to edit institute")

		return institute.NewEditInstituteInternalServerError().WithPayload("Unable to edit institute")
	}

	return institute.NewEditInstituteOK().WithPayload(&models.SuccessResponse{
		ID:      params.InstituteID,
		Message: "Successfully edited institute",
	})
}
