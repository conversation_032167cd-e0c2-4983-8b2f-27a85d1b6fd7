// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetStudentByEmailOKCode is the HTTP code returned for type GetStudentByEmailOK
const GetStudentByEmailOKCode int = 200

/*
GetStudentByEmailOK Successful operation

swagger:response getStudentByEmailOK
*/
type GetStudentByEmailOK struct {

	/*
	  In: Body
	*/
	Payload *models.Student `json:"body,omitempty"`
}

// NewGetStudentByEmailOK creates GetStudentByEmailOK with default headers values
func NewGetStudentByEmailOK() *GetStudentByEmailOK {

	return &GetStudentByEmailOK{}
}

// WithPayload adds the payload to the get student by email o k response
func (o *GetStudentByEmailOK) WithPayload(payload *models.Student) *GetStudentByEmailOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email o k response
func (o *GetStudentByEmailOK) SetPayload(payload *models.Student) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetStudentByEmailBadRequestCode is the HTTP code returned for type GetStudentByEmailBadRequest
const GetStudentByEmailBadRequestCode int = 400

/*
GetStudentByEmailBadRequest Bad Request

swagger:response getStudentByEmailBadRequest
*/
type GetStudentByEmailBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailBadRequest creates GetStudentByEmailBadRequest with default headers values
func NewGetStudentByEmailBadRequest() *GetStudentByEmailBadRequest {

	return &GetStudentByEmailBadRequest{}
}

// WithPayload adds the payload to the get student by email bad request response
func (o *GetStudentByEmailBadRequest) WithPayload(payload models.ErrorResponse) *GetStudentByEmailBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email bad request response
func (o *GetStudentByEmailBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByEmailForbiddenCode is the HTTP code returned for type GetStudentByEmailForbidden
const GetStudentByEmailForbiddenCode int = 403

/*
GetStudentByEmailForbidden Forbidden

swagger:response getStudentByEmailForbidden
*/
type GetStudentByEmailForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailForbidden creates GetStudentByEmailForbidden with default headers values
func NewGetStudentByEmailForbidden() *GetStudentByEmailForbidden {

	return &GetStudentByEmailForbidden{}
}

// WithPayload adds the payload to the get student by email forbidden response
func (o *GetStudentByEmailForbidden) WithPayload(payload models.ErrorResponse) *GetStudentByEmailForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email forbidden response
func (o *GetStudentByEmailForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByEmailNotFoundCode is the HTTP code returned for type GetStudentByEmailNotFound
const GetStudentByEmailNotFoundCode int = 404

/*
GetStudentByEmailNotFound Not Found

swagger:response getStudentByEmailNotFound
*/
type GetStudentByEmailNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailNotFound creates GetStudentByEmailNotFound with default headers values
func NewGetStudentByEmailNotFound() *GetStudentByEmailNotFound {

	return &GetStudentByEmailNotFound{}
}

// WithPayload adds the payload to the get student by email not found response
func (o *GetStudentByEmailNotFound) WithPayload(payload models.ErrorResponse) *GetStudentByEmailNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email not found response
func (o *GetStudentByEmailNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByEmailTooManyRequestsCode is the HTTP code returned for type GetStudentByEmailTooManyRequests
const GetStudentByEmailTooManyRequestsCode int = 429

/*
GetStudentByEmailTooManyRequests Too Many Requests

swagger:response getStudentByEmailTooManyRequests
*/
type GetStudentByEmailTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailTooManyRequests creates GetStudentByEmailTooManyRequests with default headers values
func NewGetStudentByEmailTooManyRequests() *GetStudentByEmailTooManyRequests {

	return &GetStudentByEmailTooManyRequests{}
}

// WithPayload adds the payload to the get student by email too many requests response
func (o *GetStudentByEmailTooManyRequests) WithPayload(payload models.ErrorResponse) *GetStudentByEmailTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email too many requests response
func (o *GetStudentByEmailTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByEmailInternalServerErrorCode is the HTTP code returned for type GetStudentByEmailInternalServerError
const GetStudentByEmailInternalServerErrorCode int = 500

/*
GetStudentByEmailInternalServerError Internal Server Error

swagger:response getStudentByEmailInternalServerError
*/
type GetStudentByEmailInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailInternalServerError creates GetStudentByEmailInternalServerError with default headers values
func NewGetStudentByEmailInternalServerError() *GetStudentByEmailInternalServerError {

	return &GetStudentByEmailInternalServerError{}
}

// WithPayload adds the payload to the get student by email internal server error response
func (o *GetStudentByEmailInternalServerError) WithPayload(payload models.ErrorResponse) *GetStudentByEmailInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email internal server error response
func (o *GetStudentByEmailInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetStudentByEmailServiceUnavailableCode is the HTTP code returned for type GetStudentByEmailServiceUnavailable
const GetStudentByEmailServiceUnavailableCode int = 503

/*
GetStudentByEmailServiceUnavailable Service Unavailable

swagger:response getStudentByEmailServiceUnavailable
*/
type GetStudentByEmailServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetStudentByEmailServiceUnavailable creates GetStudentByEmailServiceUnavailable with default headers values
func NewGetStudentByEmailServiceUnavailable() *GetStudentByEmailServiceUnavailable {

	return &GetStudentByEmailServiceUnavailable{}
}

// WithPayload adds the payload to the get student by email service unavailable response
func (o *GetStudentByEmailServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetStudentByEmailServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get student by email service unavailable response
func (o *GetStudentByEmailServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetStudentByEmailServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
