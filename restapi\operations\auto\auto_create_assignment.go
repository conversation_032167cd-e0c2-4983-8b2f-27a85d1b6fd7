// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// AutoCreateAssignmentHandlerFunc turns a function with the right signature into a auto create assignment handler
type AutoCreateAssignmentHandlerFunc func(AutoCreateAssignmentParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn AutoCreateAssignmentHandlerFunc) Handle(params AutoCreateAssignmentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// AutoCreateAssignmentHandler interface for that can handle valid auto create assignment params
type AutoCreateAssignmentHandler interface {
	Handle(AutoCreateAssignmentParams, interface{}) middleware.Responder
}

// NewAutoCreateAssignment creates a new http.Handler for the auto create assignment operation
func NewAutoCreateAssignment(ctx *middleware.Context, handler AutoCreateAssignmentHandler) *AutoCreateAssignment {
	return &AutoCreateAssignment{Context: ctx, Handler: handler}
}

/*
	AutoCreateAssignment swagger:route POST /institute/{instituteId}/assignment/auto auto autoCreateAssignment

# Create new assignment from upload

Create new assignment from upload
*/
type AutoCreateAssignment struct {
	Context *middleware.Context
	Handler AutoCreateAssignmentHandler
}

func (o *AutoCreateAssignment) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewAutoCreateAssignmentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
