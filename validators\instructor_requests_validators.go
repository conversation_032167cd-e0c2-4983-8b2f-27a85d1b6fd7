package validators

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/restapi/operations/instructor"
)

func AddInstructorByInstituteIDValidator(params instructor.AddInstructorByInstituteIDParams) bool {
	if params.InstituteID == constants.EmptyString || params.Instructor.Email == constants.EmptyString || params.Instructor.FirstName == constants.EmptyString || params.Instructor.LastName == constants.EmptyString || params.Instructor.Role == constants.StudentRole {
		return false
	}
	return true
}

func GetInstructorByInstituteIDValidator(params instructor.GetInstructorByInstituteIDParams) bool {
	return params.InstituteID != constants.EmptyString
}

func DeleteInstructorByIDValidator(params instructor.DeleteInstructorByEmailParams) bool {
	if params.InstituteID == constants.EmptyString || params.Email == constants.EmptyString {
		return false
	}
	return true
}

func EditInstructorByInstituteIDValidator(params instructor.EditInstructorByInstituteIDParams) bool {
	if params.InstituteID == constants.EmptyString || params.Instructor.Email == constants.EmptyString || params.Instructor.FirstName == constants.EmptyString || params.Instructor.LastName == constants.EmptyString || params.Instructor.Role == constants.StudentRole {
		return false
	}
	return true
}
