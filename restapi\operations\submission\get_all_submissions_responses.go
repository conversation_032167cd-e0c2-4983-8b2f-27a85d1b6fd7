// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllSubmissionsOKCode is the HTTP code returned for type GetAllSubmissionsOK
const GetAllSubmissionsOKCode int = 200

/*
GetAllSubmissionsOK Successful operation

swagger:response getAllSubmissionsOK
*/
type GetAllSubmissionsOK struct {

	/*
	  In: Body
	*/
	Payload models.SubmissionList `json:"body,omitempty"`
}

// NewGetAllSubmissionsOK creates GetAllSubmissionsOK with default headers values
func NewGetAllSubmissionsOK() *GetAllSubmissionsOK {

	return &GetAllSubmissionsOK{}
}

// WithPayload adds the payload to the get all submissions o k response
func (o *GetAllSubmissionsOK) WithPayload(payload models.SubmissionList) *GetAllSubmissionsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions o k response
func (o *GetAllSubmissionsOK) SetPayload(payload models.SubmissionList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.SubmissionList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsBadRequestCode is the HTTP code returned for type GetAllSubmissionsBadRequest
const GetAllSubmissionsBadRequestCode int = 400

/*
GetAllSubmissionsBadRequest Bad Request

swagger:response getAllSubmissionsBadRequest
*/
type GetAllSubmissionsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsBadRequest creates GetAllSubmissionsBadRequest with default headers values
func NewGetAllSubmissionsBadRequest() *GetAllSubmissionsBadRequest {

	return &GetAllSubmissionsBadRequest{}
}

// WithPayload adds the payload to the get all submissions bad request response
func (o *GetAllSubmissionsBadRequest) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions bad request response
func (o *GetAllSubmissionsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsForbiddenCode is the HTTP code returned for type GetAllSubmissionsForbidden
const GetAllSubmissionsForbiddenCode int = 403

/*
GetAllSubmissionsForbidden Forbidden

swagger:response getAllSubmissionsForbidden
*/
type GetAllSubmissionsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsForbidden creates GetAllSubmissionsForbidden with default headers values
func NewGetAllSubmissionsForbidden() *GetAllSubmissionsForbidden {

	return &GetAllSubmissionsForbidden{}
}

// WithPayload adds the payload to the get all submissions forbidden response
func (o *GetAllSubmissionsForbidden) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions forbidden response
func (o *GetAllSubmissionsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsNotFoundCode is the HTTP code returned for type GetAllSubmissionsNotFound
const GetAllSubmissionsNotFoundCode int = 404

/*
GetAllSubmissionsNotFound Not Found

swagger:response getAllSubmissionsNotFound
*/
type GetAllSubmissionsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsNotFound creates GetAllSubmissionsNotFound with default headers values
func NewGetAllSubmissionsNotFound() *GetAllSubmissionsNotFound {

	return &GetAllSubmissionsNotFound{}
}

// WithPayload adds the payload to the get all submissions not found response
func (o *GetAllSubmissionsNotFound) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions not found response
func (o *GetAllSubmissionsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsTooManyRequestsCode is the HTTP code returned for type GetAllSubmissionsTooManyRequests
const GetAllSubmissionsTooManyRequestsCode int = 429

/*
GetAllSubmissionsTooManyRequests Too Many Requests

swagger:response getAllSubmissionsTooManyRequests
*/
type GetAllSubmissionsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsTooManyRequests creates GetAllSubmissionsTooManyRequests with default headers values
func NewGetAllSubmissionsTooManyRequests() *GetAllSubmissionsTooManyRequests {

	return &GetAllSubmissionsTooManyRequests{}
}

// WithPayload adds the payload to the get all submissions too many requests response
func (o *GetAllSubmissionsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions too many requests response
func (o *GetAllSubmissionsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsInternalServerErrorCode is the HTTP code returned for type GetAllSubmissionsInternalServerError
const GetAllSubmissionsInternalServerErrorCode int = 500

/*
GetAllSubmissionsInternalServerError Internal Server Error

swagger:response getAllSubmissionsInternalServerError
*/
type GetAllSubmissionsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsInternalServerError creates GetAllSubmissionsInternalServerError with default headers values
func NewGetAllSubmissionsInternalServerError() *GetAllSubmissionsInternalServerError {

	return &GetAllSubmissionsInternalServerError{}
}

// WithPayload adds the payload to the get all submissions internal server error response
func (o *GetAllSubmissionsInternalServerError) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions internal server error response
func (o *GetAllSubmissionsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllSubmissionsServiceUnavailableCode is the HTTP code returned for type GetAllSubmissionsServiceUnavailable
const GetAllSubmissionsServiceUnavailableCode int = 503

/*
GetAllSubmissionsServiceUnavailable Service Unvailable

swagger:response getAllSubmissionsServiceUnavailable
*/
type GetAllSubmissionsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllSubmissionsServiceUnavailable creates GetAllSubmissionsServiceUnavailable with default headers values
func NewGetAllSubmissionsServiceUnavailable() *GetAllSubmissionsServiceUnavailable {

	return &GetAllSubmissionsServiceUnavailable{}
}

// WithPayload adds the payload to the get all submissions service unavailable response
func (o *GetAllSubmissionsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAllSubmissionsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all submissions service unavailable response
func (o *GetAllSubmissionsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllSubmissionsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
