package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type deleteAssignmentByIDImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewDeleteAssignmentByIDHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.DeleteAssignmentByIDHandler {
	return &deleteAssignmentByIDImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *deleteAssignmentByIDImpl) Handle(params assignment.DeleteAssignmentByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteAssignmentByIDHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewDeleteAssignmentByIDForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.deleteAssignmentByIDValidator(ctx, params)
	if !valid {
		return validateResp
	}

	deletedBy := principal.(string)
	err = impl.assignmentProvider.Delete(ctx, params.AssignmentID, params.InstituteID, deletedBy)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete assignment")
		if errors.Is(err, mongo.ErrNoDocuments) {
			return assignment.NewDeleteAssignmentByIDNotFound().WithPayload("Assignment not found")
		}
		return assignment.NewDeleteAssignmentByIDInternalServerError().WithPayload("Failed to delete assignment")
	}

	return assignment.NewDeleteAssignmentByIDOK().WithPayload(&models.SuccessResponse{
		ID:      params.AssignmentID,
		Message: "Assignment deleted successfully",
	})
}

func (impl *deleteAssignmentByIDImpl) deleteAssignmentByIDValidator(ctx context.Context, params assignment.DeleteAssignmentByIDParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, assignment.NewDeleteAssignmentByIDBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewDeleteAssignmentByIDBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewDeleteAssignmentByIDInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, assignment.NewDeleteAssignmentByIDBadRequest().WithPayload("Invalid Assignment ID")
	}

	return true, nil
}
