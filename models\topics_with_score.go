// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// TopicsWithScore topics with score
//
// swagger:model TopicsWithScore
type TopicsWithScore struct {

	// topic
	Topic string `json:"topic,omitempty"`

	// topic score
	TopicScore float32 `json:"topicScore,omitempty"`
}

// Validate validates this topics with score
func (m *TopicsWithScore) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this topics with score based on context it is used
func (m *TopicsWithScore) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *TopicsWithScore) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *TopicsWithScore) UnmarshalBinary(b []byte) error {
	var res TopicsWithScore
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
