// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ChapterTopics chapter topics
//
// swagger:model ChapterTopics
type ChapterTopics struct {

	// chapter
	Chapter string `json:"chapter,omitempty"`

	// topics
	Topics []string `json:"topics"`
}

// Validate validates this chapter topics
func (m *ChapterTopics) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this chapter topics based on context it is used
func (m *ChapterTopics) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ChapterTopics) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ChapterTopics) UnmarshalBinary(b []byte) error {
	var res ChapterTopics
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
