// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateAssignmentOKCode is the HTTP code returned for type CreateAssignmentOK
const CreateAssignmentOKCode int = 200

/*
CreateAssignmentOK Successful operation

swagger:response createAssignmentOK
*/
type CreateAssignmentOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewCreateAssignmentOK creates CreateAssignmentOK with default headers values
func NewCreateAssignmentOK() *CreateAssignmentOK {

	return &CreateAssignmentOK{}
}

// WithPayload adds the payload to the create assignment o k response
func (o *CreateAssignmentOK) WithPayload(payload *models.SuccessResponse) *CreateAssignmentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment o k response
func (o *CreateAssignmentOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateAssignmentBadRequestCode is the HTTP code returned for type CreateAssignmentBadRequest
const CreateAssignmentBadRequestCode int = 400

/*
CreateAssignmentBadRequest Bad Request

swagger:response createAssignmentBadRequest
*/
type CreateAssignmentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentBadRequest creates CreateAssignmentBadRequest with default headers values
func NewCreateAssignmentBadRequest() *CreateAssignmentBadRequest {

	return &CreateAssignmentBadRequest{}
}

// WithPayload adds the payload to the create assignment bad request response
func (o *CreateAssignmentBadRequest) WithPayload(payload models.ErrorResponse) *CreateAssignmentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment bad request response
func (o *CreateAssignmentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateAssignmentForbiddenCode is the HTTP code returned for type CreateAssignmentForbidden
const CreateAssignmentForbiddenCode int = 403

/*
CreateAssignmentForbidden Forbidden

swagger:response createAssignmentForbidden
*/
type CreateAssignmentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentForbidden creates CreateAssignmentForbidden with default headers values
func NewCreateAssignmentForbidden() *CreateAssignmentForbidden {

	return &CreateAssignmentForbidden{}
}

// WithPayload adds the payload to the create assignment forbidden response
func (o *CreateAssignmentForbidden) WithPayload(payload models.ErrorResponse) *CreateAssignmentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment forbidden response
func (o *CreateAssignmentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateAssignmentNotFoundCode is the HTTP code returned for type CreateAssignmentNotFound
const CreateAssignmentNotFoundCode int = 404

/*
CreateAssignmentNotFound Not Found

swagger:response createAssignmentNotFound
*/
type CreateAssignmentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentNotFound creates CreateAssignmentNotFound with default headers values
func NewCreateAssignmentNotFound() *CreateAssignmentNotFound {

	return &CreateAssignmentNotFound{}
}

// WithPayload adds the payload to the create assignment not found response
func (o *CreateAssignmentNotFound) WithPayload(payload models.ErrorResponse) *CreateAssignmentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment not found response
func (o *CreateAssignmentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateAssignmentTooManyRequestsCode is the HTTP code returned for type CreateAssignmentTooManyRequests
const CreateAssignmentTooManyRequestsCode int = 429

/*
CreateAssignmentTooManyRequests Too Many Requests

swagger:response createAssignmentTooManyRequests
*/
type CreateAssignmentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentTooManyRequests creates CreateAssignmentTooManyRequests with default headers values
func NewCreateAssignmentTooManyRequests() *CreateAssignmentTooManyRequests {

	return &CreateAssignmentTooManyRequests{}
}

// WithPayload adds the payload to the create assignment too many requests response
func (o *CreateAssignmentTooManyRequests) WithPayload(payload models.ErrorResponse) *CreateAssignmentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment too many requests response
func (o *CreateAssignmentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateAssignmentInternalServerErrorCode is the HTTP code returned for type CreateAssignmentInternalServerError
const CreateAssignmentInternalServerErrorCode int = 500

/*
CreateAssignmentInternalServerError Internal Server Error

swagger:response createAssignmentInternalServerError
*/
type CreateAssignmentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentInternalServerError creates CreateAssignmentInternalServerError with default headers values
func NewCreateAssignmentInternalServerError() *CreateAssignmentInternalServerError {

	return &CreateAssignmentInternalServerError{}
}

// WithPayload adds the payload to the create assignment internal server error response
func (o *CreateAssignmentInternalServerError) WithPayload(payload models.ErrorResponse) *CreateAssignmentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment internal server error response
func (o *CreateAssignmentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateAssignmentServiceUnavailableCode is the HTTP code returned for type CreateAssignmentServiceUnavailable
const CreateAssignmentServiceUnavailableCode int = 503

/*
CreateAssignmentServiceUnavailable Service Unvailable

swagger:response createAssignmentServiceUnavailable
*/
type CreateAssignmentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateAssignmentServiceUnavailable creates CreateAssignmentServiceUnavailable with default headers values
func NewCreateAssignmentServiceUnavailable() *CreateAssignmentServiceUnavailable {

	return &CreateAssignmentServiceUnavailable{}
}

// WithPayload adds the payload to the create assignment service unavailable response
func (o *CreateAssignmentServiceUnavailable) WithPayload(payload models.ErrorResponse) *CreateAssignmentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create assignment service unavailable response
func (o *CreateAssignmentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateAssignmentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
