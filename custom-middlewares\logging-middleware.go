package custom_middlewares

import (
	"bytes"
	"encoding/base64"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// LoggingMiddleware logs detailed request and response information
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Generate a unique trace ID for each request
		traceID := uuid.New().String()

		// Capture the start time
		start := time.Now()

		// Read and log request details
		var requestBody []byte
		if r.Body != nil {
			requestBody, _ = io.ReadAll(r.Body)
		}
		r.Body = io.NopCloser(bytes.NewBuffer(requestBody)) // Reset request body

		// Create a buffer to capture the request body
		requestBuffer := bytes.NewBuffer(requestBody)

		log.Info().
			Str("log_type", "api_request").
			Str("trace_id", traceID).
			Str("method", r.Method).
			Str("url", r.URL.String()).
			Str("path", r.URL.Path).
			Str("remote_addr", r.RemoteAddr).
			Str("content_type", r.Header.Get("Content-Type")).
			Str("user_agent", r.Header.Get("User-Agent")).
			Str("host", r.Host).
			Str("request_body", requestBuffer.String()).
			Msg("Incoming request")

		// Capture the response
		responseWriter := &responseCapture{ResponseWriter: w, statusCode: http.StatusOK, body: &bytes.Buffer{}}
		// Set the trace ID in the response headers
		w.Header().Set("X-Trace-ID", traceID)
		next.ServeHTTP(responseWriter, r)

		// Log response details
		duration := time.Since(start)

		log.Info().
			Str("log_type", "api_response").
			Str("trace_id", traceID).
			Int("status_code", responseWriter.statusCode).
			Dur("duration", duration).
			Str("content_type", w.Header().Get("Content-Type")).
			Str("response_body", base64.RawStdEncoding.EncodeToString(responseWriter.body.Bytes())).
			Msg("Outgoing response")
	})
}

// responseCapture captures the response body for logging
type responseCapture struct {
	http.ResponseWriter
	statusCode int
	body       *bytes.Buffer
}

func (rw *responseCapture) Write(b []byte) (int, error) {
	rw.body.Write(b)
	return rw.ResponseWriter.Write(b)
}

func (rw *responseCapture) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}
