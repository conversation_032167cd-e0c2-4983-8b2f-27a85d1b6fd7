package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getStudentImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	termProvider      data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetStudentHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) student.GetStudentByIDHandler {
	return &getStudentImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		termProvider:      termProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *getStudentImpl) Handle(params student.GetStudentByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetStudentByIDHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewGetStudentByIDForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.getStudentValidator(params)
	if !valid {
		return validateResp
	}

	termId := ""
	if params.TermID != nil {
		term, err := impl.termProvider.Get(ctx, *params.TermID, params.InstituteID)
		if err != nil {
			log.Error().Msg(err.Error())
			if errors.Is(err, mongo.ErrNoDocuments) {
				return student.NewCreateNewStudentBadRequest().WithPayload("Invalid Term ID")
			}
			return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Term")
		}
		if term != nil {
			termId = *term.ID
		}
	} else {
		currentTerm, err := impl.termProvider.GetCurrent(ctx, params.InstituteID)
		if err != (nil) {
			log.Error().Msg(err.Error())
			if errors.Is(err, mongo.ErrNoDocuments) {
				return student.NewCreateNewStudentBadRequest().WithPayload("Current Tern Not Found")
			}
			return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Term")
		}
		if currentTerm != nil {
			termId = *currentTerm.ID
		}
	}
	if termId == "" {
		return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Term")
	}

	studentEntity, err := impl.studentProvider.Get(ctx, params.InstituteID, params.StudentID)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewGetStudentByIDInternalServerError().WithPayload("Unable to get Student")
	}

	studentResponse := MapStudentEntityToModel(studentEntity, termId)
	return student.NewGetStudentByIDOK().WithPayload(studentResponse)

}

func (impl *getStudentImpl) getStudentValidator(params student.GetStudentByIDParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "getStudentValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, student.NewGetStudentByIDBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewGetStudentByIDBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, student.NewGetStudentByIDInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.StudentID == constants.EmptyString {
		return false, student.NewGetStudentByIDBadRequest().WithPayload("Invalid Student ID")
	}
	return true, nil
}

func MapStudentEntityToModel(studentEntity *entities.Student, termId string) *models.Student {
	if studentEntity == nil {
		return nil
	}

	studentResponse := &models.Student{
		StudentID:  *studentEntity.StudentID,
		Class:      int32(studentEntity.AcademicHistory[termId].Grade),
		RollNumber: int32(studentEntity.AcademicHistory[termId].RollNumber),
	}
	if studentEntity.Email != nil {
		studentResponse.Email = *studentEntity.Email
	}
	if studentEntity.FirstName != nil {
		studentResponse.FirstName = *studentEntity.FirstName
	}
	if studentEntity.LastName != nil {
		studentResponse.LastName = *studentEntity.LastName
	}
	if studentEntity.AcademicHistory[termId].Section != nil {
		studentResponse.Section = *studentEntity.AcademicHistory[termId].Section
	}
	return studentResponse
}
