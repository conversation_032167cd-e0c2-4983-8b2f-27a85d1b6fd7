package data_providers

import (
	"context"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"

	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
)

var (
	ErrNoCurrentTerm = errors.New("no current term found")
)

// termProvider is a concrete implementation of TermProvider.
type termProvider struct {
	tracer      trace.Tracer
	mongoClient *mongo.Client
	dbName      string
}

// NewTermProvider is a factory function that instantiates a termProvider.
func NewTermProvider(tracer trace.Tracer, mongoClient *mongo.Client, dbName string) TermProvider {
	return &termProvider{
		tracer:      tracer,
		mongoClient: mongoClient,
		dbName:      dbName,
	}
}

// GetByDateRange implements TermProvider.
func (t *termProvider) IsOverlapDateRange(ctx context.Context, instituteId string, startDate time.Time, endDate time.Time) (bool, error) {
	ctx, span := t.tracer.Start(ctx, "TermProvider : GetByDateRange")
	defer span.End()

	// Build the filter
	filter := bson.M{
		"institute_id": instituteId,
		"$or": []bson.M{
			{"start_date": bson.M{"$lte": startDate}, "end_date": bson.M{"$gte": startDate}},
			{"start_date": bson.M{"$lte": endDate}, "end_date": bson.M{"$gte": endDate}},
			{"start_date": bson.M{"$gte": startDate}, "end_date": bson.M{"$lte": endDate}},
		},
	}

	// Query for the term
	cursor, err := t.mongoClient.Database(t.dbName).Collection(constants.MongoDBCollectionTerms).Find(ctx, filter)
	if err != nil {
		log.Error().Msg(err.Error())
		return false, err
	}
	defer cursor.Close(ctx)
	return cursor.Next(ctx), nil
}

// Add inserts a new Term into the MongoDBCollectionTerms collection.
func (t *termProvider) Add(ctx context.Context, term *entities.Term) (string, error) {
	ctx, span := t.tracer.Start(ctx, "TermProvider : Add")
	defer span.End()

	// Use the ID from the term entity
	if term.ID == nil {
		return constants.EmptyString, errors.New("term ID is required")
	}

	// Insert the document
	_, err := t.mongoClient.Database(t.dbName).
		Collection(constants.MongoDBCollectionTerms).
		InsertOne(ctx, term)
	if err != nil {
		log.Error().Msg(err.Error())
		return constants.EmptyString, err
	}

	return *term.ID, nil
}

// Get retrieves a single Term by termId and instituteId.
func (t *termProvider) Get(ctx context.Context, termId string, instituteId string) (*entities.Term, error) {
	ctx, span := t.tracer.Start(ctx, "TermProvider : Get")
	defer span.End()

	// Build the filter
	filter := bson.M{
		"_id":          termId,
		"institute_id": instituteId,
	}

	// Query for the term
	var term entities.Term
	err := t.mongoClient.Database(t.dbName).
		Collection(constants.MongoDBCollectionTerms).
		FindOne(ctx, filter).
		Decode(&term)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil // or return nil, errors.New("term not found")
		}
		log.Error().Msg(err.Error())
		return nil, err
	}

	return &term, nil
}

// GetAll retrieves all Terms for a given instituteId.
func (t *termProvider) GetAll(ctx context.Context, instituteId string) (*[]entities.Term, error) {
	ctx, span := t.tracer.Start(ctx, "TermProvider : GetAll")
	defer span.End()

	filter := bson.M{
		"institute_id": instituteId,
	}

	cursor, err := t.mongoClient.Database(t.dbName).
		Collection(constants.MongoDBCollectionTerms).
		Find(ctx, filter)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	defer cursor.Close(ctx)

	var terms []entities.Term
	if err := cursor.All(ctx, &terms); err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	return &terms, nil
}

// Edit updates the fields of an existing Term document.
func (t *termProvider) Edit(ctx context.Context, termId string, instituteId string, term *entities.Term) error {
	ctx, span := t.tracer.Start(ctx, "TermProvider : Edit")
	defer span.End()

	// Build the filter to locate the correct term document
	filter := bson.M{
		"_id":          termId,
		"institute_id": instituteId,
	}

	// We can either:
	// 1. Use $set with a map of updated fields.
	// 2. Replace the entire document except for the _id & institute_id.
	// Below, we do a simple replacement approach. If you need partial updates, adjust accordingly.

	// Make sure we preserve _id and institute_id in the replacement doc
	replacement := bson.M{
		"_id":          termId,
		"institute_id": instituteId,
		"name":         term.Name,
		"start_date":   term.StartDate,
		"end_date":     term.EndDate,
	}

	// ReplaceOne overwrites the existing document except the _id.
	_, err := t.mongoClient.Database(t.dbName).
		Collection(constants.MongoDBCollectionTerms).
		ReplaceOne(ctx, filter, replacement)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	return nil
}

// GetCurrent retrieves the current Term for a given instituteId.
func (t *termProvider) GetCurrent(ctx context.Context, instituteId string) (*entities.Term, error) {
	ctx, span := t.tracer.Start(ctx, "TermProvider : GetCurrent")
	defer span.End()

	// Build the filter to find the current term
	filter := bson.M{
		"institute_id": instituteId,
		"start_date":   bson.M{"$lte": time.Now()},
		"end_date":     bson.M{"$gte": time.Now()},
	}

	var terms []entities.Term
	cursor, err := t.mongoClient.Database(t.dbName).Collection(constants.MongoDBCollectionTerms).Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	if err := cursor.All(ctx, &terms); err != nil {
		return nil, err
	}
	if len(terms) == 0 {
		return nil, ErrNoCurrentTerm
	}
	return &terms[len(terms)-1], nil
}

// Delete deletes a Term by termId and instituteId.
func (t *termProvider) Delete(ctx context.Context, termId string, instituteId string) error {
	ctx, span := t.tracer.Start(ctx, "TermProvider : Delete")
	defer span.End()

	// Build the filter
	findFilter := bson.M{
		"_id":          termId,
		"institute_id": instituteId,
	}

	var term entities.Term
	err := t.mongoClient.Database(t.dbName).Collection(constants.MongoDBCollectionTerms).FindOne(ctx, findFilter).Decode(&term)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			log.Error().Msg("No document found to delete")
			return errors.New("no document found to delete")
		}
		log.Error().Msg(err.Error())
		return err
	}

	// Insert the document into the archive collection
	_, err = t.mongoClient.Database(t.dbName).Collection(constants.MongoDBCollectionTermsArchive).InsertOne(ctx, term)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	// Delete the document from the main collection
	deleteResult, err := t.mongoClient.Database(t.dbName).Collection(constants.MongoDBCollectionTerms).DeleteOne(ctx, findFilter)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	if deleteResult.DeletedCount == 0 {
		log.Error().Msg("No document found to delete from main collection")
		return errors.New("no document found to delete from main collection")
	}

	return err
}
