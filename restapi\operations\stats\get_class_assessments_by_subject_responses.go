// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassAssessmentsBySubjectOKCode is the HTTP code returned for type GetClassAssessmentsBySubjectOK
const GetClassAssessmentsBySubjectOKCode int = 200

/*
GetClassAssessmentsBySubjectOK Successful operation

swagger:response getClassAssessmentsBySubjectOK
*/
type GetClassAssessmentsBySubjectOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassAssessmentsBySubject `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectOK creates GetClassAssessmentsBySubjectOK with default headers values
func NewGetClassAssessmentsBySubjectOK() *GetClassAssessmentsBySubjectOK {

	return &GetClassAssessmentsBySubjectOK{}
}

// WithPayload adds the payload to the get class assessments by subject o k response
func (o *GetClassAssessmentsBySubjectOK) WithPayload(payload *models.ClassAssessmentsBySubject) *GetClassAssessmentsBySubjectOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject o k response
func (o *GetClassAssessmentsBySubjectOK) SetPayload(payload *models.ClassAssessmentsBySubject) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassAssessmentsBySubjectBadRequestCode is the HTTP code returned for type GetClassAssessmentsBySubjectBadRequest
const GetClassAssessmentsBySubjectBadRequestCode int = 400

/*
GetClassAssessmentsBySubjectBadRequest Bad Request

swagger:response getClassAssessmentsBySubjectBadRequest
*/
type GetClassAssessmentsBySubjectBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectBadRequest creates GetClassAssessmentsBySubjectBadRequest with default headers values
func NewGetClassAssessmentsBySubjectBadRequest() *GetClassAssessmentsBySubjectBadRequest {

	return &GetClassAssessmentsBySubjectBadRequest{}
}

// WithPayload adds the payload to the get class assessments by subject bad request response
func (o *GetClassAssessmentsBySubjectBadRequest) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject bad request response
func (o *GetClassAssessmentsBySubjectBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectForbiddenCode is the HTTP code returned for type GetClassAssessmentsBySubjectForbidden
const GetClassAssessmentsBySubjectForbiddenCode int = 403

/*
GetClassAssessmentsBySubjectForbidden Forbidden

swagger:response getClassAssessmentsBySubjectForbidden
*/
type GetClassAssessmentsBySubjectForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectForbidden creates GetClassAssessmentsBySubjectForbidden with default headers values
func NewGetClassAssessmentsBySubjectForbidden() *GetClassAssessmentsBySubjectForbidden {

	return &GetClassAssessmentsBySubjectForbidden{}
}

// WithPayload adds the payload to the get class assessments by subject forbidden response
func (o *GetClassAssessmentsBySubjectForbidden) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject forbidden response
func (o *GetClassAssessmentsBySubjectForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectNotFoundCode is the HTTP code returned for type GetClassAssessmentsBySubjectNotFound
const GetClassAssessmentsBySubjectNotFoundCode int = 404

/*
GetClassAssessmentsBySubjectNotFound Not Found

swagger:response getClassAssessmentsBySubjectNotFound
*/
type GetClassAssessmentsBySubjectNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectNotFound creates GetClassAssessmentsBySubjectNotFound with default headers values
func NewGetClassAssessmentsBySubjectNotFound() *GetClassAssessmentsBySubjectNotFound {

	return &GetClassAssessmentsBySubjectNotFound{}
}

// WithPayload adds the payload to the get class assessments by subject not found response
func (o *GetClassAssessmentsBySubjectNotFound) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject not found response
func (o *GetClassAssessmentsBySubjectNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectTooManyRequestsCode is the HTTP code returned for type GetClassAssessmentsBySubjectTooManyRequests
const GetClassAssessmentsBySubjectTooManyRequestsCode int = 429

/*
GetClassAssessmentsBySubjectTooManyRequests Too Many Requests

swagger:response getClassAssessmentsBySubjectTooManyRequests
*/
type GetClassAssessmentsBySubjectTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectTooManyRequests creates GetClassAssessmentsBySubjectTooManyRequests with default headers values
func NewGetClassAssessmentsBySubjectTooManyRequests() *GetClassAssessmentsBySubjectTooManyRequests {

	return &GetClassAssessmentsBySubjectTooManyRequests{}
}

// WithPayload adds the payload to the get class assessments by subject too many requests response
func (o *GetClassAssessmentsBySubjectTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject too many requests response
func (o *GetClassAssessmentsBySubjectTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectInternalServerErrorCode is the HTTP code returned for type GetClassAssessmentsBySubjectInternalServerError
const GetClassAssessmentsBySubjectInternalServerErrorCode int = 500

/*
GetClassAssessmentsBySubjectInternalServerError Internal Server Error

swagger:response getClassAssessmentsBySubjectInternalServerError
*/
type GetClassAssessmentsBySubjectInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectInternalServerError creates GetClassAssessmentsBySubjectInternalServerError with default headers values
func NewGetClassAssessmentsBySubjectInternalServerError() *GetClassAssessmentsBySubjectInternalServerError {

	return &GetClassAssessmentsBySubjectInternalServerError{}
}

// WithPayload adds the payload to the get class assessments by subject internal server error response
func (o *GetClassAssessmentsBySubjectInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject internal server error response
func (o *GetClassAssessmentsBySubjectInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectServiceUnavailableCode is the HTTP code returned for type GetClassAssessmentsBySubjectServiceUnavailable
const GetClassAssessmentsBySubjectServiceUnavailableCode int = 503

/*
GetClassAssessmentsBySubjectServiceUnavailable Service Unvailable

swagger:response getClassAssessmentsBySubjectServiceUnavailable
*/
type GetClassAssessmentsBySubjectServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectServiceUnavailable creates GetClassAssessmentsBySubjectServiceUnavailable with default headers values
func NewGetClassAssessmentsBySubjectServiceUnavailable() *GetClassAssessmentsBySubjectServiceUnavailable {

	return &GetClassAssessmentsBySubjectServiceUnavailable{}
}

// WithPayload adds the payload to the get class assessments by subject service unavailable response
func (o *GetClassAssessmentsBySubjectServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject service unavailable response
func (o *GetClassAssessmentsBySubjectServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
