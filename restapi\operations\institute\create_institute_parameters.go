// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"io"
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/validate"

	"eddyowl-backend/models"
)

// NewCreateInstituteParams creates a new CreateInstituteParams object
//
// There are no default values defined in the spec.
func NewCreateInstituteParams() CreateInstituteParams {

	return CreateInstituteParams{}
}

// CreateInstituteParams contains all the bound params for the create institute operation
// typically these are obtained from a http.Request
//
// swagger:parameters CreateInstitute
type CreateInstituteParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: body
	*/
	NewInstitute *models.NewInstitute
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewCreateInstituteParams() beforehand.
func (o *CreateInstituteParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	if runtime.HasBody(r) {
		defer r.Body.Close()
		var body models.NewInstitute
		if err := route.Consumer.Consume(r.Body, &body); err != nil {
			if err == io.EOF {
				res = append(res, errors.Required("newInstitute", "body", ""))
			} else {
				res = append(res, errors.NewParseError("newInstitute", "body", "", err))
			}
		} else {
			// validate body object
			if err := body.Validate(route.Formats); err != nil {
				res = append(res, err)
			}

			ctx := validate.WithOperationRequest(r.Context())
			if err := body.ContextValidate(ctx, route.Formats); err != nil {
				res = append(res, err)
			}

			if len(res) == 0 {
				o.NewInstitute = &body
			}
		}
	} else {
		res = append(res, errors.Required("newInstitute", "body", ""))
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
