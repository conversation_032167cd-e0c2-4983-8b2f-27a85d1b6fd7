// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteTermByIDHandlerFunc turns a function with the right signature into a delete term by Id handler
type DeleteTermByIDHandlerFunc func(DeleteTermByIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteTermByIDHandlerFunc) Handle(params DeleteTermByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteTermByIDHandler interface for that can handle valid delete term by Id params
type DeleteTermByIDHandler interface {
	Handle(DeleteTermByIDParams, interface{}) middleware.Responder
}

// NewDeleteTermByID creates a new http.Handler for the delete term by Id operation
func NewDeleteTermByID(ctx *middleware.Context, handler Delete<PERSON>er<PERSON><PERSON>y<PERSON>H<PERSON><PERSON>) *DeleteTermByID {
	return &DeleteTermByID{Context: ctx, Handler: handler}
}

/*
	DeleteTermByID swagger:route DELETE /institute/{instituteId}/term/{termId} term deleteTermById

# Delete term

Delete term by School Term Id
*/
type DeleteTermByID struct {
	Context *middleware.Context
	Handler DeleteTermByIDHandler
}

func (o *DeleteTermByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteTermByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
