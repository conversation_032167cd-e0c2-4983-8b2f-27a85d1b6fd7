// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllAssignmentsOKCode is the HTTP code returned for type GetAllAssignmentsOK
const GetAllAssignmentsOKCode int = 200

/*
GetAllAssignmentsOK Successful operation

swagger:response getAllAssignmentsOK
*/
type GetAllAssignmentsOK struct {

	/*
	  In: Body
	*/
	Payload models.AssignmentList `json:"body,omitempty"`
}

// NewGetAllAssignmentsOK creates GetAllAssignmentsOK with default headers values
func NewGetAllAssignmentsOK() *GetAllAssignmentsOK {

	return &GetAllAssignmentsOK{}
}

// WithPayload adds the payload to the get all assignments o k response
func (o *GetAllAssignmentsOK) WithPayload(payload models.AssignmentList) *GetAllAssignmentsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments o k response
func (o *GetAllAssignmentsOK) SetPayload(payload models.AssignmentList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.AssignmentList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsBadRequestCode is the HTTP code returned for type GetAllAssignmentsBadRequest
const GetAllAssignmentsBadRequestCode int = 400

/*
GetAllAssignmentsBadRequest Bad Request

swagger:response getAllAssignmentsBadRequest
*/
type GetAllAssignmentsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsBadRequest creates GetAllAssignmentsBadRequest with default headers values
func NewGetAllAssignmentsBadRequest() *GetAllAssignmentsBadRequest {

	return &GetAllAssignmentsBadRequest{}
}

// WithPayload adds the payload to the get all assignments bad request response
func (o *GetAllAssignmentsBadRequest) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments bad request response
func (o *GetAllAssignmentsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsForbiddenCode is the HTTP code returned for type GetAllAssignmentsForbidden
const GetAllAssignmentsForbiddenCode int = 403

/*
GetAllAssignmentsForbidden Forbidden

swagger:response getAllAssignmentsForbidden
*/
type GetAllAssignmentsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsForbidden creates GetAllAssignmentsForbidden with default headers values
func NewGetAllAssignmentsForbidden() *GetAllAssignmentsForbidden {

	return &GetAllAssignmentsForbidden{}
}

// WithPayload adds the payload to the get all assignments forbidden response
func (o *GetAllAssignmentsForbidden) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments forbidden response
func (o *GetAllAssignmentsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsNotFoundCode is the HTTP code returned for type GetAllAssignmentsNotFound
const GetAllAssignmentsNotFoundCode int = 404

/*
GetAllAssignmentsNotFound Not Found

swagger:response getAllAssignmentsNotFound
*/
type GetAllAssignmentsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsNotFound creates GetAllAssignmentsNotFound with default headers values
func NewGetAllAssignmentsNotFound() *GetAllAssignmentsNotFound {

	return &GetAllAssignmentsNotFound{}
}

// WithPayload adds the payload to the get all assignments not found response
func (o *GetAllAssignmentsNotFound) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments not found response
func (o *GetAllAssignmentsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsTooManyRequestsCode is the HTTP code returned for type GetAllAssignmentsTooManyRequests
const GetAllAssignmentsTooManyRequestsCode int = 429

/*
GetAllAssignmentsTooManyRequests Too Many Requests

swagger:response getAllAssignmentsTooManyRequests
*/
type GetAllAssignmentsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsTooManyRequests creates GetAllAssignmentsTooManyRequests with default headers values
func NewGetAllAssignmentsTooManyRequests() *GetAllAssignmentsTooManyRequests {

	return &GetAllAssignmentsTooManyRequests{}
}

// WithPayload adds the payload to the get all assignments too many requests response
func (o *GetAllAssignmentsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments too many requests response
func (o *GetAllAssignmentsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsInternalServerErrorCode is the HTTP code returned for type GetAllAssignmentsInternalServerError
const GetAllAssignmentsInternalServerErrorCode int = 500

/*
GetAllAssignmentsInternalServerError Internal Server Error

swagger:response getAllAssignmentsInternalServerError
*/
type GetAllAssignmentsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsInternalServerError creates GetAllAssignmentsInternalServerError with default headers values
func NewGetAllAssignmentsInternalServerError() *GetAllAssignmentsInternalServerError {

	return &GetAllAssignmentsInternalServerError{}
}

// WithPayload adds the payload to the get all assignments internal server error response
func (o *GetAllAssignmentsInternalServerError) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments internal server error response
func (o *GetAllAssignmentsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllAssignmentsServiceUnavailableCode is the HTTP code returned for type GetAllAssignmentsServiceUnavailable
const GetAllAssignmentsServiceUnavailableCode int = 503

/*
GetAllAssignmentsServiceUnavailable Service Unvailable

swagger:response getAllAssignmentsServiceUnavailable
*/
type GetAllAssignmentsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllAssignmentsServiceUnavailable creates GetAllAssignmentsServiceUnavailable with default headers values
func NewGetAllAssignmentsServiceUnavailable() *GetAllAssignmentsServiceUnavailable {

	return &GetAllAssignmentsServiceUnavailable{}
}

// WithPayload adds the payload to the get all assignments service unavailable response
func (o *GetAllAssignmentsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAllAssignmentsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all assignments service unavailable response
func (o *GetAllAssignmentsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllAssignmentsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
