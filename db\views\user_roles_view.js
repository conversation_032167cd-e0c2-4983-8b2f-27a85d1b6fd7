db.createView("user_roles_view", "users", [
  {
    $lookup: {
      from: "students",
      localField: "email",
      foreignField: "email",
      as: "student_roles",
    },
  },
  {
    $lookup: {
      from: "instructors",
      localField: "email",
      foreignField: "email",
      as: "instructor_roles",
    },
  },
  {
    $project: {
      _id: 0,
      email: 1,
      roles: {
        $concatArrays: [
          {
            $map: {
              input: "$student_roles",
              as: "s",
              in: {
                institute_id: "$$s.institute_id",
                role: 3,
              },
            },
          },
          {
            $map: {
              input: "$instructor_roles",
              as: "i",
              in: {
                institute_id: "$$i.institute_id",
                role: "$$i.role",
              },
            },
          },
        ],
      },
    },
  },
  {
    $match: {
      roles: { $ne: [] },
    },
  },
]);
