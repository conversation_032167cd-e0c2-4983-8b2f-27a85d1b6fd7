// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetInstituteByIDHandlerFunc turns a function with the right signature into a get institute by Id handler
type GetInstituteByIDHandlerFunc func(GetInstituteByIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetInstituteByIDHandlerFunc) Handle(params GetInstituteByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetInstituteByIDHandler interface for that can handle valid get institute by Id params
type GetInstituteByIDHandler interface {
	Handle(GetInstituteByIDParams, interface{}) middleware.Responder
}

// NewGetInstituteByID creates a new http.Handler for the get institute by Id operation
func NewGetInstituteByID(ctx *middleware.Context, handler GetInstit<PERSON><PERSON>y<PERSON>H<PERSON><PERSON>) *GetInstituteByID {
	return &GetInstituteByID{Context: ctx, Handler: handler}
}

/*
	GetInstituteByID swagger:route GET /institute/{instituteId} institute getInstituteById

# Get institute

Get institute by id
*/
type GetInstituteByID struct {
	Context *middleware.Context
	Handler GetInstituteByIDHandler
}

func (o *GetInstituteByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetInstituteByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
