db.createView("section_submission_count_view", "submissions_view", [
  {
    $match: {
      created_at: {
        $ne: null,
      },
    },
  },
  {
    $group: {
      _id: {
        institute_id: "$institute_id",
        term_id: "$term_id",
        grade: "$grade",
        section: "$student_section", // Group by section
      },

      submission_count: {
        $sum: 1,
      },
    },
  },
  // Lookup assignments matching the same institute, term, grade, and section
  {
    $lookup: {
      from: "assignments",
      let: {
        inst_id: "$_id.institute_id",
        term_id: "$_id.term_id",
        grade: "$_id.grade",
        section: "$_id.section",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$institute_id", "$$inst_id"],
                },
                {
                  $eq: ["$term_id", "$$term_id"],
                },
                {
                  $eq: ["$grade", "$$grade"],
                },
                {
                  $in: ["$$section", "$sections"],
                }, // Check if section is in assignment.sections
              ],
            },
          },
        },
      ],

      as: "matched_assignments",
    },
  },
  {
    $addFields: {
      assignment_count: {
        $size: "$matched_assignments",
      },
    },
  },
  {
    $sort: {
      "_id.institute_id": 1,
      "_id.term_id": 1,
      "_id.grade": 1,
      "_id.section": 1,
    },
  },
  {
    $project:
      /**
       * specifications: The fields to
       *   include or exclude.
       */
      {
        _id: 1,
        submission_count: 1,
        assignment_count: 1,
      },
  },
]);
