// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// SubmissionData submission data
//
// swagger:model SubmissionData
type SubmissionData struct {

	// image urls
	// Required: true
	ImageUrls []string `json:"imageUrls"`

	// student Id
	// Required: true
	StudentID *string `json:"studentId"`
}

// Validate validates this submission data
func (m *SubmissionData) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateImageUrls(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateStudentID(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *SubmissionData) validateImageUrls(formats strfmt.Registry) error {

	if err := validate.Required("imageUrls", "body", m.ImageUrls); err != nil {
		return err
	}

	return nil
}

func (m *SubmissionData) validateStudentID(formats strfmt.Registry) error {

	if err := validate.Required("studentId", "body", m.StudentID); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this submission data based on context it is used
func (m *SubmissionData) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *SubmissionData) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *SubmissionData) UnmarshalBinary(b []byte) error {
	var res SubmissionData
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
