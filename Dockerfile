FROM golang:1.22-alpine3.19 AS build

# Define current working directory
WORKDIR /backend

# Download modules to local cache
COPY go.mod .
COPY go.sum .
RUN go mod download

# Add sources
COPY . .

RUN go build -o ./cmd/eddy-owl-core-api-server/eddy-owl-backend ./cmd/eddy-owl-core-api-server

FROM alpine:latest

WORKDIR /backend

COPY --from=build /backend/cmd/eddy-owl-core-api-server/eddy-owl-backend ./cmd/eddy-owl-core-api-server/eddy-owl-backend

# Copy only the base config and secrets files
COPY --from=build /backend/config/config.json ./config/config.json
COPY --from=build /backend/secrets/secrets.json ./secrets/secrets.json

# Create directories for override files that will be mounted
RUN mkdir -p /backend/config && mkdir -p /backend/secrets

EXPOSE 8080
ENTRYPOINT ["./cmd/eddy-owl-core-api-server/eddy-owl-backend", "--scheme=http","--port=8080", "--host=0.0.0.0"]
