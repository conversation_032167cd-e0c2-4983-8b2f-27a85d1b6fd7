// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetInstructorByInstituteIDOKCode is the HTTP code returned for type GetInstructorByInstituteIDOK
const GetInstructorByInstituteIDOKCode int = 200

/*
GetInstructorByInstituteIDOK Successful operation

swagger:response getInstructorByInstituteIdOK
*/
type GetInstructorByInstituteIDOK struct {

	/*
	  In: Body
	*/
	Payload models.InstructorList `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDOK creates GetInstructorByInstituteIDOK with default headers values
func NewGetInstructorByInstituteIDOK() *GetInstructorByInstituteIDOK {

	return &GetInstructorByInstituteIDOK{}
}

// WithPayload adds the payload to the get instructor by institute Id o k response
func (o *GetInstructorByInstituteIDOK) WithPayload(payload models.InstructorList) *GetInstructorByInstituteIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id o k response
func (o *GetInstructorByInstituteIDOK) SetPayload(payload models.InstructorList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.InstructorList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDBadRequestCode is the HTTP code returned for type GetInstructorByInstituteIDBadRequest
const GetInstructorByInstituteIDBadRequestCode int = 400

/*
GetInstructorByInstituteIDBadRequest Bad Request

swagger:response getInstructorByInstituteIdBadRequest
*/
type GetInstructorByInstituteIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDBadRequest creates GetInstructorByInstituteIDBadRequest with default headers values
func NewGetInstructorByInstituteIDBadRequest() *GetInstructorByInstituteIDBadRequest {

	return &GetInstructorByInstituteIDBadRequest{}
}

// WithPayload adds the payload to the get instructor by institute Id bad request response
func (o *GetInstructorByInstituteIDBadRequest) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id bad request response
func (o *GetInstructorByInstituteIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDForbiddenCode is the HTTP code returned for type GetInstructorByInstituteIDForbidden
const GetInstructorByInstituteIDForbiddenCode int = 403

/*
GetInstructorByInstituteIDForbidden Forbidden

swagger:response getInstructorByInstituteIdForbidden
*/
type GetInstructorByInstituteIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDForbidden creates GetInstructorByInstituteIDForbidden with default headers values
func NewGetInstructorByInstituteIDForbidden() *GetInstructorByInstituteIDForbidden {

	return &GetInstructorByInstituteIDForbidden{}
}

// WithPayload adds the payload to the get instructor by institute Id forbidden response
func (o *GetInstructorByInstituteIDForbidden) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id forbidden response
func (o *GetInstructorByInstituteIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDNotFoundCode is the HTTP code returned for type GetInstructorByInstituteIDNotFound
const GetInstructorByInstituteIDNotFoundCode int = 404

/*
GetInstructorByInstituteIDNotFound Not Found

swagger:response getInstructorByInstituteIdNotFound
*/
type GetInstructorByInstituteIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDNotFound creates GetInstructorByInstituteIDNotFound with default headers values
func NewGetInstructorByInstituteIDNotFound() *GetInstructorByInstituteIDNotFound {

	return &GetInstructorByInstituteIDNotFound{}
}

// WithPayload adds the payload to the get instructor by institute Id not found response
func (o *GetInstructorByInstituteIDNotFound) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id not found response
func (o *GetInstructorByInstituteIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDTooManyRequestsCode is the HTTP code returned for type GetInstructorByInstituteIDTooManyRequests
const GetInstructorByInstituteIDTooManyRequestsCode int = 429

/*
GetInstructorByInstituteIDTooManyRequests Too Many Requests

swagger:response getInstructorByInstituteIdTooManyRequests
*/
type GetInstructorByInstituteIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDTooManyRequests creates GetInstructorByInstituteIDTooManyRequests with default headers values
func NewGetInstructorByInstituteIDTooManyRequests() *GetInstructorByInstituteIDTooManyRequests {

	return &GetInstructorByInstituteIDTooManyRequests{}
}

// WithPayload adds the payload to the get instructor by institute Id too many requests response
func (o *GetInstructorByInstituteIDTooManyRequests) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id too many requests response
func (o *GetInstructorByInstituteIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDInternalServerErrorCode is the HTTP code returned for type GetInstructorByInstituteIDInternalServerError
const GetInstructorByInstituteIDInternalServerErrorCode int = 500

/*
GetInstructorByInstituteIDInternalServerError Internal Server Error

swagger:response getInstructorByInstituteIdInternalServerError
*/
type GetInstructorByInstituteIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDInternalServerError creates GetInstructorByInstituteIDInternalServerError with default headers values
func NewGetInstructorByInstituteIDInternalServerError() *GetInstructorByInstituteIDInternalServerError {

	return &GetInstructorByInstituteIDInternalServerError{}
}

// WithPayload adds the payload to the get instructor by institute Id internal server error response
func (o *GetInstructorByInstituteIDInternalServerError) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id internal server error response
func (o *GetInstructorByInstituteIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstructorByInstituteIDServiceUnavailableCode is the HTTP code returned for type GetInstructorByInstituteIDServiceUnavailable
const GetInstructorByInstituteIDServiceUnavailableCode int = 503

/*
GetInstructorByInstituteIDServiceUnavailable Service Unvailable

swagger:response getInstructorByInstituteIdServiceUnavailable
*/
type GetInstructorByInstituteIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstructorByInstituteIDServiceUnavailable creates GetInstructorByInstituteIDServiceUnavailable with default headers values
func NewGetInstructorByInstituteIDServiceUnavailable() *GetInstructorByInstituteIDServiceUnavailable {

	return &GetInstructorByInstituteIDServiceUnavailable{}
}

// WithPayload adds the payload to the get instructor by institute Id service unavailable response
func (o *GetInstructorByInstituteIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetInstructorByInstituteIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get instructor by institute Id service unavailable response
func (o *GetInstructorByInstituteIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstructorByInstituteIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
