// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassAssessmentsBySectionsHandlerFunc turns a function with the right signature into a get class assessments by sections handler
type GetClassAssessmentsBySectionsHandlerFunc func(GetClassAssessmentsBySectionsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassAssessmentsBySectionsHandlerFunc) Handle(params GetClassAssessmentsBySectionsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassAssessmentsBySectionsHandler interface for that can handle valid get class assessments by sections params
type GetClassAssessmentsBySectionsHandler interface {
	Handle(GetClassAssessmentsBySectionsParams, interface{}) middleware.Responder
}

// NewGetClassAssessmentsBySections creates a new http.Handler for the get class assessments by sections operation
func NewGetClassAssessmentsBySections(ctx *middleware.Context, handler GetClassAssessmentsBySectionsHandler) *GetClassAssessmentsBySections {
	return &GetClassAssessmentsBySections{Context: ctx, Handler: handler}
}

/*
	GetClassAssessmentsBySections swagger:route GET /institute/{instituteId}/class/{class}/assessmentsbysections stats getClassAssessmentsBySections

# Get Class Assessments By Sections

Get Class Assessments By Sections.
*/
type GetClassAssessmentsBySections struct {
	Context *middleware.Context
	Handler GetClassAssessmentsBySectionsHandler
}

func (o *GetClassAssessmentsBySections) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassAssessmentsBySectionsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
