package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/term"
	"eddyowl-backend/utils"
	"time"

	"github.com/go-openapi/strfmt"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type createTermImpl struct {
	provider          data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

// NewCreateNewTermHandler constructs a new handler for adding a Term.
func NewCreateNewTermHandler(
	provider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) term.CreateNewTermHandler {
	return &createTermImpl{
		provider:          provider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

// <PERSON><PERSON> processes the "create term" request.
func (impl *createTermImpl) Handle(params term.CreateNewTermParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateNewTermHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return term.NewCreateNewTermForbidden().WithPayload("Unauthorized")
	}

	valid := CreateNewTermValidator(params)
	if !valid {
		return term.NewCreateNewTermBadRequest().WithPayload("Invalid Term Parameters")
	}

	instituteID := params.InstituteID // e.g., "xyz123"
	name := params.Term.Name          // e.g., "Term 1"

	startDate := time.Time(params.Term.StartDate)
	endDate := time.Time(params.Term.EndDate)

	if termExists, err := impl.provider.IsOverlapDateRange(ctx, instituteID, startDate, endDate); err != nil {
		log.Error().Msg(err.Error())
		return term.NewCreateNewTermInternalServerError().WithPayload("Unable to create Term")
	} else if termExists {
		return term.NewCreateNewTermBadRequest().WithPayload("Term already exists for the given date range")
	}

	// Construct the Term entity.
	// You might have a dedicated constructor like entities.NewTerm(...),
	// or you can create the struct directly.
	termEntity := entities.NewTerm(
		instituteID,
		name,       // name
		&startDate, // startDate
		&endDate,   // endDate
	)
	// The term's InstituteID is often needed; add it if your entity supports it:
	// termEntity.InstituteID = instituteID

	// Optionally, if you store userID as CreatedBy or something similar:
	// termEntity.CreatedBy = &userID

	// Call your data provider to add the new term.
	termID, err := impl.provider.Add(ctx, termEntity)
	if err != nil {
		log.Error().Msg(err.Error())
		return term.NewCreateNewTermInternalServerError().WithPayload("Unable to create Term")
	}

	// Return success with the newly created term ID.
	return term.NewCreateNewTermOK().WithPayload(
		&models.SuccessResponse{
			ID:      termID,
			Message: "Successfully created Term",
		},
	)
}

func CreateNewTermValidator(params term.CreateNewTermParams) bool {
	if params.InstituteID == constants.EmptyString || params.Term.EndDate == strfmt.DateTime(strfmt.UnixZero) || params.Term.StartDate == strfmt.DateTime(strfmt.UnixZero) || params.Term.Name == constants.EmptyString {
		return false
	}
	return true
}
