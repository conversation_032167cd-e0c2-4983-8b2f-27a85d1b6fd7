// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ChapterStats chapter stats
//
// swagger:model ChapterStats
type ChapterStats struct {

	// chapter
	Chapter string `json:"chapter,omitempty"`

	// topics
	Topics []*TopicStats `json:"topics"`
}

// Validate validates this chapter stats
func (m *ChapterStats) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateTopics(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChapterStats) validateTopics(formats strfmt.Registry) error {
	if swag.<PERSON>Zero(m.Topics) { // not required
		return nil
	}

	for i := 0; i < len(m.Topics); i++ {
		if swag.IsZero(m.Topics[i]) { // not required
			continue
		}

		if m.Topics[i] != nil {
			if err := m.Topics[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this chapter stats based on the context it is used
func (m *ChapterStats) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateTopics(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *ChapterStats) contextValidateTopics(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Topics); i++ {

		if m.Topics[i] != nil {

			if swag.IsZero(m.Topics[i]) { // not required
				return nil
			}

			if err := m.Topics[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *ChapterStats) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ChapterStats) UnmarshalBinary(b []byte) error {
	var res ChapterStats
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
