// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// RubricInput rubric input
//
// swagger:model RubricInput
type RubricInput struct {

	// class
	Class int32 `json:"class,omitempty"`

	// questions
	Questions *QuestionList `json:"questions,omitempty"`

	// subject
	Subject string `json:"subject,omitempty"`
}

// Validate validates this rubric input
func (m *RubricInput) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateQuestions(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *RubricInput) validateQuestions(formats strfmt.Registry) error {
	if swag.IsZero(m.Questions) { // not required
		return nil
	}

	if m.Questions != nil {
		if err := m.Questions.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("questions")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("questions")
			}
			return err
		}
	}

	return nil
}

// ContextValidate validate this rubric input based on the context it is used
func (m *RubricInput) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateQuestions(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *RubricInput) contextValidateQuestions(ctx context.Context, formats strfmt.Registry) error {

	if m.Questions != nil {

		if swag.IsZero(m.Questions) { // not required
			return nil
		}

		if err := m.Questions.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("questions")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("questions")
			}
			return err
		}
	}

	return nil
}

// MarshalBinary interface implementation
func (m *RubricInput) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *RubricInput) UnmarshalBinary(b []byte) error {
	var res RubricInput
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
