// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// NewGetAllSubmissionsParams creates a new GetAllSubmissionsParams object
//
// There are no default values defined in the spec.
func NewGetAllSubmissionsParams() GetAllSubmissionsParams {

	return GetAllSubmissionsParams{}
}

// GetAllSubmissionsParams contains all the bound params for the get all submissions operation
// typically these are obtained from a http.Request
//
// swagger:parameters GetAllSubmissions
type GetAllSubmissionsParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  In: query
	*/
	AssignmentID *string
	/*
	  In: query
	*/
	Grade *int32
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  In: query
	*/
	Section []string
	/*
	  In: query
	*/
	StudentID *string
	/*
	  In: query
	*/
	TermID *string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewGetAllSubmissionsParams() beforehand.
func (o *GetAllSubmissionsParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	qs := runtime.Values(r.URL.Query())

	qAssignmentID, qhkAssignmentID, _ := qs.GetOK("assignmentId")
	if err := o.bindAssignmentID(qAssignmentID, qhkAssignmentID, route.Formats); err != nil {
		res = append(res, err)
	}

	qGrade, qhkGrade, _ := qs.GetOK("grade")
	if err := o.bindGrade(qGrade, qhkGrade, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	qSection, qhkSection, _ := qs.GetOK("section")
	if err := o.bindSection(qSection, qhkSection, route.Formats); err != nil {
		res = append(res, err)
	}

	qStudentID, qhkStudentID, _ := qs.GetOK("studentId")
	if err := o.bindStudentID(qStudentID, qhkStudentID, route.Formats); err != nil {
		res = append(res, err)
	}

	qTermID, qhkTermID, _ := qs.GetOK("termId")
	if err := o.bindTermID(qTermID, qhkTermID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindAssignmentID binds and validates parameter AssignmentID from query.
func (o *GetAllSubmissionsParams) bindAssignmentID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.AssignmentID = &raw

	return nil
}

// bindGrade binds and validates parameter Grade from query.
func (o *GetAllSubmissionsParams) bindGrade(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}

	value, err := swag.ConvertInt32(raw)
	if err != nil {
		return errors.InvalidType("grade", "query", "int32", raw)
	}
	o.Grade = &value

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *GetAllSubmissionsParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}

// bindSection binds and validates array parameter Section from query.
//
// Arrays are parsed according to CollectionFormat: "" (defaults to "csv" when empty).
func (o *GetAllSubmissionsParams) bindSection(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var qvSection string
	if len(rawData) > 0 {
		qvSection = rawData[len(rawData)-1]
	}

	// CollectionFormat:
	sectionIC := swag.SplitByFormat(qvSection, "")
	if len(sectionIC) == 0 {
		return nil
	}

	var sectionIR []string
	for _, sectionIV := range sectionIC {
		sectionI := sectionIV

		sectionIR = append(sectionIR, sectionI)
	}

	o.Section = sectionIR

	return nil
}

// bindStudentID binds and validates parameter StudentID from query.
func (o *GetAllSubmissionsParams) bindStudentID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.StudentID = &raw

	return nil
}

// bindTermID binds and validates parameter TermID from query.
func (o *GetAllSubmissionsParams) bindTermID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.TermID = &raw

	return nil
}
