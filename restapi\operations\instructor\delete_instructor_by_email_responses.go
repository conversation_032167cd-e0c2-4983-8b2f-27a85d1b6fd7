// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteInstructorByEmailOKCode is the HTTP code returned for type DeleteInstructorByEmailOK
const DeleteInstructorByEmailOKCode int = 200

/*
DeleteInstructorByEmailOK Successful operation

swagger:response deleteInstructorByEmailOK
*/
type DeleteInstructorByEmailOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailOK creates DeleteInstructorByEmailOK with default headers values
func NewDeleteInstructorByEmailOK() *DeleteInstructorByEmailOK {

	return &DeleteInstructorByEmailOK{}
}

// WithPayload adds the payload to the delete instructor by email o k response
func (o *DeleteInstructorByEmailOK) WithPayload(payload *models.SuccessResponse) *DeleteInstructorByEmailOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email o k response
func (o *DeleteInstructorByEmailOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteInstructorByEmailBadRequestCode is the HTTP code returned for type DeleteInstructorByEmailBadRequest
const DeleteInstructorByEmailBadRequestCode int = 400

/*
DeleteInstructorByEmailBadRequest Bad Request

swagger:response deleteInstructorByEmailBadRequest
*/
type DeleteInstructorByEmailBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailBadRequest creates DeleteInstructorByEmailBadRequest with default headers values
func NewDeleteInstructorByEmailBadRequest() *DeleteInstructorByEmailBadRequest {

	return &DeleteInstructorByEmailBadRequest{}
}

// WithPayload adds the payload to the delete instructor by email bad request response
func (o *DeleteInstructorByEmailBadRequest) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email bad request response
func (o *DeleteInstructorByEmailBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstructorByEmailForbiddenCode is the HTTP code returned for type DeleteInstructorByEmailForbidden
const DeleteInstructorByEmailForbiddenCode int = 403

/*
DeleteInstructorByEmailForbidden Forbidden

swagger:response deleteInstructorByEmailForbidden
*/
type DeleteInstructorByEmailForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailForbidden creates DeleteInstructorByEmailForbidden with default headers values
func NewDeleteInstructorByEmailForbidden() *DeleteInstructorByEmailForbidden {

	return &DeleteInstructorByEmailForbidden{}
}

// WithPayload adds the payload to the delete instructor by email forbidden response
func (o *DeleteInstructorByEmailForbidden) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email forbidden response
func (o *DeleteInstructorByEmailForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstructorByEmailNotFoundCode is the HTTP code returned for type DeleteInstructorByEmailNotFound
const DeleteInstructorByEmailNotFoundCode int = 404

/*
DeleteInstructorByEmailNotFound Not Found

swagger:response deleteInstructorByEmailNotFound
*/
type DeleteInstructorByEmailNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailNotFound creates DeleteInstructorByEmailNotFound with default headers values
func NewDeleteInstructorByEmailNotFound() *DeleteInstructorByEmailNotFound {

	return &DeleteInstructorByEmailNotFound{}
}

// WithPayload adds the payload to the delete instructor by email not found response
func (o *DeleteInstructorByEmailNotFound) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email not found response
func (o *DeleteInstructorByEmailNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstructorByEmailTooManyRequestsCode is the HTTP code returned for type DeleteInstructorByEmailTooManyRequests
const DeleteInstructorByEmailTooManyRequestsCode int = 429

/*
DeleteInstructorByEmailTooManyRequests Too Many Requests

swagger:response deleteInstructorByEmailTooManyRequests
*/
type DeleteInstructorByEmailTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailTooManyRequests creates DeleteInstructorByEmailTooManyRequests with default headers values
func NewDeleteInstructorByEmailTooManyRequests() *DeleteInstructorByEmailTooManyRequests {

	return &DeleteInstructorByEmailTooManyRequests{}
}

// WithPayload adds the payload to the delete instructor by email too many requests response
func (o *DeleteInstructorByEmailTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email too many requests response
func (o *DeleteInstructorByEmailTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstructorByEmailInternalServerErrorCode is the HTTP code returned for type DeleteInstructorByEmailInternalServerError
const DeleteInstructorByEmailInternalServerErrorCode int = 500

/*
DeleteInstructorByEmailInternalServerError Internal Server Error

swagger:response deleteInstructorByEmailInternalServerError
*/
type DeleteInstructorByEmailInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailInternalServerError creates DeleteInstructorByEmailInternalServerError with default headers values
func NewDeleteInstructorByEmailInternalServerError() *DeleteInstructorByEmailInternalServerError {

	return &DeleteInstructorByEmailInternalServerError{}
}

// WithPayload adds the payload to the delete instructor by email internal server error response
func (o *DeleteInstructorByEmailInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email internal server error response
func (o *DeleteInstructorByEmailInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstructorByEmailServiceUnavailableCode is the HTTP code returned for type DeleteInstructorByEmailServiceUnavailable
const DeleteInstructorByEmailServiceUnavailableCode int = 503

/*
DeleteInstructorByEmailServiceUnavailable Service Unvailable

swagger:response deleteInstructorByEmailServiceUnavailable
*/
type DeleteInstructorByEmailServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstructorByEmailServiceUnavailable creates DeleteInstructorByEmailServiceUnavailable with default headers values
func NewDeleteInstructorByEmailServiceUnavailable() *DeleteInstructorByEmailServiceUnavailable {

	return &DeleteInstructorByEmailServiceUnavailable{}
}

// WithPayload adds the payload to the delete instructor by email service unavailable response
func (o *DeleteInstructorByEmailServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteInstructorByEmailServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete instructor by email service unavailable response
func (o *DeleteInstructorByEmailServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstructorByEmailServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
