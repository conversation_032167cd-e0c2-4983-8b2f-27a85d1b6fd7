package components

import "context"

type AIComponent interface {
	CreateAssignment(ctx context.Context, imageURLs []string) (*[]QuestionSchema, error)
	GradeAssignment(ctx context.Context, instituteID, testID, studentID string) error
	CreateAutoRubric(ctx context.Context, grade int, subject string, questions []QuestionSchema) (*[]QuestionWithRubricSchema, error)
	CreateFileRubric(ctx context.Context, grade int, subject string, questions []QuestionSchema, imageURLs []string) (*[]QuestionWithRubricSchema, error)
	AssignTopics(ctx context.Context, testID, instituteID string) error
}

// QuestionSchema represents a single question, its content, and its score.
type QuestionSchema struct {
	// QuestionNumber is the sequential question number.
	QuestionNumber int `json:"questionNumber"`

	// Question is the complete content of the question, including subparts,
	// instructions, and optional components.
	Question string `json:"question"`

	// QuestionScore is the score/marks for the question.
	QuestionScore float64 `json:"questionScore"`
}

// GradedQuestionSchema contains a question along with its grading details.
type GradedQuestionSchema struct {
	// QuestionNumber is the question number from the input JSON.
	QuestionNumber int `json:"questionNumber"`

	// Question is the content of the question from the input JSON.
	Question string `json:"question"`

	// QuestionScore is the score for the question from the input JSON.
	QuestionScore int `json:"questionScore"`

	// QuestionRubric is the rubric of the question from the input JSON.
	QuestionRubric string `json:"questionRubric"`

	// StudentResponse is the student's response as derived from the images or input.
	StudentResponse string `json:"studentResponse"`

	// Score is the graded student response score calculated by the system.
	Score float64 `json:"score"`

	// Feedback describes why the particular score was assigned.
	Feedback string `json:"feedback"`
}

// TopicsSchema holds chapter and associated topics.
type TopicsSchema struct {
	// Chapter is the name of the chapter.
	Chapter string `json:"chapter"`

	// Topics is a list of topics associated with the question.
	Topics []string `json:"topics"`
}

// QuestionTopicsSchema maps a question to one or more chapter/topic pairs.
type QuestionTopicsSchema struct {
	// QuestionNumber is the question number from the question list.
	QuestionNumber int `json:"questionNumber"`

	// Question is the text of the question from the question list.
	Question string `json:"question"`

	// Topics is a list of chapters and topics associated with this question.
	Topics []TopicsSchema `json:"topics"`
}

// QuestionWithRubricSchema adds a generated rubric to the question details.
type QuestionWithRubricSchema struct {
	// QuestionNumber is the question number from the input JSON.
	QuestionNumber int `json:"questionNumber"`

	// Question is the content of the question from the input JSON.
	Question string `json:"question"`

	// QuestionScore is the score for the question from the input JSON.
	QuestionScore int `json:"questionScore"`

	// QuestionRubric is the generated rubric for this question.
	QuestionRubric string `json:"questionRubric"`
}
