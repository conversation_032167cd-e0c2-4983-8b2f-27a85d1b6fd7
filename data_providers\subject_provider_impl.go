package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type subjectProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Get implements SubjectProvider.
func (s *subjectProvider) Get(ctx context.Context, program string, grade int) (*[]string, error) {
	ctx, span := s.tracer.Start(ctx, "SubjectProvider : Get")
	defer span.End()
	filter := bson.D{{"program", program}, {"grade", grade}}
	var subjects entities.Subjects
	err := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBViewSubjects).FindOne(ctx, filter).Decode(&subjects)
	if err != nil {
		log.Error().Msg(err.<PERSON>rror())
		return nil, err
	}
	return subjects.Subjects, nil
}

func NewSubjectProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) SubjectProvider {
	return &subjectProvider{
		mongoClient: mongoClient,
		dbName:      databaseName,
		tracer:      tracer,
	}
}
