package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"fmt"

	"github.com/go-openapi/runtime/middleware"
	"go.opentelemetry.io/otel/trace"
)

type GetClassDetailsHandler struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassDetailsHandler(statsProvider data_providers.StatsProvider, termProvider data_providers.TermProvider, tracer trace.Tracer) *GetClassDetailsHandler {
	return &GetClassDetailsHandler{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (h *GetClassDetailsHandler) Handle(params stats.GetClassDetailsParams, principal interface{}) middleware.Responder {
	ctx, span := h.tracer.Start(params.HTTPRequest.Context(), "GetClassDetailsHandler.Handle")
	defer span.End()

	// Get all grade stats for the institute
	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, h.termProvider, params.InstituteID, nil)
	if err != nil {
		return stats.NewGetClassDetailsInternalServerError().WithPayload("Unable to resolve term")
	}

	gradeStats, err := h.statsProvider.GetAllGradeOverallStats(ctx, params.InstituteID, termID)
	if err != nil {
		return stats.NewGetClassDetailsInternalServerError()
	}

	// Transform the data into ClassDetails model
	result := &models.ClassDetails{
		Classes:     make([]string, len(*gradeStats)),
		Assessments: make([]int32, len(*gradeStats)),
		Students:    make([]int32, len(*gradeStats)),
	}

	// Populate the arrays
	for i, stat := range *gradeStats {
		result.Classes[i] = fmt.Sprint(stat.Grade) // Convert grade to string
		result.Assessments[i] = stat.Assessments   // Assuming this field exists in GradeOverallStats
		result.Students[i] = stat.Students         // Assuming this field exists in GradeOverallStats
	}

	return stats.NewGetClassDetailsOK().WithPayload(result)
}
