package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/instructor"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type deleteInstructorImpl struct {
	instructorProvider data_providers.InstructorProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewDeleteInstructorHandler(
	instructorProvider data_providers.InstructorProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) instructor.DeleteInstructorByEmailHandler {
	return &deleteInstructorImpl{
		instructorProvider: instructor<PERSON>rov<PERSON>,
		instituteProvider:  instituteProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *deleteInstructorImpl) <PERSON><PERSON>(params instructor.DeleteInstructorByEmailParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteInstructorHandler")
	defer span.End()

	principal = principal.(string)
	if params.InstituteID == constants.EmptyString {
		return instructor.NewDeleteInstructorByEmailBadRequest().WithPayload("Invalid InstituteID")
	}
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return instructor.NewDeleteInstructorByEmailForbidden().WithPayload("Unauthorized")
	}

	// Get instructor to verify institute
	existingInstructor, err := impl.instructorProvider.GetByEmailAndInstitute(ctx, params.Email, params.InstituteID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return instructor.NewDeleteInstructorByEmailNotFound().WithPayload("Instructor not found")
		}
		log.Error().Err(err).Msg("Failed to get instructor")
		return instructor.NewDeleteInstructorByEmailInternalServerError().WithPayload("Failed to get instructor")
	}

	// Verify instructor belongs to the institute
	if existingInstructor.InstituteID == nil || *existingInstructor.InstituteID != params.InstituteID {
		return instructor.NewDeleteInstructorByEmailBadRequest().WithPayload("Instructor does not belong to this institute")
	}

	// Delete instructor
	deletedBy := principal.(string)
	err = impl.instructorProvider.Delete(ctx, *existingInstructor.ID, deletedBy)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete instructor")
		return instructor.NewDeleteInstructorByEmailInternalServerError().WithPayload("Failed to delete instructor")
	}

	return instructor.NewDeleteInstructorByEmailOK().WithPayload(&models.SuccessResponse{
		ID:      *existingInstructor.ID,
		Message: "Successfully deleted instructor",
	})
}
