package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type userProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

// Add implements EddyOwlUserProvider.
func (e *userProvider) Add(ctx context.Context, eddyOwlUser *entities.User) (string, error) {
	if eddyOwlUser.ID == nil {
		return constants.EmptyString, errors.New("user ID is required")
	}

	now := time.Now()
	eddyOwlUser.CreatedAt = &now

	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : Add")
	defer span.End()

	_, err := e.mongoClient.Database(e.dbName).
		Collection(constants.MongoDBCollectionEddyOwlUsers).
		InsertOne(ctx, eddyOwlUser)
	if err != nil {
		log.Error().Msg(err.Error())
		return constants.EmptyString, err
	}
	return *eddyOwlUser.ID, nil
}

// Delete implements EddyOwlUserProvider.
func (e *userProvider) Delete(ctx context.Context, id string, deletedBy string) error {
	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : Delete")
	defer span.End()
	user, err := e.Get(ctx, id)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	currentTime := time.Now()
	user.DeletedAt = &currentTime
	user.DeletedBy = &deletedBy
	_, err = e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsersArchive).InsertOne(ctx, user)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	res, err := e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsers).DeleteOne(ctx, bson.D{{"_id", id}})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.DeletedCount != 1 {
		log.Error().Msg("User not found : " + id)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Edit implements EddyOwlUserProvider.
func (e *userProvider) Edit(ctx context.Context, id string, eddyOwlUser *entities.User) error {
	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : Edit")
	defer span.End()
	now := time.Now()
	eddyOwlUser.UpdatedAt = &now
	res, err := e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsers).UpdateOne(ctx, bson.D{{"_id", id}}, bson.M{"$set": eddyOwlUser})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.MatchedCount != 1 {
		log.Error().Msg("User not found : " + id)
		return mongo.ErrNoDocuments
	}
	return nil
}

// Get implements EddyOwlUserProvider.
func (e *userProvider) Get(ctx context.Context, id string) (*entities.User, error) {
	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : Get")
	defer span.End()
	eddyOwlUser := &entities.User{}
	err := e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsers).FindOne(ctx, bson.D{{"_id", id}}).Decode(eddyOwlUser)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return eddyOwlUser, nil
}

// GetAll implements EddyOwlUserProvider.
func (e *userProvider) GetAll(ctx context.Context, role int) (*[]entities.User, error) {
	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : GetAll")
	defer span.End()
	eddyOwlUserList := make([]entities.User, 0)
	filter := bson.D{}
	if role > constants.AllRoles {
		filter = bson.D{
			{"role", role},
		}
	}
	cursor, err := e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsers).Find(ctx, filter)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}

	err = cursor.All(ctx, &eddyOwlUserList)

	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return &eddyOwlUserList, nil
}

// GetByEmail implements UserProvider.
func (e *userProvider) GetByEmail(ctx context.Context, email string) (*entities.User, error) {
	ctx, span := e.tracer.Start(ctx, "EddyOwlUserProvider : GetByEmail")
	defer span.End()
	eddyOwlUser := &entities.User{}
	filter := bson.D{{"email", email}}
	err := e.mongoClient.Database(e.dbName).Collection(constants.MongoDBCollectionEddyOwlUsers).FindOne(ctx, filter).Decode(eddyOwlUser)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return eddyOwlUser, nil
}

func NewUserProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) UserProvider {
	return &userProvider{mongoClient: mongoClient, dbName: databaseName, tracer: tracer}
}
