// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// BulkCreateSubmissionHandlerFunc turns a function with the right signature into a bulk create submission handler
type BulkCreateSubmissionHandlerFunc func(BulkCreateSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn BulkCreateSubmissionHandlerFunc) Handle(params BulkCreateSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// BulkCreateSubmissionHandler interface for that can handle valid bulk create submission params
type BulkCreateSubmissionHandler interface {
	Handle(BulkCreateSubmissionParams, interface{}) middleware.Responder
}

// NewBulkCreateSubmission creates a new http.Handler for the bulk create submission operation
func NewBulkCreateSubmission(ctx *middleware.Context, handler BulkCreateSubmissionHandler) *BulkCreateSubmission {
	return &BulkCreateSubmission{Context: ctx, Handler: handler}
}

/*
	BulkCreateSubmission swagger:route PUT /institute/{instituteId}/assignment/{assignmentId}/submission/bulk submission bulkCreateSubmission

# Bulk upload student submissions

Upload multiple student submissions for grading
*/
type BulkCreateSubmission struct {
	Context *middleware.Context
	Handler BulkCreateSubmissionHandler
}

func (o *BulkCreateSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewBulkCreateSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
