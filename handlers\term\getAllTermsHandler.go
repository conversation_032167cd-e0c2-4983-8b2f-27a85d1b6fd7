package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/term"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getAllTermsImpl struct {
	provider          data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

// NewGetAllTermsHandler constructs a new handler for fetching all terms.
func NewGetAllTermsHandler(
	provider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) term.GetAllTermsHandler {
	return &getAllTermsImpl{
		provider:          provider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

// Handle processes the "get all terms" request.
func (impl *getAllTermsImpl) Handle(params term.GetAllTermsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAllTermsHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return term.NewGetAllTermsForbidden().WithPayload("Unauthorized")
	}

	// Extract the instituteID from the params.
	instituteID := params.InstituteID
	if instituteID == constants.EmptyString {
		return term.NewGetAllTermsBadRequest().WithPayload("Invalid or empty institute ID")
	}

	// Retrieve terms from the provider.
	terms, err := impl.provider.GetAll(ctx, instituteID)
	if err != nil {
		log.Error().Msg(err.Error())
		return term.NewGetAllTermsInternalServerError().WithPayload("Unable to fetch Terms")
	}

	// Convert the returned []entities.Term to a slice of *models.Term for the response payload.
	// Adjust field names according to your actual models/structures.
	var termResponses []*models.Term
	if terms != nil {
		for _, term := range *terms {
			// Safely handle possible nil pointers in term
			termID := ""
			name := ""
			startDateStr := ""
			endDateStr := ""

			if term.ID != nil {
				termID = *term.ID
			}
			if term.Name != nil {
				name = *term.Name
			}

			// Convert start/end date from *time.Time to a formatted string
			// using your custom date format if not nil.
			if term.StartDate != nil {
				startDateStr = term.StartDate.Format(constants.CustomDateFormat)
			}
			if term.EndDate != nil {
				endDateStr = term.EndDate.Format(constants.CustomDateFormat)
			}

			// Populate swagger model
			termModel := &models.Term{
				TermID:    termID,
				Name:      name,
				StartDate: startDateStr,
				EndDate:   endDateStr,
			}
			termResponses = append(termResponses, termModel)
		}
	}

	// Return the results in a 200 OK response
	return term.NewGetAllTermsOK().WithPayload(termResponses)
}
