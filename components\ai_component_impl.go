package components

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

// AIClient implements the AIComponent interface and calls the Python/Flask APIs.
type aiInferenceImpl struct {
	baseURL       string
	httpClient    *http.Client
	frameWorkMain string
	modelMain     string
	frameWorkLite string
	modelLite     string
	tracer        trace.Tracer
}

// NewAIInferenceComponent creates a new AIClient with a default HTTP client (10-second timeout).
func NewAIInferenceComponent(baseURL string, frameWorkMain string, modelMain string, frameWorkLite string, modelLite string, tracer trace.Tracer) *aiInferenceImpl {

	return &aiInferenceImpl{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Minute,
		},
		frameWorkMain: frameWorkMain,
		modelMain:     modelMain,
		frameWorkLite: frameWorkLite,
		modelLite:     modelLite,
		tracer:        tracer,
	}
}

// CreateAssignment calls POST /<framework>/<model>/create-assignment
// It sends { "image_urls": [...] } in the JSON body and expects a
// list of QuestionSchema in the response.
func (c *aiInferenceImpl) CreateAssignment(ctx context.Context, imageURLs []string) (*[]QuestionSchema, error) {
	ctx, span := c.tracer.Start(ctx, "AIInference : CreateAssignment")
	defer span.End()
	endpoint := fmt.Sprintf("%s/%s/%s/create-assignment", c.baseURL, c.frameWorkLite, c.modelLite)

	payload := map[string]interface{}{
		"image_urls": imageURLs,
	}

	var result []QuestionSchema
	if err := c.postJSON(ctx, endpoint, payload, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// GradeAssignment calls POST /<framework>/<model>/grade-assignment
// It sends { "testId": ..., "studentId": ... } in the JSON body. The API responds
// with a message and spawns a background thread. This method returns an error only
// if the immediate API call fails.
func (c *aiInferenceImpl) GradeAssignment(ctx context.Context, instituteID, testID, studentID string) error {
	ctx, span := c.tracer.Start(ctx, "AIInference : GradeAssignment")
	defer span.End()
	endpoint := fmt.Sprintf("%s/%s/%s/grade-assignment", c.baseURL, c.frameWorkMain, c.modelMain)
	fmt.Printf("endpoint: %s\n", endpoint)

	payload := map[string]interface{}{
		"instituteId": instituteID,
		"testId":      testID,
		"studentId":   studentID,
	}
	fmt.Printf("payload: %v\n", payload)

	// This endpoint only returns a simple message or an error, so we don't parse into
	// a specialized struct. We'll parse into a generic map and check for "error".
	var respBody map[string]interface{}
	if err := c.postJSON(ctx, endpoint, payload, &respBody); err != nil {
		log.Error().Msg(err.Error())
		return err
	}

	return nil
}

// CreateAutoRubric calls POST /<framework>/<model>/create-auto-rubric
// It sends { "grade": ..., "subject": ..., "questions": [...] } in the JSON body
// and expects a list of QuestionWithRubricSchema in the response.
func (c *aiInferenceImpl) CreateAutoRubric(ctx context.Context, grade int, subject string, questions []QuestionSchema) (*[]QuestionWithRubricSchema, error) {
	ctx, span := c.tracer.Start(ctx, "AIInference : CreateAutoRubric")
	defer span.End()
	endpoint := fmt.Sprintf("%s/%s/%s/create-auto-rubric", c.baseURL, c.frameWorkLite, c.modelLite)
	bytes, err := json.Marshal(questions)
	if err != nil {
		return nil, err
	}
	payload := map[string]interface{}{
		"grade":     grade,
		"subject":   subject,
		"questions": string(bytes),
	}

	var result []QuestionWithRubricSchema
	if err := c.postJSON(ctx, endpoint, payload, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// CreateFileRubric calls POST /<framework>/<model>/create-file-rubric
// It sends { "grade": ..., "subject": ..., "questions": [...], "image_urls": [...] }
// in the JSON body and expects a list of QuestionWithRubricSchema in the response.
func (c *aiInferenceImpl) CreateFileRubric(ctx context.Context, grade int, subject string, questions []QuestionSchema, imageURLs []string) (*[]QuestionWithRubricSchema, error) {
	ctx, span := c.tracer.Start(ctx, "AIInference : CreateFileRubric")
	defer span.End()
	endpoint := fmt.Sprintf("%s/%s/%s/create-file-rubric", c.baseURL, c.frameWorkLite, c.modelLite)
	bytes, err := json.Marshal(questions)
	if err != nil {
		return nil, err
	}
	payload := map[string]interface{}{
		"grade":      grade,
		"subject":    subject,
		"questions":  string(bytes),
		"image_urls": imageURLs,
	}

	var result []QuestionWithRubricSchema
	if err := c.postJSON(ctx, endpoint, payload, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// AssignTopics calls POST /<framework>/<model>/assign-topics
// It sends { "testId": ... } in the JSON body. The Python endpoint spawns a
// background process and immediately returns a success message. The interface
// suggests returning a list of QuestionTopicsSchema, but the actual Python code
// (as shown) does not return that data immediately. Below, we simply check for
// errors and return an empty slice or nil. Adjust as needed if the API
// eventually returns real data.
func (c *aiInferenceImpl) AssignTopics(ctx context.Context, testID, isntituteID string) error {
	endpoint := fmt.Sprintf("%s/%s/%s/assign-topics", c.baseURL, c.frameWorkLite, c.modelLite)
	ctx, span := c.tracer.Start(ctx, "AIInference : AssignTopics")
	defer span.End()

	payload := map[string]interface{}{
		"testId":      testID,
		"instituteId": isntituteID,
	}

	var respBody map[string]interface{}
	if err := c.postJSON(ctx, endpoint, payload, &respBody); err != nil {
		return err
	}

	return nil
}

// -----------------------------------------------------------------------------
// Helper function: POST JSON to an endpoint, decode response into `out`.
//
// If out is nil, we won't decode the response body into a struct.
// If the response has {"error": "..."} then we interpret that as an error.
// -----------------------------------------------------------------------------
func (c *aiInferenceImpl) postJSON(ctx context.Context, endpoint string, payload interface{}, out interface{}) error {
	// Marshal the payload to JSON
	// Create a context with a timeout
	ctx, cancel := context.WithTimeout(ctx, 3*time.Minute)
	defer cancel()

	// Marshal the payload to JSON

	body, err := json.Marshal(payload)
	if err != nil {
		return errors.New(fmt.Sprintf("failed to marshal payload: %v", err))
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, bytes.NewBuffer(body))
	if err != nil {
		return errors.New("failed to create request: " + err.Error())
	}
	req.Header.Set("Content-Type", "application/json")

	// Perform the request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return errors.New(fmt.Sprintf("request error: %v", err))
	}
	defer resp.Body.Close()

	// Read all response data
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.New(fmt.Sprintf("failed to read response body: %v", err))
	}

	// Check if HTTP status is not 2xx
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		return errors.New("non-2xx status code: " + string(resp.StatusCode) + ", body: " + string(respData))
	}

	// If we have an output variable, attempt to decode into it
	if out != nil {
		if err := json.Unmarshal(respData, out); err != nil {
			return errors.New(fmt.Sprintf("failed to unmarshal response JSON: %v", err))
		}
	}

	// Check if the response (now in 'out' or a map) has "error" field
	// If 'out' is a map, we can check. If 'out' is a slice or struct, ignore.
	// For demonstration, let's handle a minimal check for map responses:
	possibleMap, ok := out.(*map[string]interface{})
	if ok && possibleMap != nil {
		if errMsg, hasError := (*possibleMap)["error"]; hasError {
			return errors.New(fmt.Sprintf("API error: %v", errMsg))
		}
	}

	return nil
}
