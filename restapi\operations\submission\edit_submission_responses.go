// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditSubmissionOKCode is the HTTP code returned for type EditSubmissionOK
const EditSubmissionOKCode int = 200

/*
EditSubmissionOK Successful operation

swagger:response editSubmissionOK
*/
type EditSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditSubmissionOK creates EditSubmissionOK with default headers values
func NewEditSubmissionOK() *EditSubmissionOK {

	return &EditSubmissionOK{}
}

// WithPayload adds the payload to the edit submission o k response
func (o *EditSubmissionOK) WithPayload(payload *models.SuccessResponse) *EditSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission o k response
func (o *EditSubmissionOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditSubmissionBadRequestCode is the HTTP code returned for type EditSubmissionBadRequest
const EditSubmissionBadRequestCode int = 400

/*
EditSubmissionBadRequest Bad Request

swagger:response editSubmissionBadRequest
*/
type EditSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionBadRequest creates EditSubmissionBadRequest with default headers values
func NewEditSubmissionBadRequest() *EditSubmissionBadRequest {

	return &EditSubmissionBadRequest{}
}

// WithPayload adds the payload to the edit submission bad request response
func (o *EditSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *EditSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission bad request response
func (o *EditSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditSubmissionForbiddenCode is the HTTP code returned for type EditSubmissionForbidden
const EditSubmissionForbiddenCode int = 403

/*
EditSubmissionForbidden Forbidden

swagger:response editSubmissionForbidden
*/
type EditSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionForbidden creates EditSubmissionForbidden with default headers values
func NewEditSubmissionForbidden() *EditSubmissionForbidden {

	return &EditSubmissionForbidden{}
}

// WithPayload adds the payload to the edit submission forbidden response
func (o *EditSubmissionForbidden) WithPayload(payload models.ErrorResponse) *EditSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission forbidden response
func (o *EditSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditSubmissionNotFoundCode is the HTTP code returned for type EditSubmissionNotFound
const EditSubmissionNotFoundCode int = 404

/*
EditSubmissionNotFound Not Found

swagger:response editSubmissionNotFound
*/
type EditSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionNotFound creates EditSubmissionNotFound with default headers values
func NewEditSubmissionNotFound() *EditSubmissionNotFound {

	return &EditSubmissionNotFound{}
}

// WithPayload adds the payload to the edit submission not found response
func (o *EditSubmissionNotFound) WithPayload(payload models.ErrorResponse) *EditSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission not found response
func (o *EditSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditSubmissionTooManyRequestsCode is the HTTP code returned for type EditSubmissionTooManyRequests
const EditSubmissionTooManyRequestsCode int = 429

/*
EditSubmissionTooManyRequests Too Many Requests

swagger:response editSubmissionTooManyRequests
*/
type EditSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionTooManyRequests creates EditSubmissionTooManyRequests with default headers values
func NewEditSubmissionTooManyRequests() *EditSubmissionTooManyRequests {

	return &EditSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the edit submission too many requests response
func (o *EditSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *EditSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission too many requests response
func (o *EditSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditSubmissionInternalServerErrorCode is the HTTP code returned for type EditSubmissionInternalServerError
const EditSubmissionInternalServerErrorCode int = 500

/*
EditSubmissionInternalServerError Internal Server Error

swagger:response editSubmissionInternalServerError
*/
type EditSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionInternalServerError creates EditSubmissionInternalServerError with default headers values
func NewEditSubmissionInternalServerError() *EditSubmissionInternalServerError {

	return &EditSubmissionInternalServerError{}
}

// WithPayload adds the payload to the edit submission internal server error response
func (o *EditSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *EditSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission internal server error response
func (o *EditSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditSubmissionServiceUnavailableCode is the HTTP code returned for type EditSubmissionServiceUnavailable
const EditSubmissionServiceUnavailableCode int = 503

/*
EditSubmissionServiceUnavailable Service Unavailable

swagger:response editSubmissionServiceUnavailable
*/
type EditSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditSubmissionServiceUnavailable creates EditSubmissionServiceUnavailable with default headers values
func NewEditSubmissionServiceUnavailable() *EditSubmissionServiceUnavailable {

	return &EditSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the edit submission service unavailable response
func (o *EditSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit submission service unavailable response
func (o *EditSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
