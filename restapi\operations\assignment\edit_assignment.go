// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditAssignmentHandlerFunc turns a function with the right signature into a edit assignment handler
type EditAssignmentHandlerFunc func(EditAssignmentParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditAssignmentHandlerFunc) Handle(params EditAssignmentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditAssignmentHandler interface for that can handle valid edit assignment params
type EditAssignmentHandler interface {
	Handle(EditAssignmentParams, interface{}) middleware.Responder
}

// NewEditAssignment creates a new http.Handler for the edit assignment operation
func NewEditAssignment(ctx *middleware.Context, handler EditAssignmentHandler) *EditAssignment {
	return &EditAssignment{Context: ctx, Handler: handler}
}

/*
	EditAssignment swagger:route PUT /institute/{instituteId}/assignment/{assignmentId} assignment editAssignment

# Edit assignment

Edit assignment
*/
type EditAssignment struct {
	Context *middleware.Context
	Handler EditAssignmentHandler
}

func (o *EditAssignment) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditAssignmentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
