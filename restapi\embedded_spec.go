// Code generated by go-swagger; DO NOT EDIT.

package restapi

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"encoding/json"
)

var (
	// SwaggerJSON embedded version of the swagger document used at generation time
	SwaggerJSON json.RawMessage
	// FlatSwaggerJSON embedded flattened version of the swagger document used at generation time
	FlatSwaggerJSON json.RawMessage
)

func init() {
	SwaggerJSON = json.RawMessage([]byte(`{
  "consumes": [
    "application/json"
  ],
  "produces": [
    "application/json"
  ],
  "schemes": [
    "http"
  ],
  "swagger": "2.0",
  "info": {
    "description": "[TO DO]",
    "title": "EddyOwl CORE",
    "termsOfService": "http://swagger.io/terms/",
    "contact": {
      "email": "<EMAIL>"
    },
    "license": {
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
    },
    "version": "1.0.11"
  },
  "host": "localhost:8080",
  "basePath": "/v1/api",
  "paths": {
    "/institute": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Insterts new institute",
        "tags": [
          "institute"
        ],
        "summary": "Create a new institute",
        "operationId": "CreateInstitute",
        "parameters": [
          {
            "name": "newInstitute",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/NewInstitute"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get institute by id",
        "tags": [
          "institute"
        ],
        "summary": "Get institute",
        "operationId": "GetInstituteById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Institute"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit institute information",
        "tags": [
          "institute"
        ],
        "summary": "Edit institute",
        "operationId": "EditInstitute",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "institute",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/EditInstitute"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete institute by id",
        "tags": [
          "institute"
        ],
        "summary": "Delete institute",
        "operationId": "DeleteInstituteById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/allstats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all stats for students, instructors, and assessments",
        "tags": [
          "stats"
        ],
        "summary": "Get all stats",
        "operationId": "GetAllStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AllStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assessments/monthly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all the assessments graded last 6 months for each month",
        "tags": [
          "stats"
        ],
        "summary": "Get monthly assessments",
        "operationId": "GetMonthlyAssessments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/MonthlyAssessmentsGraded"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assessments/weekly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all the assessments graded last week for each day Mon to Fri",
        "tags": [
          "stats"
        ],
        "summary": "Get weekly assessments",
        "operationId": "GetWeeklyAssessments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/WeeklyAssessmentsGraded"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all assignments by Institute Id",
        "tags": [
          "assignment"
        ],
        "summary": "Get all assignments",
        "operationId": "GetAllAssignments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "section",
            "in": "query"
          },
          {
            "type": "string",
            "name": "subject",
            "in": "query"
          },
          {
            "type": "string",
            "description": "Return only assignments in this folder",
            "name": "folderId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AssignmentList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new assignment",
        "tags": [
          "assignment"
        ],
        "summary": "Create new assignment",
        "operationId": "CreateAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "assignment",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/NewAssignment"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/auto": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new assignment from upload",
        "tags": [
          "auto"
        ],
        "summary": "Create new assignment from upload",
        "operationId": "AutoCreateAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "files",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/FileList"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get assignment by assignmentid for an institute",
        "tags": [
          "assignment"
        ],
        "summary": "Get assignment",
        "operationId": "GetAssignmentByID",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Assignment"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit assignment",
        "tags": [
          "assignment"
        ],
        "summary": "Edit assignment",
        "operationId": "EditAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "name": "assignment",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Assignment"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete assignment by assignmentid for an institute",
        "tags": [
          "assignment"
        ],
        "summary": "Delete assignment",
        "operationId": "DeleteAssignmentByID",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/publish": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Publish all submissions",
        "tags": [
          "submission"
        ],
        "summary": "Publish all submissions",
        "operationId": "PublishAllSubmissions",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/edit/submission": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit scores and feedabck fo graded submission",
        "tags": [
          "submission"
        ],
        "summary": "Edit student gradedsubmission",
        "operationId": "EditSubmission",
        "parameters": [
          {
            "type": "string",
            "description": "ID of the institute",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the assignment",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the student",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "name": "submission",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/EditedSubmission"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get graded submission",
        "tags": [
          "submission"
        ],
        "summary": "Get graded student submission",
        "operationId": "GetSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentSubmission"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Upload solutions to S3",
        "tags": [
          "submission"
        ],
        "summary": "Upload student solutions",
        "operationId": "CreateSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "name": "files",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/FileList"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete submission",
        "tags": [
          "submission"
        ],
        "summary": "Delete student submission",
        "operationId": "DeleteSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/performance": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get graded submission performance",
        "tags": [
          "stats"
        ],
        "summary": "Get graded student submission performance",
        "operationId": "GetGradedSubmissionPerformance",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/GradedSubmissionPerformance"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/publish": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Publish submission",
        "tags": [
          "submission"
        ],
        "summary": "Publish student submission",
        "operationId": "PublishSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/submission/bulk": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Upload multiple student submissions for grading",
        "tags": [
          "submission"
        ],
        "summary": "Bulk upload student submissions",
        "operationId": "BulkCreateSubmission",
        "parameters": [
          {
            "type": "string",
            "description": "ID of the institute",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the assignment",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "name": "submissions",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/BulkCreateSubmissionBody"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/topics": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add topics by question number",
        "tags": [
          "assignment"
        ],
        "summary": "Add topics to assignment",
        "operationId": "AddTopicsToAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/auto/rubric": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Auto Add Rubric",
        "tags": [
          "auto"
        ],
        "summary": "Auto Add Rubric",
        "operationId": "AutoAddRubric",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "questions",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/RubricInput"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/allstats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Stats",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Stats",
        "operationId": "GetClassStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AllStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysections": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Sections.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Sections",
        "operationId": "GetClassAssessmentsBySections",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAssessmentsBySections"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysub": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Subject",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Subject",
        "operationId": "GetClassAssessmentsBySubject",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAssessmentsBySubject"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysub/monthly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Subject Monthly for the last 12 months.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Subject Monthly",
        "operationId": "GetClassAssessmentsBySubjectMonthly",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAggMonthlyAssessmentsBySubject"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/studentsbysections": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Students By Sections.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Students By Sections",
        "operationId": "GetClassStudentsBySections",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassStudentsBySections"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/topstudents": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Top Performing Students",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Top Performing Students",
        "operationId": "GetClassTopPerformingStudents",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassTopPerformingStudents"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/classdetails": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Details",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Details",
        "operationId": "GetClassDetails",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassDetails"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/folder": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "tags": [
          "folder"
        ],
        "summary": "Get all folders for an institute",
        "operationId": "GetAllFolders",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "List of folders",
            "schema": {
              "type": "array",
              "items": {
                "$ref": "#/definitions/Folder"
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create a folder under an institute",
        "tags": [
          "folder"
        ],
        "summary": "Create a new folder",
        "operationId": "CreateFolder",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "folder",
            "in": "body",
            "schema": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string",
                  "example": "Chapter 1 Assignments"
                }
              }
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Folder created",
            "schema": {
              "$ref": "#/definitions/Folder"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/folder/{folderId}": {
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "tags": [
          "folder"
        ],
        "summary": "Delete a folder",
        "operationId": "DeleteFolder",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "folderId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Folder deleted",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/instructor": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get instructors by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Get instructors",
        "operationId": "GetInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/InstructorList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit instructor by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Edit instructor",
        "operationId": "EditInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "instructor",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Instructor"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add instructor by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Add instructor",
        "operationId": "AddInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "instructor",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Instructor"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete instructor by instructor email",
        "tags": [
          "instructor"
        ],
        "summary": "Delete instructor",
        "operationId": "DeleteInstructorByEmail",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "email",
            "in": "query",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all students by Institute Id",
        "tags": [
          "student"
        ],
        "summary": "Get all students",
        "operationId": "GetAllStudents",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "sections",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit student",
        "tags": [
          "student"
        ],
        "summary": "Edit student",
        "operationId": "EditStudent",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "student",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new student",
        "tags": [
          "student"
        ],
        "summary": "Create new student",
        "operationId": "CreateNewStudent",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "student",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/email/{email}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get student by email address",
        "tags": [
          "student"
        ],
        "summary": "Get student by email",
        "operationId": "GetStudentByEmail",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "email",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/{studentId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get student by School Student Id",
        "tags": [
          "student"
        ],
        "summary": "Get student",
        "operationId": "GetStudentById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete student by School Student Id",
        "tags": [
          "student"
        ],
        "summary": "Delete student",
        "operationId": "DeleteStudentById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/{studentId}/studentOverallStats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get overall stats for student",
        "tags": [
          "stats"
        ],
        "summary": "Get Student Overall Stats",
        "operationId": "GetStudentOverallStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentOverallStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/subject/grade/{grade}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Returns list of subjects by grade",
        "tags": [
          "subject"
        ],
        "summary": "Get subjects by grade",
        "operationId": "GetSubjects",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SubjectList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/submissions": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get submissions based on query",
        "tags": [
          "submission"
        ],
        "summary": "Get all submissions",
        "operationId": "GetAllSubmissions",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "section",
            "in": "query"
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SubmissionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/term/{termId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get term by School Term Id",
        "tags": [
          "term"
        ],
        "summary": "Get term",
        "operationId": "GetTermById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Term"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete term by School Term Id",
        "tags": [
          "term"
        ],
        "summary": "Delete term",
        "operationId": "DeleteTermById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/terms": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all terms by Institute Id",
        "tags": [
          "term"
        ],
        "summary": "Get all terms",
        "operationId": "GetAllTerms",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/TermList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit term",
        "tags": [
          "term"
        ],
        "summary": "Edit term",
        "operationId": "EditTerm",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "term",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Term"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new term",
        "tags": [
          "term"
        ],
        "summary": "Create new term",
        "operationId": "CreateNewTerm",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "term",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/NewTerm"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/upload/rubric": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add Rubric from a file",
        "tags": [
          "auto"
        ],
        "summary": "Add rubric from a file.",
        "operationId": "AutoFileRubric",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "options",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/RubricUpload"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/userRoles": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Returns all roles for a user",
        "tags": [
          "user"
        ],
        "summary": "Get user roles",
        "operationId": "GetUserRoles",
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/UserRoles"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    }
  },
  "definitions": {
    "Address": {
      "type": "object",
      "properties": {
        "addressOne": {
          "type": "string"
        },
        "addressTwo": {
          "type": "string"
        },
        "city": {
          "type": "string"
        },
        "pincode": {
          "type": "string"
        },
        "state": {
          "type": "string"
        }
      }
    },
    "AllStats": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "integer",
          "format": "int32"
        },
        "instructors": {
          "type": "integer",
          "format": "int32"
        },
        "overallStudentPerformance": {
          "type": "integer",
          "format": "int32"
        },
        "students": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "Assignment": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "duration": {
          "type": "integer",
          "format": "int32"
        },
        "folderId": {
          "description": "Optional folder ID the assignment belongs to",
          "type": "string"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Assignment-1"
        },
        "questions": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/Question"
          }
        },
        "sectionList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "subjectName": {
          "type": "string"
        },
        "totalScore": {
          "type": "number",
          "format": "float"
        }
      }
    },
    "AssignmentList": {
      "type": "array",
      "items": {
        "type": "object",
        "$ref": "#/definitions/Assignment"
      }
    },
    "AssignmentResult": {
      "type": "object",
      "properties": {
        "StudentResponseList": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/StudentResponse"
          }
        },
        "acheivedScore": {
          "type": "number",
          "format": "float",
          "example": 75.5
        },
        "imageIdList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "missedQuestionNumbers": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "BulkCreateSubmissionBody": {
      "type": "object",
      "properties": {
        "submissionData": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/SubmissionData"
          }
        }
      }
    },
    "ChapterStats": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/TopicStats"
          }
        }
      }
    },
    "ChapterTopics": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ChaptersWithScore": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "score": {
          "type": "number",
          "format": "float"
        },
        "topicsWithScore": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/TopicsWithScore"
          }
        }
      }
    },
    "ClassAggMonthlyAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "data": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ClassMonthlyAssessmentsBySubject"
          }
        },
        "months": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassAssessmentsBySections": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "sections": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subjects": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassDetails": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "classes": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "students": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    },
    "ClassMonthlyAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subjects": {
          "type": "string"
        }
      }
    },
    "ClassStudentsBySections": {
      "type": "object",
      "properties": {
        "sections": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "students": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    },
    "ClassTopPerformingStudent": {
      "type": "object",
      "properties": {
        "highestScore": {
          "type": "number",
          "format": "float"
        },
        "lowestScore": {
          "type": "number",
          "format": "float"
        },
        "overallPerformance": {
          "type": "number",
          "format": "float"
        },
        "student": {
          "type": "string"
        }
      }
    },
    "ClassTopPerformingStudents": {
      "type": "object",
      "properties": {
        "data": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ClassTopPerformingStudent"
          }
        }
      }
    },
    "EditInstitute": {
      "type": "object",
      "properties": {
        "address": {
          "$ref": "#/definitions/Address"
        },
        "availableSections": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "example": [
            "A",
            "B",
            "C"
          ]
        },
        "name": {
          "type": "string"
        }
      }
    },
    "EditedStudentResponse": {
      "type": "object",
      "properties": {
        "feedback": {
          "type": "string",
          "example": "Improvement needed in so and so area."
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "score": {
          "type": "number",
          "format": "float",
          "example": 5
        },
        "studentResponse": {
          "type": "string",
          "example": "Mitochondria is the powerhouse of the cell."
        }
      }
    },
    "EditedSubmission": {
      "type": "object",
      "properties": {
        "studentResponses": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/EditedStudentResponse"
          }
        }
      }
    },
    "ErrorResponse": {
      "type": "string",
      "example": "Unable"
    },
    "FileList": {
      "type": "object",
      "properties": {
        "files": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "Folder": {
      "type": "object",
      "properties": {
        "createdAt": {
          "type": "string",
          "format": "date-time"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string"
        },
        "userId": {
          "type": "string"
        }
      }
    },
    "GradedSubmissionPerformance": {
      "type": "object",
      "properties": {
        "stats": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/QuestionStats"
          }
        }
      }
    },
    "Institute": {
      "type": "object",
      "properties": {
        "address": {
          "type": "object",
          "$ref": "#/definitions/Address"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Saraswati Vidya Mandir"
        },
        "program": {
          "type": "string"
        },
        "sectionList": {
          "type": "object",
          "$ref": "#/definitions/SectionList"
        }
      }
    },
    "Instructor": {
      "type": "object",
      "properties": {
        "email": {
          "type": "string"
        },
        "firstName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "role": {
          "description": "0: AdminRole\n1: InstructorRole\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            1,
            2
          ],
          "x-go-const-names": [
            "AdminRole",
            "InstructorRole"
          ]
        }
      }
    },
    "InstructorList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Instructor"
      }
    },
    "MonthlyAssessmentsGraded": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "months": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "NewAssignment": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "duration": {
          "type": "integer",
          "format": "int32"
        },
        "folderId": {
          "description": "Optional folder ID if assignment is inside a folder",
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Assignment-1"
        },
        "questions": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/Question"
          }
        },
        "sectionList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "subjectName": {
          "type": "string"
        },
        "totalScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "NewInstitute": {
      "type": "object",
      "properties": {
        "address": {
          "type": "object",
          "$ref": "#/definitions/Address"
        },
        "name": {
          "type": "string",
          "example": "Saraswati Vidya Mandir"
        },
        "program": {
          "type": "string"
        },
        "terms": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/NewTerm"
          }
        }
      }
    },
    "NewTerm": {
      "type": "object",
      "properties": {
        "endDate": {
          "type": "string",
          "format": "date-time"
        },
        "name": {
          "type": "string"
        },
        "startDate": {
          "type": "string",
          "format": "date-time"
        }
      }
    },
    "Question": {
      "type": "object",
      "properties": {
        "question": {
          "type": "string"
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "questionRubric": {
          "type": "string"
        },
        "questionScore": {
          "type": "number",
          "format": "float"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterTopics"
          }
        }
      }
    },
    "QuestionList": {
      "type": "object",
      "properties": {
        "questionList": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/Question"
          }
        }
      }
    },
    "QuestionStats": {
      "type": "object",
      "properties": {
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "stats": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterStats"
          }
        }
      }
    },
    "Role": {
      "type": "object",
      "properties": {
        "instituteId": {
          "type": "string"
        },
        "role": {
          "description": "0: AdminRole\n1: InstructorRole\n2: StudentRole\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "x-go-const-names": [
            "AdminRole",
            "InstructorRole",
            "StudentRole"
          ]
        }
      }
    },
    "RubricInput": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "questions": {
          "type": "object",
          "$ref": "#/definitions/QuestionList"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "RubricUpload": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "filePaths": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "questions": {
          "type": "object",
          "$ref": "#/definitions/QuestionList"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "SectionList": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "StatusHistory": {
      "type": "object",
      "properties": {
        "status": {
          "description": "0: Due\n1: Submitted\n2: Processing\n3: Graded\n4: Failed\n5: Published\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            0,
            1,
            2,
            3,
            4,
            5
          ],
          "x-go-const-names": [
            "SubmissionStatusDue",
            "SubmissionStatusSubmitted",
            "SubmissionStatusProcessing",
            "SubmissionStatusGraded",
            "SubmissionStatusFailed",
            "SubmissionStatusPublished"
          ]
        },
        "timestamp": {
          "type": "string",
          "format": "date-time"
        },
        "updated_by": {
          "type": "string"
        }
      }
    },
    "Student": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32",
          "example": 10
        },
        "email": {
          "type": "string"
        },
        "firstName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "rollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "section": {
          "type": "string",
          "example": "C"
        },
        "studentId": {
          "type": "string"
        }
      }
    },
    "StudentList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Student"
      }
    },
    "StudentOverallPerformance": {
      "type": "object",
      "properties": {
        "totalAchievedScore": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        },
        "totalAttemptedScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "StudentOverallStats": {
      "type": "object",
      "properties": {
        "averageStudentPerformance": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "StudentPerformance": {
      "type": "object",
      "properties": {
        "assessmentPerformance": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/SubjectAssessmentPerformance"
          }
        },
        "highestPerformance": {
          "type": "number",
          "format": "float"
        },
        "lowestPerformance": {
          "type": "number",
          "format": "float"
        },
        "overallPerformance": {
          "type": "object",
          "$ref": "#/definitions/StudentOverallPerformance"
        }
      }
    },
    "StudentResponse": {
      "type": "object",
      "properties": {
        "feedback": {
          "type": "string",
          "example": "Improvement needed in so and so area."
        },
        "question": {
          "type": "string"
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "questionRubric": {
          "type": "string"
        },
        "questionScore": {
          "type": "integer",
          "format": "int32"
        },
        "score": {
          "type": "number",
          "format": "float",
          "example": 5
        },
        "studentResponse": {
          "type": "string",
          "example": "Mitochondria is the powerhouse of the cell."
        }
      }
    },
    "StudentSubmission": {
      "type": "object",
      "properties": {
        "assignmentId": {
          "type": "string"
        },
        "assignmentName": {
          "type": "string"
        },
        "assignmentScore": {
          "type": "integer",
          "format": "int32"
        },
        "chapterPerformance": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ChaptersWithScore"
          }
        },
        "createdAt": {
          "type": "string",
          "format": "date-time"
        },
        "createdBy": {
          "type": "string"
        },
        "grade": {
          "type": "integer",
          "format": "int32"
        },
        "history": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/StatusHistory"
          }
        },
        "id": {
          "type": "string"
        },
        "imageIds": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "instituteId": {
          "type": "string"
        },
        "missedQuestions": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "studentEmail": {
          "type": "string"
        },
        "studentFirstName": {
          "type": "string"
        },
        "studentGrade": {
          "type": "integer",
          "format": "int32"
        },
        "studentId": {
          "type": "string"
        },
        "studentLastName": {
          "type": "string"
        },
        "studentResponses": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "feedback": {
                "type": "string"
              },
              "question": {
                "type": "string"
              },
              "questionNumber": {
                "type": "integer",
                "format": "int32"
              },
              "questionScore": {
                "type": "integer",
                "format": "int32"
              },
              "response": {
                "type": "string"
              },
              "rubric": {
                "type": "string"
              },
              "score": {
                "type": "number",
                "format": "float"
              },
              "topics": {
                "type": "array",
                "items": {
                  "$ref": "#/definitions/ChapterTopics"
                }
              }
            }
          }
        },
        "studentRollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "studentSection": {
          "type": "string"
        },
        "subject": {
          "type": "string"
        },
        "termId": {
          "type": "string"
        },
        "totalScore": {
          "type": "number",
          "format": "float"
        },
        "updatedAt": {
          "type": "string",
          "format": "date-time"
        },
        "updatedBy": {
          "type": "string"
        }
      }
    },
    "SubjectAssessmentPerformance": {
      "type": "object",
      "properties": {
        "subject": {
          "type": "string"
        },
        "totalAchievedScore": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        },
        "totalAttemptedScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "SubjectList": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "SubjectStats": {
      "type": "object",
      "properties": {
        "chapters": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterStats"
          }
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "Submission": {
      "type": "object",
      "properties": {
        "assignmentId": {
          "type": "string"
        },
        "assignmentName": {
          "type": "string"
        },
        "assignmentScore": {
          "type": "integer",
          "format": "int32"
        },
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "section": {
          "type": "string"
        },
        "status": {
          "type": "string"
        },
        "studentId": {
          "type": "string"
        },
        "studentName": {
          "type": "string"
        },
        "studentRollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "studentScore": {
          "type": "number",
          "format": "float"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "SubmissionData": {
      "type": "object",
      "required": [
        "studentId",
        "imageUrls"
      ],
      "properties": {
        "imageUrls": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "studentId": {
          "type": "string"
        }
      }
    },
    "SubmissionList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Submission"
      }
    },
    "SuccessResponse": {
      "type": "object",
      "properties": {
        "id": {
          "type": "string"
        },
        "message": {
          "type": "string"
        }
      }
    },
    "Term": {
      "type": "object",
      "properties": {
        "endDate": {
          "type": "string"
        },
        "name": {
          "type": "string"
        },
        "startDate": {
          "type": "string"
        },
        "termId": {
          "type": "string"
        }
      }
    },
    "TermList": {
      "type": "array",
      "items": {
        "type": "object",
        "$ref": "#/definitions/Term"
      }
    },
    "TopicStats": {
      "type": "object",
      "properties": {
        "score": {
          "type": "integer",
          "format": "int32"
        },
        "topic": {
          "type": "string"
        }
      }
    },
    "TopicsByQuestion": {
      "type": "object",
      "properties": {
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterTopics"
          }
        }
      }
    },
    "TopicsWithScore": {
      "type": "object",
      "properties": {
        "topic": {
          "type": "string"
        },
        "topicScore": {
          "type": "number",
          "format": "float"
        }
      }
    },
    "UserRoles": {
      "type": "object",
      "properties": {
        "roles": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/Role"
          }
        }
      }
    },
    "WeeklyAssessmentsGraded": {
      "type": "object",
      "properties": {
        "weeklyData": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    }
  },
  "securityDefinitions": {
    "eddyowl-okta": {
      "type": "oauth2",
      "flow": "accessCode",
      "authorizationUrl": "https://dev-z8m8ne04to0cvoad.us.auth0.com/authorize?audience=https%3A%2F%2Feddyowl.com%2Fv1%2Fapi",
      "tokenUrl": "https://dev-z8m8ne04to0cvoad.us.auth0.com/oauth/token",
      "scopes": {
        "openid profile email": "openid profile email"
      }
    }
  },
  "tags": [
    {
      "description": "Operations on Institute",
      "name": "institute"
    },
    {
      "description": "Operations on Instructor",
      "name": "instructor"
    },
    {
      "description": "Operations on Academic Term",
      "name": "term"
    },
    {
      "description": "Operations on Student",
      "name": "student"
    },
    {
      "description": "Operations on Assignment",
      "name": "assignment"
    },
    {
      "description": "Operations on Assignment",
      "name": "folder"
    },
    {
      "description": "Automated Operations",
      "name": "auto"
    },
    {
      "description": "Operations on Submission",
      "name": "submission"
    },
    {
      "description": "Operations on user",
      "name": "user"
    },
    {
      "description": "Operations on statistics",
      "name": "stats"
    }
  ]
}`))
	FlatSwaggerJSON = json.RawMessage([]byte(`{
  "consumes": [
    "application/json"
  ],
  "produces": [
    "application/json"
  ],
  "schemes": [
    "http"
  ],
  "swagger": "2.0",
  "info": {
    "description": "[TO DO]",
    "title": "EddyOwl CORE",
    "termsOfService": "http://swagger.io/terms/",
    "contact": {
      "email": "<EMAIL>"
    },
    "license": {
      "name": "Apache 2.0",
      "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
    },
    "version": "1.0.11"
  },
  "host": "localhost:8080",
  "basePath": "/v1/api",
  "paths": {
    "/institute": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Insterts new institute",
        "tags": [
          "institute"
        ],
        "summary": "Create a new institute",
        "operationId": "CreateInstitute",
        "parameters": [
          {
            "name": "newInstitute",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/NewInstitute"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get institute by id",
        "tags": [
          "institute"
        ],
        "summary": "Get institute",
        "operationId": "GetInstituteById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Institute"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit institute information",
        "tags": [
          "institute"
        ],
        "summary": "Edit institute",
        "operationId": "EditInstitute",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "institute",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/EditInstitute"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete institute by id",
        "tags": [
          "institute"
        ],
        "summary": "Delete institute",
        "operationId": "DeleteInstituteById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/allstats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all stats for students, instructors, and assessments",
        "tags": [
          "stats"
        ],
        "summary": "Get all stats",
        "operationId": "GetAllStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AllStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assessments/monthly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all the assessments graded last 6 months for each month",
        "tags": [
          "stats"
        ],
        "summary": "Get monthly assessments",
        "operationId": "GetMonthlyAssessments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/MonthlyAssessmentsGraded"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assessments/weekly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all the assessments graded last week for each day Mon to Fri",
        "tags": [
          "stats"
        ],
        "summary": "Get weekly assessments",
        "operationId": "GetWeeklyAssessments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/WeeklyAssessmentsGraded"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all assignments by Institute Id",
        "tags": [
          "assignment"
        ],
        "summary": "Get all assignments",
        "operationId": "GetAllAssignments",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "section",
            "in": "query"
          },
          {
            "type": "string",
            "name": "subject",
            "in": "query"
          },
          {
            "type": "string",
            "description": "Return only assignments in this folder",
            "name": "folderId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AssignmentList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new assignment",
        "tags": [
          "assignment"
        ],
        "summary": "Create new assignment",
        "operationId": "CreateAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "assignment",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/NewAssignment"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/auto": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new assignment from upload",
        "tags": [
          "auto"
        ],
        "summary": "Create new assignment from upload",
        "operationId": "AutoCreateAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "files",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/FileList"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get assignment by assignmentid for an institute",
        "tags": [
          "assignment"
        ],
        "summary": "Get assignment",
        "operationId": "GetAssignmentByID",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Assignment"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit assignment",
        "tags": [
          "assignment"
        ],
        "summary": "Edit assignment",
        "operationId": "EditAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "name": "assignment",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Assignment"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete assignment by assignmentid for an institute",
        "tags": [
          "assignment"
        ],
        "summary": "Delete assignment",
        "operationId": "DeleteAssignmentByID",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/publish": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Publish all submissions",
        "tags": [
          "submission"
        ],
        "summary": "Publish all submissions",
        "operationId": "PublishAllSubmissions",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/edit/submission": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit scores and feedabck fo graded submission",
        "tags": [
          "submission"
        ],
        "summary": "Edit student gradedsubmission",
        "operationId": "EditSubmission",
        "parameters": [
          {
            "type": "string",
            "description": "ID of the institute",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the assignment",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the student",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "name": "submission",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/EditedSubmission"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get graded submission",
        "tags": [
          "submission"
        ],
        "summary": "Get graded student submission",
        "operationId": "GetSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentSubmission"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Upload solutions to S3",
        "tags": [
          "submission"
        ],
        "summary": "Upload student solutions",
        "operationId": "CreateSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "name": "files",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/FileList"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete submission",
        "tags": [
          "submission"
        ],
        "summary": "Delete student submission",
        "operationId": "DeleteSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/performance": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get graded submission performance",
        "tags": [
          "stats"
        ],
        "summary": "Get graded student submission performance",
        "operationId": "GetGradedSubmissionPerformance",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/GradedSubmissionPerformance"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/publish": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Publish submission",
        "tags": [
          "submission"
        ],
        "summary": "Publish student submission",
        "operationId": "PublishSubmission",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/submission/bulk": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Upload multiple student submissions for grading",
        "tags": [
          "submission"
        ],
        "summary": "Bulk upload student submissions",
        "operationId": "BulkCreateSubmission",
        "parameters": [
          {
            "type": "string",
            "description": "ID of the institute",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "description": "ID of the assignment",
            "name": "assignmentId",
            "in": "path",
            "required": true
          },
          {
            "name": "submissions",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/BulkCreateSubmissionBody"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/assignment/{assignmentId}/topics": {
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add topics by question number",
        "tags": [
          "assignment"
        ],
        "summary": "Add topics to assignment",
        "operationId": "AddTopicsToAssignment",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/auto/rubric": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Auto Add Rubric",
        "tags": [
          "auto"
        ],
        "summary": "Auto Add Rubric",
        "operationId": "AutoAddRubric",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "questions",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/RubricInput"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/allstats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Stats",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Stats",
        "operationId": "GetClassStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/AllStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysections": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Sections.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Sections",
        "operationId": "GetClassAssessmentsBySections",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAssessmentsBySections"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysub": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Subject",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Subject",
        "operationId": "GetClassAssessmentsBySubject",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAssessmentsBySubject"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/assessmentsbysub/monthly": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Assessments By Subject Monthly for the last 12 months.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Assessments By Subject Monthly",
        "operationId": "GetClassAssessmentsBySubjectMonthly",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassAggMonthlyAssessmentsBySubject"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/studentsbysections": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Students By Sections.",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Students By Sections",
        "operationId": "GetClassStudentsBySections",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassStudentsBySections"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/class/{class}/topstudents": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Top Performing Students",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Top Performing Students",
        "operationId": "GetClassTopPerformingStudents",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassTopPerformingStudents"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/classdetails": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get Class Details",
        "tags": [
          "stats"
        ],
        "summary": "Get Class Details",
        "operationId": "GetClassDetails",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/ClassDetails"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/folder": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "tags": [
          "folder"
        ],
        "summary": "Get all folders for an institute",
        "operationId": "GetAllFolders",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "List of folders",
            "schema": {
              "type": "array",
              "items": {
                "$ref": "#/definitions/Folder"
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create a folder under an institute",
        "tags": [
          "folder"
        ],
        "summary": "Create a new folder",
        "operationId": "CreateFolder",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "folder",
            "in": "body",
            "schema": {
              "type": "object",
              "properties": {
                "name": {
                  "type": "string",
                  "example": "Chapter 1 Assignments"
                }
              }
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Folder created",
            "schema": {
              "$ref": "#/definitions/Folder"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/folder/{folderId}": {
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "tags": [
          "folder"
        ],
        "summary": "Delete a folder",
        "operationId": "DeleteFolder",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "folderId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Folder deleted",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/instructor": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get instructors by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Get instructors",
        "operationId": "GetInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/InstructorList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit instructor by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Edit instructor",
        "operationId": "EditInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "instructor",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Instructor"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add instructor by institute id",
        "tags": [
          "instructor"
        ],
        "summary": "Add instructor",
        "operationId": "AddInstructorByInstituteId",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "instructor",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Instructor"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete instructor by instructor email",
        "tags": [
          "instructor"
        ],
        "summary": "Delete instructor",
        "operationId": "DeleteInstructorByEmail",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "email",
            "in": "query",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all students by Institute Id",
        "tags": [
          "student"
        ],
        "summary": "Get all students",
        "operationId": "GetAllStudents",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "class",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "sections",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit student",
        "tags": [
          "student"
        ],
        "summary": "Edit student",
        "operationId": "EditStudent",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "student",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new student",
        "tags": [
          "student"
        ],
        "summary": "Create new student",
        "operationId": "CreateNewStudent",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "name": "student",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/email/{email}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get student by email address",
        "tags": [
          "student"
        ],
        "summary": "Get student by email",
        "operationId": "GetStudentByEmail",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "email",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unavailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/{studentId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get student by School Student Id",
        "tags": [
          "student"
        ],
        "summary": "Get student",
        "operationId": "GetStudentById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Student"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete student by School Student Id",
        "tags": [
          "student"
        ],
        "summary": "Delete student",
        "operationId": "DeleteStudentById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/student/{studentId}/studentOverallStats": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get overall stats for student",
        "tags": [
          "stats"
        ],
        "summary": "Get Student Overall Stats",
        "operationId": "GetStudentOverallStats",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/StudentOverallStats"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/subject/grade/{grade}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Returns list of subjects by grade",
        "tags": [
          "subject"
        ],
        "summary": "Get subjects by grade",
        "operationId": "GetSubjects",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SubjectList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/submissions": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get submissions based on query",
        "tags": [
          "submission"
        ],
        "summary": "Get all submissions",
        "operationId": "GetAllSubmissions",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "assignmentId",
            "in": "query"
          },
          {
            "type": "integer",
            "format": "int32",
            "name": "grade",
            "in": "query"
          },
          {
            "type": "array",
            "items": {
              "type": "string"
            },
            "name": "section",
            "in": "query"
          },
          {
            "type": "string",
            "name": "termId",
            "in": "query"
          },
          {
            "type": "string",
            "name": "studentId",
            "in": "query"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SubmissionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/term/{termId}": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get term by School Term Id",
        "tags": [
          "term"
        ],
        "summary": "Get term",
        "operationId": "GetTermById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/Term"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "delete": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Delete term by School Term Id",
        "tags": [
          "term"
        ],
        "summary": "Delete term",
        "operationId": "DeleteTermById",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "type": "string",
            "name": "termId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/terms": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Get all terms by Institute Id",
        "tags": [
          "term"
        ],
        "summary": "Get all terms",
        "operationId": "GetAllTerms",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/TermList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Edit term",
        "tags": [
          "term"
        ],
        "summary": "Edit term",
        "operationId": "EditTerm",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "term",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/Term"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      },
      "post": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Create new term",
        "tags": [
          "term"
        ],
        "summary": "Create new term",
        "operationId": "CreateNewTerm",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "term",
            "in": "body",
            "schema": {
              "$ref": "#/definitions/NewTerm"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/SuccessResponse"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/institute/{instituteId}/upload/rubric": {
      "put": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Add Rubric from a file",
        "tags": [
          "auto"
        ],
        "summary": "Add rubric from a file.",
        "operationId": "AutoFileRubric",
        "parameters": [
          {
            "type": "string",
            "name": "instituteId",
            "in": "path",
            "required": true
          },
          {
            "name": "options",
            "in": "body",
            "required": true,
            "schema": {
              "$ref": "#/definitions/RubricUpload"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/QuestionList"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    },
    "/userRoles": {
      "get": {
        "security": [
          {
            "eddyowl-okta": [
              "https://eddyowl.com/backend"
            ]
          }
        ],
        "description": "Returns all roles for a user",
        "tags": [
          "user"
        ],
        "summary": "Get user roles",
        "operationId": "GetUserRoles",
        "responses": {
          "200": {
            "description": "Successful operation",
            "schema": {
              "$ref": "#/definitions/UserRoles"
            }
          },
          "400": {
            "description": "Bad Request",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "401": {
            "description": "Unauthorized",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "403": {
            "description": "Forbidden",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "404": {
            "description": "Not Found",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "429": {
            "description": "Too Many Requests",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "500": {
            "description": "Internal Server Error",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          },
          "503": {
            "description": "Service Unvailable",
            "schema": {
              "$ref": "#/definitions/ErrorResponse"
            }
          }
        }
      }
    }
  },
  "definitions": {
    "Address": {
      "type": "object",
      "properties": {
        "addressOne": {
          "type": "string"
        },
        "addressTwo": {
          "type": "string"
        },
        "city": {
          "type": "string"
        },
        "pincode": {
          "type": "string"
        },
        "state": {
          "type": "string"
        }
      }
    },
    "AllStats": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "integer",
          "format": "int32"
        },
        "instructors": {
          "type": "integer",
          "format": "int32"
        },
        "overallStudentPerformance": {
          "type": "integer",
          "format": "int32"
        },
        "students": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "Assignment": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "duration": {
          "type": "integer",
          "format": "int32"
        },
        "folderId": {
          "description": "Optional folder ID the assignment belongs to",
          "type": "string"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Assignment-1"
        },
        "questions": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/Question"
          }
        },
        "sectionList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "subjectName": {
          "type": "string"
        },
        "totalScore": {
          "type": "number",
          "format": "float"
        }
      }
    },
    "AssignmentList": {
      "type": "array",
      "items": {
        "type": "object",
        "$ref": "#/definitions/Assignment"
      }
    },
    "AssignmentResult": {
      "type": "object",
      "properties": {
        "StudentResponseList": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/StudentResponse"
          }
        },
        "acheivedScore": {
          "type": "number",
          "format": "float",
          "example": 75.5
        },
        "imageIdList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "missedQuestionNumbers": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "BulkCreateSubmissionBody": {
      "type": "object",
      "properties": {
        "submissionData": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/SubmissionData"
          }
        }
      }
    },
    "ChapterStats": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/TopicStats"
          }
        }
      }
    },
    "ChapterTopics": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ChaptersWithScore": {
      "type": "object",
      "properties": {
        "chapter": {
          "type": "string"
        },
        "score": {
          "type": "number",
          "format": "float"
        },
        "topicsWithScore": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/TopicsWithScore"
          }
        }
      }
    },
    "ClassAggMonthlyAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "data": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ClassMonthlyAssessmentsBySubject"
          }
        },
        "months": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassAssessmentsBySections": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "sections": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subjects": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "ClassDetails": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "classes": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "students": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    },
    "ClassMonthlyAssessmentsBySubject": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "subjects": {
          "type": "string"
        }
      }
    },
    "ClassStudentsBySections": {
      "type": "object",
      "properties": {
        "sections": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "students": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    },
    "ClassTopPerformingStudent": {
      "type": "object",
      "properties": {
        "highestScore": {
          "type": "number",
          "format": "float"
        },
        "lowestScore": {
          "type": "number",
          "format": "float"
        },
        "overallPerformance": {
          "type": "number",
          "format": "float"
        },
        "student": {
          "type": "string"
        }
      }
    },
    "ClassTopPerformingStudents": {
      "type": "object",
      "properties": {
        "data": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ClassTopPerformingStudent"
          }
        }
      }
    },
    "EditInstitute": {
      "type": "object",
      "properties": {
        "address": {
          "$ref": "#/definitions/Address"
        },
        "availableSections": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "example": [
            "A",
            "B",
            "C"
          ]
        },
        "name": {
          "type": "string"
        }
      }
    },
    "EditedStudentResponse": {
      "type": "object",
      "properties": {
        "feedback": {
          "type": "string",
          "example": "Improvement needed in so and so area."
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "score": {
          "type": "number",
          "format": "float",
          "example": 5
        },
        "studentResponse": {
          "type": "string",
          "example": "Mitochondria is the powerhouse of the cell."
        }
      }
    },
    "EditedSubmission": {
      "type": "object",
      "properties": {
        "studentResponses": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/EditedStudentResponse"
          }
        }
      }
    },
    "ErrorResponse": {
      "type": "string",
      "example": "Unable"
    },
    "FileList": {
      "type": "object",
      "properties": {
        "files": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "Folder": {
      "type": "object",
      "properties": {
        "createdAt": {
          "type": "string",
          "format": "date-time"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string"
        },
        "userId": {
          "type": "string"
        }
      }
    },
    "GradedSubmissionPerformance": {
      "type": "object",
      "properties": {
        "stats": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/QuestionStats"
          }
        }
      }
    },
    "Institute": {
      "type": "object",
      "properties": {
        "address": {
          "type": "object",
          "$ref": "#/definitions/Address"
        },
        "id": {
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Saraswati Vidya Mandir"
        },
        "program": {
          "type": "string"
        },
        "sectionList": {
          "type": "object",
          "$ref": "#/definitions/SectionList"
        }
      }
    },
    "Instructor": {
      "type": "object",
      "properties": {
        "email": {
          "type": "string"
        },
        "firstName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "role": {
          "description": "0: AdminRole\n1: InstructorRole\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            1,
            2
          ],
          "x-go-const-names": [
            "AdminRole",
            "InstructorRole"
          ]
        }
      }
    },
    "InstructorList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Instructor"
      }
    },
    "MonthlyAssessmentsGraded": {
      "type": "object",
      "properties": {
        "assessments": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "months": {
          "type": "array",
          "items": {
            "type": "string"
          }
        }
      }
    },
    "NewAssignment": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "duration": {
          "type": "integer",
          "format": "int32"
        },
        "folderId": {
          "description": "Optional folder ID if assignment is inside a folder",
          "type": "string"
        },
        "name": {
          "type": "string",
          "example": "Assignment-1"
        },
        "questions": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/Question"
          }
        },
        "sectionList": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "subjectName": {
          "type": "string"
        },
        "totalScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "NewInstitute": {
      "type": "object",
      "properties": {
        "address": {
          "type": "object",
          "$ref": "#/definitions/Address"
        },
        "name": {
          "type": "string",
          "example": "Saraswati Vidya Mandir"
        },
        "program": {
          "type": "string"
        },
        "terms": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/NewTerm"
          }
        }
      }
    },
    "NewTerm": {
      "type": "object",
      "properties": {
        "endDate": {
          "type": "string",
          "format": "date-time"
        },
        "name": {
          "type": "string"
        },
        "startDate": {
          "type": "string",
          "format": "date-time"
        }
      }
    },
    "Question": {
      "type": "object",
      "properties": {
        "question": {
          "type": "string"
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "questionRubric": {
          "type": "string"
        },
        "questionScore": {
          "type": "number",
          "format": "float"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterTopics"
          }
        }
      }
    },
    "QuestionList": {
      "type": "object",
      "properties": {
        "questionList": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/Question"
          }
        }
      }
    },
    "QuestionStats": {
      "type": "object",
      "properties": {
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "stats": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterStats"
          }
        }
      }
    },
    "Role": {
      "type": "object",
      "properties": {
        "instituteId": {
          "type": "string"
        },
        "role": {
          "description": "0: AdminRole\n1: InstructorRole\n2: StudentRole\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "x-go-const-names": [
            "AdminRole",
            "InstructorRole",
            "StudentRole"
          ]
        }
      }
    },
    "RubricInput": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "questions": {
          "type": "object",
          "$ref": "#/definitions/QuestionList"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "RubricUpload": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "filePaths": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "questions": {
          "type": "object",
          "$ref": "#/definitions/QuestionList"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "SectionList": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "StatusHistory": {
      "type": "object",
      "properties": {
        "status": {
          "description": "0: Due\n1: Submitted\n2: Processing\n3: Graded\n4: Failed\n5: Published\n",
          "type": "integer",
          "format": "int32",
          "enum": [
            0,
            1,
            2,
            3,
            4,
            5
          ],
          "x-go-const-names": [
            "SubmissionStatusDue",
            "SubmissionStatusSubmitted",
            "SubmissionStatusProcessing",
            "SubmissionStatusGraded",
            "SubmissionStatusFailed",
            "SubmissionStatusPublished"
          ]
        },
        "timestamp": {
          "type": "string",
          "format": "date-time"
        },
        "updated_by": {
          "type": "string"
        }
      }
    },
    "Student": {
      "type": "object",
      "properties": {
        "class": {
          "type": "integer",
          "format": "int32",
          "example": 10
        },
        "email": {
          "type": "string"
        },
        "firstName": {
          "type": "string"
        },
        "lastName": {
          "type": "string"
        },
        "rollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "section": {
          "type": "string",
          "example": "C"
        },
        "studentId": {
          "type": "string"
        }
      }
    },
    "StudentList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Student"
      }
    },
    "StudentOverallPerformance": {
      "type": "object",
      "properties": {
        "totalAchievedScore": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        },
        "totalAttemptedScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "StudentOverallStats": {
      "type": "object",
      "properties": {
        "averageStudentPerformance": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "StudentPerformance": {
      "type": "object",
      "properties": {
        "assessmentPerformance": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/SubjectAssessmentPerformance"
          }
        },
        "highestPerformance": {
          "type": "number",
          "format": "float"
        },
        "lowestPerformance": {
          "type": "number",
          "format": "float"
        },
        "overallPerformance": {
          "type": "object",
          "$ref": "#/definitions/StudentOverallPerformance"
        }
      }
    },
    "StudentResponse": {
      "type": "object",
      "properties": {
        "feedback": {
          "type": "string",
          "example": "Improvement needed in so and so area."
        },
        "question": {
          "type": "string"
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "questionRubric": {
          "type": "string"
        },
        "questionScore": {
          "type": "integer",
          "format": "int32"
        },
        "score": {
          "type": "number",
          "format": "float",
          "example": 5
        },
        "studentResponse": {
          "type": "string",
          "example": "Mitochondria is the powerhouse of the cell."
        }
      }
    },
    "StudentSubmission": {
      "type": "object",
      "properties": {
        "assignmentId": {
          "type": "string"
        },
        "assignmentName": {
          "type": "string"
        },
        "assignmentScore": {
          "type": "integer",
          "format": "int32"
        },
        "chapterPerformance": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ChaptersWithScore"
          }
        },
        "createdAt": {
          "type": "string",
          "format": "date-time"
        },
        "createdBy": {
          "type": "string"
        },
        "grade": {
          "type": "integer",
          "format": "int32"
        },
        "history": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/StatusHistory"
          }
        },
        "id": {
          "type": "string"
        },
        "imageIds": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "instituteId": {
          "type": "string"
        },
        "missedQuestions": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        },
        "studentEmail": {
          "type": "string"
        },
        "studentFirstName": {
          "type": "string"
        },
        "studentGrade": {
          "type": "integer",
          "format": "int32"
        },
        "studentId": {
          "type": "string"
        },
        "studentLastName": {
          "type": "string"
        },
        "studentResponses": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/StudentSubmissionStudentResponsesItems0"
          }
        },
        "studentRollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "studentSection": {
          "type": "string"
        },
        "subject": {
          "type": "string"
        },
        "termId": {
          "type": "string"
        },
        "totalScore": {
          "type": "number",
          "format": "float"
        },
        "updatedAt": {
          "type": "string",
          "format": "date-time"
        },
        "updatedBy": {
          "type": "string"
        }
      }
    },
    "StudentSubmissionStudentResponsesItems0": {
      "type": "object",
      "properties": {
        "feedback": {
          "type": "string"
        },
        "question": {
          "type": "string"
        },
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "questionScore": {
          "type": "integer",
          "format": "int32"
        },
        "response": {
          "type": "string"
        },
        "rubric": {
          "type": "string"
        },
        "score": {
          "type": "number",
          "format": "float"
        },
        "topics": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/ChapterTopics"
          }
        }
      }
    },
    "SubjectAssessmentPerformance": {
      "type": "object",
      "properties": {
        "subject": {
          "type": "string"
        },
        "totalAchievedScore": {
          "type": "number",
          "format": "float"
        },
        "totalAssignmentsSolved": {
          "type": "integer",
          "format": "int32"
        },
        "totalAttemptedScore": {
          "type": "integer",
          "format": "int32"
        }
      }
    },
    "SubjectList": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "SubjectStats": {
      "type": "object",
      "properties": {
        "chapters": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterStats"
          }
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "Submission": {
      "type": "object",
      "properties": {
        "assignmentId": {
          "type": "string"
        },
        "assignmentName": {
          "type": "string"
        },
        "assignmentScore": {
          "type": "integer",
          "format": "int32"
        },
        "class": {
          "type": "integer",
          "format": "int32"
        },
        "section": {
          "type": "string"
        },
        "status": {
          "type": "string"
        },
        "studentId": {
          "type": "string"
        },
        "studentName": {
          "type": "string"
        },
        "studentRollNumber": {
          "type": "integer",
          "format": "int32"
        },
        "studentScore": {
          "type": "number",
          "format": "float"
        },
        "subject": {
          "type": "string"
        }
      }
    },
    "SubmissionData": {
      "type": "object",
      "required": [
        "studentId",
        "imageUrls"
      ],
      "properties": {
        "imageUrls": {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        "studentId": {
          "type": "string"
        }
      }
    },
    "SubmissionList": {
      "type": "array",
      "items": {
        "$ref": "#/definitions/Submission"
      }
    },
    "SuccessResponse": {
      "type": "object",
      "properties": {
        "id": {
          "type": "string"
        },
        "message": {
          "type": "string"
        }
      }
    },
    "Term": {
      "type": "object",
      "properties": {
        "endDate": {
          "type": "string"
        },
        "name": {
          "type": "string"
        },
        "startDate": {
          "type": "string"
        },
        "termId": {
          "type": "string"
        }
      }
    },
    "TermList": {
      "type": "array",
      "items": {
        "type": "object",
        "$ref": "#/definitions/Term"
      }
    },
    "TopicStats": {
      "type": "object",
      "properties": {
        "score": {
          "type": "integer",
          "format": "int32"
        },
        "topic": {
          "type": "string"
        }
      }
    },
    "TopicsByQuestion": {
      "type": "object",
      "properties": {
        "questionNumber": {
          "type": "integer",
          "format": "int32"
        },
        "topics": {
          "type": "array",
          "items": {
            "type": "object",
            "$ref": "#/definitions/ChapterTopics"
          }
        }
      }
    },
    "TopicsWithScore": {
      "type": "object",
      "properties": {
        "topic": {
          "type": "string"
        },
        "topicScore": {
          "type": "number",
          "format": "float"
        }
      }
    },
    "UserRoles": {
      "type": "object",
      "properties": {
        "roles": {
          "type": "array",
          "items": {
            "$ref": "#/definitions/Role"
          }
        }
      }
    },
    "WeeklyAssessmentsGraded": {
      "type": "object",
      "properties": {
        "weeklyData": {
          "type": "array",
          "items": {
            "type": "integer",
            "format": "int32"
          }
        }
      }
    }
  },
  "securityDefinitions": {
    "eddyowl-okta": {
      "type": "oauth2",
      "flow": "accessCode",
      "authorizationUrl": "https://dev-z8m8ne04to0cvoad.us.auth0.com/authorize?audience=https%3A%2F%2Feddyowl.com%2Fv1%2Fapi",
      "tokenUrl": "https://dev-z8m8ne04to0cvoad.us.auth0.com/oauth/token",
      "scopes": {
        "openid profile email": "openid profile email"
      }
    }
  },
  "tags": [
    {
      "description": "Operations on Institute",
      "name": "institute"
    },
    {
      "description": "Operations on Instructor",
      "name": "instructor"
    },
    {
      "description": "Operations on Academic Term",
      "name": "term"
    },
    {
      "description": "Operations on Student",
      "name": "student"
    },
    {
      "description": "Operations on Assignment",
      "name": "assignment"
    },
    {
      "description": "Operations on Assignment",
      "name": "folder"
    },
    {
      "description": "Automated Operations",
      "name": "auto"
    },
    {
      "description": "Operations on Submission",
      "name": "submission"
    },
    {
      "description": "Operations on user",
      "name": "user"
    },
    {
      "description": "Operations on statistics",
      "name": "stats"
    }
  ]
}`))
}
