package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getStudentByEmailImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	termProvider      data_providers.TermProvider
	tracer            trace.Tracer
}

func NewGetStudentByEmailHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) student.GetStudentByEmailHandler {
	return &getStudentByEmailImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		userRolesProvider: userRolesProvider,
		termProvider:      termProvider,
		tracer:            tracer,
	}
}

func (impl *getStudentByEmailImpl) Handle(params student.GetStudentByEmailParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetStudentByEmailHandler")
	defer span.End()

	// Validate request and check permissions
	if ok, resp := impl.getStudentByEmailValidator(params); !ok {
		return resp
	}

	// Check user roles and institute access
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewGetStudentByEmailForbidden().WithPayload("Unauthorized")
	}

	termId, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return student.NewGetStudentByEmailInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get student by email
	studentEntity, err := impl.studentProvider.GetByEmail(ctx, params.InstituteID, params.Email)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return student.NewGetStudentByEmailNotFound().WithPayload("Student not found")
		}
		log.Error().Err(err).Msg("Failed to get student by email")
		return student.NewGetStudentByEmailInternalServerError().WithPayload("Unable to get Student")
	}

	studentResponse := MapStudentEntityToModel(studentEntity, termId)
	return student.NewGetStudentByEmailOK().WithPayload(studentResponse)
}

func (impl *getStudentByEmailImpl) getStudentByEmailValidator(params student.GetStudentByEmailParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "getStudentByEmailValidator")
	defer span.End()

	if params.InstituteID == constants.EmptyString {
		return false, student.NewGetStudentByEmailBadRequest().WithPayload("Invalid Institute ID")
	}

	if params.Email == constants.EmptyString {
		return false, student.NewGetStudentByEmailBadRequest().WithPayload("Invalid Email")
	}

	// Validate institute exists
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewGetStudentByEmailBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Err(err).Msg("Failed to fetch institute")
		return false, student.NewGetStudentByEmailInternalServerError().WithPayload("Unable to fetch Institute")
	}

	return true, nil
}
