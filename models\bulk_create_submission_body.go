// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// BulkCreateSubmissionBody bulk create submission body
//
// swagger:model BulkCreateSubmissionBody
type BulkCreateSubmissionBody struct {

	// submission data
	SubmissionData []*SubmissionData `json:"submissionData"`
}

// Validate validates this bulk create submission body
func (m *BulkCreateSubmissionBody) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateSubmissionData(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *BulkCreateSubmissionBody) validateSubmissionData(formats strfmt.Registry) error {
	if swag.IsZero(m.SubmissionData) { // not required
		return nil
	}

	for i := 0; i < len(m.SubmissionData); i++ {
		if swag.IsZero(m.SubmissionData[i]) { // not required
			continue
		}

		if m.SubmissionData[i] != nil {
			if err := m.SubmissionData[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("submissionData" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("submissionData" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this bulk create submission body based on the context it is used
func (m *BulkCreateSubmissionBody) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateSubmissionData(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *BulkCreateSubmissionBody) contextValidateSubmissionData(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.SubmissionData); i++ {

		if m.SubmissionData[i] != nil {

			if swag.IsZero(m.SubmissionData[i]) { // not required
				return nil
			}

			if err := m.SubmissionData[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("submissionData" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("submissionData" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *BulkCreateSubmissionBody) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *BulkCreateSubmissionBody) UnmarshalBinary(b []byte) error {
	var res BulkCreateSubmissionBody
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
