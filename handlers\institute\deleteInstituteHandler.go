package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/institute"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type deleteInstituteByIDImpl struct {
	instituteProvider  data_providers.InstituteProvider
	instructorProvider data_providers.InstructorProvider
	termProvider       data_providers.TermProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewDeleteInstituteByIDHandler(
	instituteProvider data_providers.InstituteProvider,
	instructorProvider data_providers.InstructorProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) institute.DeleteInstituteByIDHandler {
	return &deleteInstituteByIDImpl{
		instituteProvider:  instituteProvider,
		instructorProvider: instructorProvider,
		termProvider:       termProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *deleteInstituteByIDImpl) Handle(params institute.DeleteInstituteByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteInstituteByIDHandler")
	defer span.End()

	user := principal.(string)
	if params.InstituteID == constants.EmptyString {
		return institute.NewDeleteInstituteByIDBadRequest().WithPayload("Invalid instituteID")
	}

	// Get all user roles for this institute
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, user, params.InstituteID, []int{constants.AdminRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return institute.NewDeleteInstituteByIDForbidden().WithPayload("Unauthorized")
	}

	// Get all instructors for this institute
	instructors, err := impl.instructorProvider.GetAll(ctx, &params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Str("instituteID", params.InstituteID).Msg("Failed to get instructors for institute")
		return institute.NewDeleteInstituteByIDInternalServerError().WithPayload("Failed to process institute deletion")
	}

	// Delete all instructors
	for _, instructor := range *instructors {
		err = impl.instructorProvider.Delete(ctx, *instructor.ID, user)
		if err != nil {
			log.Error().Err(err).
				Str("instituteID", params.InstituteID).
				Str("instructorID", *instructor.ID).
				Msg("Failed to delete instructor during institute deletion")
			return institute.NewDeleteInstituteByIDInternalServerError().WithPayload("Failed to delete associated instructors")
		}
	}

	// Get all terms for this institute
	terms, err := impl.termProvider.GetAll(ctx, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Str("instituteID", params.InstituteID).Msg("Failed to get terms for institute")
		return institute.NewDeleteInstituteByIDInternalServerError().WithPayload("Failed to process institute deletion")
	}

	// Delete all terms
	for _, term := range *terms {
		err = impl.termProvider.Delete(ctx, *term.ID, params.InstituteID)
		if err != nil {
			log.Error().Err(err).
				Str("instituteID", params.InstituteID).
				Str("termID", *term.ID).
				Msg("Failed to delete term during institute deletion")
			return institute.NewDeleteInstituteByIDInternalServerError().WithPayload("Failed to delete associated terms")
		}
	}

	// Delete the institute
	err = impl.instituteProvider.Delete(ctx, params.InstituteID, user)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete institute")
		if utils.IsNoDocumentFound(err) {
			return institute.NewDeleteInstituteByIDNotFound().WithPayload("Institute Not Found")
		}
		return institute.NewDeleteInstituteByIDInternalServerError().WithPayload("Unable to delete institute")
	}

	return institute.NewDeleteInstituteByIDOK().WithPayload(&models.SuccessResponse{
		ID:      params.InstituteID,
		Message: "Successfully deleted institute and associated instructors and terms",
	})
}
