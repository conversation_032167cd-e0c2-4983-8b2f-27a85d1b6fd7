// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditInstituteOKCode is the HTTP code returned for type EditInstituteOK
const EditInstituteOKCode int = 200

/*
EditInstituteOK Successful operation

swagger:response editInstituteOK
*/
type EditInstituteOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditInstituteOK creates EditInstituteOK with default headers values
func NewEditInstituteOK() *EditInstituteOK {

	return &EditInstituteOK{}
}

// WithPayload adds the payload to the edit institute o k response
func (o *EditInstituteOK) WithPayload(payload *models.SuccessResponse) *EditInstituteOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute o k response
func (o *EditInstituteOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditInstituteBadRequestCode is the HTTP code returned for type EditInstituteBadRequest
const EditInstituteBadRequestCode int = 400

/*
EditInstituteBadRequest Bad Request

swagger:response editInstituteBadRequest
*/
type EditInstituteBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteBadRequest creates EditInstituteBadRequest with default headers values
func NewEditInstituteBadRequest() *EditInstituteBadRequest {

	return &EditInstituteBadRequest{}
}

// WithPayload adds the payload to the edit institute bad request response
func (o *EditInstituteBadRequest) WithPayload(payload models.ErrorResponse) *EditInstituteBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute bad request response
func (o *EditInstituteBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstituteForbiddenCode is the HTTP code returned for type EditInstituteForbidden
const EditInstituteForbiddenCode int = 403

/*
EditInstituteForbidden Forbidden

swagger:response editInstituteForbidden
*/
type EditInstituteForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteForbidden creates EditInstituteForbidden with default headers values
func NewEditInstituteForbidden() *EditInstituteForbidden {

	return &EditInstituteForbidden{}
}

// WithPayload adds the payload to the edit institute forbidden response
func (o *EditInstituteForbidden) WithPayload(payload models.ErrorResponse) *EditInstituteForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute forbidden response
func (o *EditInstituteForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstituteNotFoundCode is the HTTP code returned for type EditInstituteNotFound
const EditInstituteNotFoundCode int = 404

/*
EditInstituteNotFound Not Found

swagger:response editInstituteNotFound
*/
type EditInstituteNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteNotFound creates EditInstituteNotFound with default headers values
func NewEditInstituteNotFound() *EditInstituteNotFound {

	return &EditInstituteNotFound{}
}

// WithPayload adds the payload to the edit institute not found response
func (o *EditInstituteNotFound) WithPayload(payload models.ErrorResponse) *EditInstituteNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute not found response
func (o *EditInstituteNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstituteTooManyRequestsCode is the HTTP code returned for type EditInstituteTooManyRequests
const EditInstituteTooManyRequestsCode int = 429

/*
EditInstituteTooManyRequests Too Many Requests

swagger:response editInstituteTooManyRequests
*/
type EditInstituteTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteTooManyRequests creates EditInstituteTooManyRequests with default headers values
func NewEditInstituteTooManyRequests() *EditInstituteTooManyRequests {

	return &EditInstituteTooManyRequests{}
}

// WithPayload adds the payload to the edit institute too many requests response
func (o *EditInstituteTooManyRequests) WithPayload(payload models.ErrorResponse) *EditInstituteTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute too many requests response
func (o *EditInstituteTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstituteInternalServerErrorCode is the HTTP code returned for type EditInstituteInternalServerError
const EditInstituteInternalServerErrorCode int = 500

/*
EditInstituteInternalServerError Internal Server Error

swagger:response editInstituteInternalServerError
*/
type EditInstituteInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteInternalServerError creates EditInstituteInternalServerError with default headers values
func NewEditInstituteInternalServerError() *EditInstituteInternalServerError {

	return &EditInstituteInternalServerError{}
}

// WithPayload adds the payload to the edit institute internal server error response
func (o *EditInstituteInternalServerError) WithPayload(payload models.ErrorResponse) *EditInstituteInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute internal server error response
func (o *EditInstituteInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditInstituteServiceUnavailableCode is the HTTP code returned for type EditInstituteServiceUnavailable
const EditInstituteServiceUnavailableCode int = 503

/*
EditInstituteServiceUnavailable Service Unvailable

swagger:response editInstituteServiceUnavailable
*/
type EditInstituteServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditInstituteServiceUnavailable creates EditInstituteServiceUnavailable with default headers values
func NewEditInstituteServiceUnavailable() *EditInstituteServiceUnavailable {

	return &EditInstituteServiceUnavailable{}
}

// WithPayload adds the payload to the edit institute service unavailable response
func (o *EditInstituteServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditInstituteServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit institute service unavailable response
func (o *EditInstituteServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditInstituteServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
