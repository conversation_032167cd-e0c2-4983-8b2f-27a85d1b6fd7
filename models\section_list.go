// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
)

// SectionList section list
//
// swagger:model SectionList
type SectionList []string

// Validate validates this section list
func (m SectionList) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this section list based on context it is used
func (m SectionList) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}
