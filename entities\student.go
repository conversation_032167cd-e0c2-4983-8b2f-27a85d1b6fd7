package entities

import (
	"eddyowl-backend/constants"
	"errors"
	"fmt"
	"net/mail"
	"time"

	"github.com/google/uuid"
)

// AcademicHistory struct to track term-wise grades, sections, and roll numbers
type AcademicHistory struct {
	Grade      int        `json:"grade" bson:"grade"`
	Section    *string    `json:"section" bson:"section"`
	RollNumber int        `json:"roll_number" bson:"roll_number"`
	Status     int        `json:"status" bson:"status"` // 1 = Active, 0 = Inactive/Failed
	CreatedAt  *time.Time `json:"created_at" bson:"created_at"`
}

// Student represents a student entity.
type Student struct {
	ID              *string                    `json:"id" bson:"_id"`
	InstituteID     *string                    `json:"institute_id" bson:"institute_id"`
	UserID          *string                    `json:"user_id,omitempty" bson:"user_id,omitempty"` // Optional
	Email           *string                    `json:"email,omitempty" bson:"email,omitempty"`     // Optional
	StudentID       *string                    `json:"student_id" bson:"student_id"`
	FirstName       *string                    `json:"first_name,omitempty" bson:"first_name,omitempty"`
	LastName        *string                    `json:"last_name,omitempty" bson:"last_name,omitempty"`
	AcademicHistory map[string]AcademicHistory `json:"academic_history" bson:"academic_history"`
	CreatedBy       *string                    `json:"created_by" bson:"created_by"`
	CreatedAt       *time.Time                 `json:"created_at" bson:"created_at"`
	UpdatedBy       *string                    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt       *time.Time                 `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy       *string                    `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt       *time.Time                 `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
}

// NewStudent initializes a student with an academic record for the given term
func NewStudent(instituteID, studentID string, email, userID *string, rollNumber, grade int, section, termID, createdBy string) *Student {
	id := uuid.New().String()
	now := time.Now()
	academicHistory := map[string]AcademicHistory{
		termID: {
			Grade:      grade,
			Section:    &section,
			RollNumber: rollNumber,
			Status:     constants.UserStatusInactive,
			CreatedAt:  &now,
		},
	}

	return &Student{
		ID:              &id,
		InstituteID:     &instituteID,
		UserID:          userID, // Optional
		Email:           email,  // Optional
		StudentID:       &studentID,
		AcademicHistory: academicHistory,
		CreatedBy:       &createdBy,
		CreatedAt:       &now,
	}
}

// Validate ensures Student data is correct
func (s *Student) Validate() error {
	if s.InstituteID == nil || *s.InstituteID == "" {
		return errors.New("institute_id is required")
	}
	if s.Email != nil && *s.Email != "" { // Validate email only if provided
		if _, err := mail.ParseAddress(*s.Email); err != nil {
			return fmt.Errorf("invalid email format: %w", err)
		}
	}
	if len(s.AcademicHistory) == 0 { // Check if the map is empty
		return errors.New("student must have at least one academic history record")
	}

	// Validate the latest academic history entry (assuming you want to validate the last entry added)
	var latestRecord AcademicHistory
	for _, v := range s.AcademicHistory {
		latestRecord = v
	}
	if latestRecord.RollNumber == 0 {
		return errors.New("roll_number is required in academic history")
	}
	if latestRecord.Grade < constants.MinSchoolGrade || latestRecord.Grade > constants.MaxSchoolGrade {
		return fmt.Errorf("grade must be between %d and %d; got %d",
			constants.MinSchoolGrade, constants.MaxSchoolGrade, latestRecord.Grade)
	}

	validStatuses := map[int]bool{
		constants.UserStatusInactive: true,
		constants.UserStatusActive:   true,
	}
	if !validStatuses[latestRecord.Status] {
		return fmt.Errorf("invalid student status: %d", latestRecord.Status)
	}

	return nil
}

// AddAcademicRecord appends a new academic record when the student progresses to a new term
func (s *Student) AddAcademicRecord(termID string, rollNumber, grade int, section string, status int) error {
	now := time.Now()

	// Check if the term already exists
	_, exists := s.AcademicHistory[termID]
	if exists {
		return errors.New("academic record for this term already exists")
	}

	newRecord := AcademicHistory{
		Grade:      grade,
		Section:    &section,
		RollNumber: rollNumber,
		Status:     status,
		CreatedAt:  &now,
	}

	s.AcademicHistory[termID] = newRecord
	return nil
}

// EditAcademicRecord edits a record for a specific term provided the term id and other fields.
func (s *Student) EditAcademicRecord(termID string, rollNumber, grade *int, section *string, status *int) error {
	// Check if the term exists
	record, exists := s.AcademicHistory[termID]
	if !exists {
		return errors.New("academic record for this term does not exist")
	}

	// Update the record
	if rollNumber != nil {
		record.RollNumber = *rollNumber
	}
	if grade != nil {
		record.Grade = *grade
	}
	if section != nil {
		record.Section = section
	}
	if status != nil {
		record.Status = *status
	}

	s.AcademicHistory[termID] = record
	return nil
}
