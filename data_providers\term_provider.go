package data_providers

import (
	"context"
	"eddyowl-backend/entities"
	"time"
)

type TermProvider interface {
	Add(ctx context.Context, term *entities.Term) (string, error)
	Get(ctx context.Context, termId string, instituteId string) (*entities.Term, error)
	GetAll(ctx context.Context, instituteId string) (*[]entities.Term, error)
	Edit(ctx context.Context, termId string, instituteId string, term *entities.Term) error
	Delete(ctx context.Context, termId string, instituteId string) error
	GetCurrent(ctx context.Context, instituteId string) (*entities.Term, error)
	IsOverlapDateRange(ctx context.Context, instituteId string, startDate time.Time, endDate time.Time) (bool, error)
}
