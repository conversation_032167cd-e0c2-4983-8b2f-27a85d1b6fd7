package handlers

import (
	"context"
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type bulkCreateSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	assignmentProvider data_providers.AssignmentProvider
	studentProvider    data_providers.StudentProvider
	aiComponent        components.AIComponent
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewBulkCreateSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	assignmentProvider data_providers.AssignmentProvider,
	studentProvider data_providers.StudentProvider,
	aiComponent components.AIComponent,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.BulkCreateSubmissionHandler {
	return &bulkCreateSubmissionImpl{
		submissionProvider: submissionProvider,
		assignmentProvider: assignmentProvider,
		studentProvider:    studentProvider,
		aiComponent:        aiComponent,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

type submissionResult struct {
	studentID string
	success   bool
	error     error
}

func (impl *bulkCreateSubmissionImpl) Handle(params submission.BulkCreateSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : BulkCreateSubmissionHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewBulkCreateSubmissionForbidden().WithPayload("Unauthorized")
	}

	// Validate assignment exists
	_, err = impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return submission.NewBulkCreateSubmissionBadRequest().WithPayload("Invalid Assignment ID")
		}
		return submission.NewBulkCreateSubmissionInternalServerError().WithPayload("Unable to fetch Assignment")
	}

	createdBy := principal.(string)

	// Process submissions synchronously
	successCount := 0
	failureCount := 0
	for _, submissionData := range params.Submissions.SubmissionData {
		result := impl.processSubmission(ctx, params.InstituteID, params.AssignmentID, submissionData, createdBy)
		if result.success {
			successCount++
			log.Info().Str("studentID", result.studentID).Msg("Successfully processed submission")
		} else {
			failureCount++
			log.Error().Str("studentID", result.studentID).Err(result.error).Msg("Failed to process submission")
		}
	}

	// Log final counts after all processing is complete
	log.Info().
		Int("successCount", successCount).
		Int("failureCount", failureCount).
		Str("instituteID", params.InstituteID).
		Str("assignmentID", params.AssignmentID).
		Msg("Bulk submission processing completed")

	return submission.NewBulkCreateSubmissionOK().WithPayload(&models.SuccessResponse{
		Message: "Bulk submission processed successfully",
	})
}

func (impl *bulkCreateSubmissionImpl) processSubmission(
	ctx context.Context,
	instituteID string,
	assignmentID string,
	submissionData *models.SubmissionData,
	createdBy string,
) submissionResult {
	result := submissionResult{studentID: *submissionData.StudentID}

	// Validate student exists
	_, err := impl.studentProvider.Get(ctx, instituteID, *submissionData.StudentID)
	if err != nil {
		result.error = err
		return result
	}

	// Create submission entity
	studentResponses := make([]entities.StudentResponse, 0)
	submissionEntity := entities.NewSubmission(
		instituteID,
		assignmentID,
		*submissionData.StudentID,
		studentResponses,
		createdBy,
		submissionData.ImageUrls,
	)

	// Validate submission
	if err := submissionEntity.Validate(); err != nil {
		result.error = err
		return result
	}

	// Add submission
	_, err = impl.submissionProvider.Add(ctx, submissionEntity)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			result.error = errors.New("submission already exists for student")
			return result
		}
		result.error = err
		return result
	}

	// Synchronous grading request
	err = impl.aiComponent.GradeAssignment(ctx, instituteID, assignmentID, *submissionData.StudentID)
	if err != nil {
		log.Error().Err(err).
			Str("instituteID", instituteID).
			Str("assignmentID", assignmentID).
			Str("studentID", *submissionData.StudentID).
			Msg("Grading failed")

		now := time.Now()
		newHistory := entities.StatusHistory{
			Status:    constants.SubmissionStatusFailed,
			Timestamp: &now,
			UpdatedBy: &createdBy,
		}

		if submissionEntity.History == nil {
			submissionEntity.History = &[]entities.StatusHistory{}
		}

		updatedHistory := append(*submissionEntity.History, newHistory)
		submissionEntity.History = &updatedHistory

		_, updateErr := impl.submissionProvider.Edit(ctx, instituteID, assignmentID, *submissionData.StudentID, submissionEntity)
		if updateErr != nil {
			log.Error().Err(updateErr).Msg("Failed to update submission status")
		}
		result.error = err
		return result
	}

	result.success = true
	return result
}
