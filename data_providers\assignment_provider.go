package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type AssignmentProvider interface {
	Add(ctx context.Context, assignment *entities.Assignment) (string, error)
	Get(ctx context.Context, assignmentId string, instituteId string) (*entities.Assignment, error)
	GetAll(ctx context.Context, instituteId string, termId *string, grade *int, sections *[]string, subject *string) (*[]entities.Assignment, error)
	Edit(ctx context.Context, assignmentId string, instituteId string, assignment *entities.Assignment) error
	Delete(ctx context.Context, assignmentId string, instituteId string, deletedBy string) error
}
