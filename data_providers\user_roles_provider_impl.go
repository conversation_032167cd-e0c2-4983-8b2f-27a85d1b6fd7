package data_providers

import (
    "context"
    "eddyowl-backend/constants"
    "eddyowl-backend/entities"

    "github.com/rs/zerolog/log"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "go.opentelemetry.io/otel/trace"
)

type userRolesProvider struct {
    mongoClient *mongo.Client
    dbName      string
    tracer      trace.Tracer
}

func NewUserRolesProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) UserRolesProvider {
    return &userRolesProvider{
        mongoClient: mongoClient,
        dbName:      databaseName,
        tracer:      tracer,
    }
}

func (p *userRolesProvider) GetByEmail(ctx context.Context, email string) (*entities.UserRoleView, error) {
    ctx, span := p.tracer.Start(ctx, "UserRolesProvider : GetByEmail")
    defer span.End()

    userRoles := &entities.UserRoleView{}
    filter := bson.D{{"email", email}}
    
    err := p.mongoClient.Database(p.dbName).
        Collection(constants.MongoDBViewUserRoles).
        FindOne(ctx, filter).
        Decode(userRoles)
    
    if err != nil {
        log.Error().Err(err).Msg("Failed to fetch user roles")
        return nil, err
    }

    return userRoles, nil
}