db.createView(
  "grade_subject_submission_data_view", // view name
  "institutes", // base collection
  [
    // Step 1: Lookup all terms
    {
      $lookup: {
        from: "terms",
        localField: "_id",
        foreignField: "institute_id",
        as: "terms",
      },
    },
    { $unwind: "$terms" },

    // Step 2: Add grades
    {
      $addFields: {
        grades: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      },
    },
    { $unwind: "$grades" },

    // Step 3: Lookup subjects from topics
    {
      $lookup: {
        from: "topics",
        let: { grade: "$grades" },
        pipeline: [
          { $match: { $expr: { $eq: ["$grade", "$$grade"] } } },
          { $project: { subject: 1, _id: 0 } },
        ],
        as: "topics_for_grade",
      },
    },
    { $unwind: "$topics_for_grade" },

    // Step 4: Project base structure
    {
      $project: {
        institute_id: "$_id",
        term_id: "$terms._id",
        grade: "$grades",
        subject: "$topics_for_grade.subject",
      },
    },

    // Step 5: Lookup assignments
    {
      $lookup: {
        from: "assignments",
        let: {
          inst_id: "$institute_id",
          t_id: "$term_id",
          g: "$grade",
          s: "$subject",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$institute_id", "$$inst_id"] },
                  { $eq: ["$term_id", "$$t_id"] },
                  { $eq: ["$grade", "$$g"] },
                  { $eq: ["$subject", "$$s"] },
                ],
              },
            },
          },
          {
            $project: {
              _id: 1,
              total_score: 1,
            },
          },
        ],
        as: "matched_assignments",
      },
    },

    // Step 6: Prepare assignment IDs and total scores
    {
      $addFields: {
        assignment_ids: {
          $map: {
            input: "$matched_assignments",
            as: "asmt",
            in: "$$asmt._id",
          },
        },
        assignment_scores_map: {
          $arrayToObject: {
            $map: {
              input: "$matched_assignments",
              as: "asmt",
              in: {
                k: { $toString: "$$asmt._id" },
                v: "$$asmt.total_score",
              },
            },
          },
        },
        assignment_count: { $size: "$matched_assignments" },
      },
    },

    // Step 7: Lookup submissions
    {
      $lookup: {
        from: "submissions",
        let: {
          inst_id: "$institute_id",
          asmt_ids: "$assignment_ids",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$institute_id", "$$inst_id"] },
                  { $in: ["$assignment_id", "$$asmt_ids"] },
                ],
              },
            },
          },
          {
            $project: {
              assignment_id: 1,
              total_achieved_score: 1,
            },
          },
        ],
        as: "matched_submissions",
      },
    },

    // Step 8: Calculate average percentage
    {
      $addFields: {
        submission_count: { $size: "$matched_submissions" },
        average_submission_percentage: {
          $cond: [
            { $gt: [{ $size: "$matched_submissions" }, 0] },
            {
              $avg: {
                $map: {
                  input: "$matched_submissions",
                  as: "sub",
                  in: {
                    $cond: [
                      {
                        $gt: [
                          {
                            $ifNull: [
                              {
                                $toDouble: {
                                  $getField: {
                                    field: { $toString: "$$sub.assignment_id" },
                                    input: "$assignment_scores_map",
                                  },
                                },
                              },
                              0,
                            ],
                          },
                          0,
                        ],
                      },
                      {
                        $multiply: [
                          {
                            $divide: [
                              "$$sub.total_achieved_score",
                              {
                                $ifNull: [
                                  {
                                    $toDouble: {
                                      $getField: {
                                        field: {
                                          $toString: "$$sub.assignment_id",
                                        },
                                        input: "$assignment_scores_map",
                                      },
                                    },
                                  },
                                  1,
                                ],
                              },
                            ],
                          },
                          100,
                        ],
                      },
                      0,
                    ],
                  },
                },
              },
            },
            null,
          ],
        },
      },
    },

    // Final projection
    {
      $project: {
        _id: 0,
        institute_id: 1,
        term_id: 1,
        grade: 1,
        subject: 1,
        assignment_count: 1,
        submission_count: 1,
        average_submission_percentage: {
          $round: ["$average_submission_percentage", 2],
        },
      },
    },
  ]
);
