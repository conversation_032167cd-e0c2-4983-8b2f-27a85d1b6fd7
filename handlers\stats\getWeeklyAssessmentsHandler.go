package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getWeeklyAssessmentsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetWeeklyAssessmentsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetWeeklyAssessmentsHandler {
	return &getWeeklyAssessmentsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getWeeklyAssessmentsImpl) Handle(params stats.GetWeeklyAssessmentsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetWeeklyAssessmentsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetWeeklyAssessmentsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get current date
	currentDate := time.Now()
	weeklyData := make([]int32, 7)

	// Iterate through last 7 days
	for i := 6; i >= 0; i-- {
		date := currentDate.AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")

		// Get submissions count for the date
		result, err := impl.statsProvider.GetDailySubmissionsCount(ctx, params.InstituteID, termID, dateStr)
		if err != nil {
			// If no data found for the date, set count to 0
			weeklyData[6-i] = 0
			continue
		}

		// If data found, add the count
		if result != nil {
			weeklyData[6-i] = int32(result.SubmissionCount)
		} else {
			weeklyData[6-i] = 0
		}
	}

	response := &models.WeeklyAssessmentsGraded{
		WeeklyData: weeklyData,
	}

	return stats.NewGetWeeklyAssessmentsOK().WithPayload(response)
}
