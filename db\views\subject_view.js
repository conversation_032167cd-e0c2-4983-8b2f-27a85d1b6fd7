db.createView("subjects", "topics", [
  // 1) Group by `program` and `grade`.
  {
    $group: {
      _id: {
        program: "$program",
        grade: "$grade",
      },
      // Collect all distinct subjects into an array.
      subjects: { $addToSet: "$subject" },
    },
  },
  // 2) Project the fields to match the desired structure.
  {
    $project: {
      // Combine grade and program (or any custom logic you prefer).
      _id: {
        $concat: [{ $toString: "$_id.grade" }, "-", "$_id.program"],
      },
      subjects: 1,
      program: "$_id.program",
      grade: "$_id.grade",
    },
  },
]);
