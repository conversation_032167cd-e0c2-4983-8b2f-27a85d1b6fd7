// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// PublishSubmissionOKCode is the HTTP code returned for type PublishSubmissionOK
const PublishSubmissionOKCode int = 200

/*
PublishSubmissionOK Successful operation

swagger:response publishSubmissionOK
*/
type PublishSubmissionOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewPublishSubmissionOK creates PublishSubmissionOK with default headers values
func NewPublishSubmissionOK() *PublishSubmissionOK {

	return &PublishSubmissionOK{}
}

// WithPayload adds the payload to the publish submission o k response
func (o *PublishSubmissionOK) WithPayload(payload *models.SuccessResponse) *PublishSubmissionOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission o k response
func (o *PublishSubmissionOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// PublishSubmissionBadRequestCode is the HTTP code returned for type PublishSubmissionBadRequest
const PublishSubmissionBadRequestCode int = 400

/*
PublishSubmissionBadRequest Bad Request

swagger:response publishSubmissionBadRequest
*/
type PublishSubmissionBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionBadRequest creates PublishSubmissionBadRequest with default headers values
func NewPublishSubmissionBadRequest() *PublishSubmissionBadRequest {

	return &PublishSubmissionBadRequest{}
}

// WithPayload adds the payload to the publish submission bad request response
func (o *PublishSubmissionBadRequest) WithPayload(payload models.ErrorResponse) *PublishSubmissionBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission bad request response
func (o *PublishSubmissionBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishSubmissionForbiddenCode is the HTTP code returned for type PublishSubmissionForbidden
const PublishSubmissionForbiddenCode int = 403

/*
PublishSubmissionForbidden Forbidden

swagger:response publishSubmissionForbidden
*/
type PublishSubmissionForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionForbidden creates PublishSubmissionForbidden with default headers values
func NewPublishSubmissionForbidden() *PublishSubmissionForbidden {

	return &PublishSubmissionForbidden{}
}

// WithPayload adds the payload to the publish submission forbidden response
func (o *PublishSubmissionForbidden) WithPayload(payload models.ErrorResponse) *PublishSubmissionForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission forbidden response
func (o *PublishSubmissionForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishSubmissionNotFoundCode is the HTTP code returned for type PublishSubmissionNotFound
const PublishSubmissionNotFoundCode int = 404

/*
PublishSubmissionNotFound Not Found

swagger:response publishSubmissionNotFound
*/
type PublishSubmissionNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionNotFound creates PublishSubmissionNotFound with default headers values
func NewPublishSubmissionNotFound() *PublishSubmissionNotFound {

	return &PublishSubmissionNotFound{}
}

// WithPayload adds the payload to the publish submission not found response
func (o *PublishSubmissionNotFound) WithPayload(payload models.ErrorResponse) *PublishSubmissionNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission not found response
func (o *PublishSubmissionNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishSubmissionTooManyRequestsCode is the HTTP code returned for type PublishSubmissionTooManyRequests
const PublishSubmissionTooManyRequestsCode int = 429

/*
PublishSubmissionTooManyRequests Too Many Requests

swagger:response publishSubmissionTooManyRequests
*/
type PublishSubmissionTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionTooManyRequests creates PublishSubmissionTooManyRequests with default headers values
func NewPublishSubmissionTooManyRequests() *PublishSubmissionTooManyRequests {

	return &PublishSubmissionTooManyRequests{}
}

// WithPayload adds the payload to the publish submission too many requests response
func (o *PublishSubmissionTooManyRequests) WithPayload(payload models.ErrorResponse) *PublishSubmissionTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission too many requests response
func (o *PublishSubmissionTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishSubmissionInternalServerErrorCode is the HTTP code returned for type PublishSubmissionInternalServerError
const PublishSubmissionInternalServerErrorCode int = 500

/*
PublishSubmissionInternalServerError Internal Server Error

swagger:response publishSubmissionInternalServerError
*/
type PublishSubmissionInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionInternalServerError creates PublishSubmissionInternalServerError with default headers values
func NewPublishSubmissionInternalServerError() *PublishSubmissionInternalServerError {

	return &PublishSubmissionInternalServerError{}
}

// WithPayload adds the payload to the publish submission internal server error response
func (o *PublishSubmissionInternalServerError) WithPayload(payload models.ErrorResponse) *PublishSubmissionInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission internal server error response
func (o *PublishSubmissionInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishSubmissionServiceUnavailableCode is the HTTP code returned for type PublishSubmissionServiceUnavailable
const PublishSubmissionServiceUnavailableCode int = 503

/*
PublishSubmissionServiceUnavailable Service Unvailable

swagger:response publishSubmissionServiceUnavailable
*/
type PublishSubmissionServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishSubmissionServiceUnavailable creates PublishSubmissionServiceUnavailable with default headers values
func NewPublishSubmissionServiceUnavailable() *PublishSubmissionServiceUnavailable {

	return &PublishSubmissionServiceUnavailable{}
}

// WithPayload adds the payload to the publish submission service unavailable response
func (o *PublishSubmissionServiceUnavailable) WithPayload(payload models.ErrorResponse) *PublishSubmissionServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish submission service unavailable response
func (o *PublishSubmissionServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishSubmissionServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
