// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// WeeklyAssessmentsGraded weekly assessments graded
//
// swagger:model WeeklyAssessmentsGraded
type WeeklyAssessmentsGraded struct {

	// weekly data
	WeeklyData []int32 `json:"weeklyData"`
}

// Validate validates this weekly assessments graded
func (m *WeeklyAssessmentsGraded) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this weekly assessments graded based on context it is used
func (m *WeeklyAssessmentsGraded) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *WeeklyAssessmentsGraded) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *WeeklyAssessmentsGraded) UnmarshalBinary(b []byte) error {
	var res WeeklyAssessmentsGraded
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
