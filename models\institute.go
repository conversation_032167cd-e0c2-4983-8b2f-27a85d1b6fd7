// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Institute institute
//
// swagger:model Institute
type Institute struct {

	// address
	Address *Address `json:"address,omitempty"`

	// id
	ID string `json:"id,omitempty"`

	// name
	// Example: <PERSON><PERSON><PERSON> Vidya Mandir
	Name string `json:"name,omitempty"`

	// program
	Program string `json:"program,omitempty"`

	// section list
	SectionList SectionList `json:"sectionList,omitempty"`
}

// Validate validates this institute
func (m *Institute) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateAddress(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateSectionList(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Institute) validateAddress(formats strfmt.Registry) error {
	if swag.IsZero(m.Address) { // not required
		return nil
	}

	if m.Address != nil {
		if err := m.Address.Validate(formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("address")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("address")
			}
			return err
		}
	}

	return nil
}

func (m *Institute) validateSectionList(formats strfmt.Registry) error {
	if swag.IsZero(m.SectionList) { // not required
		return nil
	}

	if err := m.SectionList.Validate(formats); err != nil {
		if ve, ok := err.(*errors.Validation); ok {
			return ve.ValidateName("sectionList")
		} else if ce, ok := err.(*errors.CompositeError); ok {
			return ce.ValidateName("sectionList")
		}
		return err
	}

	return nil
}

// ContextValidate validate this institute based on the context it is used
func (m *Institute) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateAddress(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateSectionList(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *Institute) contextValidateAddress(ctx context.Context, formats strfmt.Registry) error {

	if m.Address != nil {

		if swag.IsZero(m.Address) { // not required
			return nil
		}

		if err := m.Address.ContextValidate(ctx, formats); err != nil {
			if ve, ok := err.(*errors.Validation); ok {
				return ve.ValidateName("address")
			} else if ce, ok := err.(*errors.CompositeError); ok {
				return ce.ValidateName("address")
			}
			return err
		}
	}

	return nil
}

func (m *Institute) contextValidateSectionList(ctx context.Context, formats strfmt.Registry) error {

	if err := m.SectionList.ContextValidate(ctx, formats); err != nil {
		if ve, ok := err.(*errors.Validation); ok {
			return ve.ValidateName("sectionList")
		} else if ce, ok := err.(*errors.CompositeError); ok {
			return ce.ValidateName("sectionList")
		}
		return err
	}

	return nil
}

// MarshalBinary interface implementation
func (m *Institute) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Institute) UnmarshalBinary(b []byte) error {
	var res Institute
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
