// Code generated by go-swagger; DO NOT EDIT.

package subject

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetSubjectsOKCode is the HTTP code returned for type GetSubjectsOK
const GetSubjectsOKCode int = 200

/*
GetSubjectsOK Successful operation

swagger:response getSubjectsOK
*/
type GetSubjectsOK struct {

	/*
	  In: Body
	*/
	Payload models.SubjectList `json:"body,omitempty"`
}

// NewGetSubjectsOK creates GetSubjectsOK with default headers values
func NewGetSubjectsOK() *GetSubjectsOK {

	return &GetSubjectsOK{}
}

// WithPayload adds the payload to the get subjects o k response
func (o *GetSubjectsOK) WithPayload(payload models.SubjectList) *GetSubjectsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects o k response
func (o *GetSubjectsOK) SetPayload(payload models.SubjectList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.SubjectList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsBadRequestCode is the HTTP code returned for type GetSubjectsBadRequest
const GetSubjectsBadRequestCode int = 400

/*
GetSubjectsBadRequest Bad Request

swagger:response getSubjectsBadRequest
*/
type GetSubjectsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsBadRequest creates GetSubjectsBadRequest with default headers values
func NewGetSubjectsBadRequest() *GetSubjectsBadRequest {

	return &GetSubjectsBadRequest{}
}

// WithPayload adds the payload to the get subjects bad request response
func (o *GetSubjectsBadRequest) WithPayload(payload models.ErrorResponse) *GetSubjectsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects bad request response
func (o *GetSubjectsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsUnauthorizedCode is the HTTP code returned for type GetSubjectsUnauthorized
const GetSubjectsUnauthorizedCode int = 401

/*
GetSubjectsUnauthorized Unauthorized

swagger:response getSubjectsUnauthorized
*/
type GetSubjectsUnauthorized struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsUnauthorized creates GetSubjectsUnauthorized with default headers values
func NewGetSubjectsUnauthorized() *GetSubjectsUnauthorized {

	return &GetSubjectsUnauthorized{}
}

// WithPayload adds the payload to the get subjects unauthorized response
func (o *GetSubjectsUnauthorized) WithPayload(payload models.ErrorResponse) *GetSubjectsUnauthorized {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects unauthorized response
func (o *GetSubjectsUnauthorized) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsUnauthorized) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(401)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsForbiddenCode is the HTTP code returned for type GetSubjectsForbidden
const GetSubjectsForbiddenCode int = 403

/*
GetSubjectsForbidden Forbidden

swagger:response getSubjectsForbidden
*/
type GetSubjectsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsForbidden creates GetSubjectsForbidden with default headers values
func NewGetSubjectsForbidden() *GetSubjectsForbidden {

	return &GetSubjectsForbidden{}
}

// WithPayload adds the payload to the get subjects forbidden response
func (o *GetSubjectsForbidden) WithPayload(payload models.ErrorResponse) *GetSubjectsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects forbidden response
func (o *GetSubjectsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsNotFoundCode is the HTTP code returned for type GetSubjectsNotFound
const GetSubjectsNotFoundCode int = 404

/*
GetSubjectsNotFound Not Found

swagger:response getSubjectsNotFound
*/
type GetSubjectsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsNotFound creates GetSubjectsNotFound with default headers values
func NewGetSubjectsNotFound() *GetSubjectsNotFound {

	return &GetSubjectsNotFound{}
}

// WithPayload adds the payload to the get subjects not found response
func (o *GetSubjectsNotFound) WithPayload(payload models.ErrorResponse) *GetSubjectsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects not found response
func (o *GetSubjectsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsTooManyRequestsCode is the HTTP code returned for type GetSubjectsTooManyRequests
const GetSubjectsTooManyRequestsCode int = 429

/*
GetSubjectsTooManyRequests Too Many Requests

swagger:response getSubjectsTooManyRequests
*/
type GetSubjectsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsTooManyRequests creates GetSubjectsTooManyRequests with default headers values
func NewGetSubjectsTooManyRequests() *GetSubjectsTooManyRequests {

	return &GetSubjectsTooManyRequests{}
}

// WithPayload adds the payload to the get subjects too many requests response
func (o *GetSubjectsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetSubjectsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects too many requests response
func (o *GetSubjectsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsInternalServerErrorCode is the HTTP code returned for type GetSubjectsInternalServerError
const GetSubjectsInternalServerErrorCode int = 500

/*
GetSubjectsInternalServerError Internal Server Error

swagger:response getSubjectsInternalServerError
*/
type GetSubjectsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsInternalServerError creates GetSubjectsInternalServerError with default headers values
func NewGetSubjectsInternalServerError() *GetSubjectsInternalServerError {

	return &GetSubjectsInternalServerError{}
}

// WithPayload adds the payload to the get subjects internal server error response
func (o *GetSubjectsInternalServerError) WithPayload(payload models.ErrorResponse) *GetSubjectsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects internal server error response
func (o *GetSubjectsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetSubjectsServiceUnavailableCode is the HTTP code returned for type GetSubjectsServiceUnavailable
const GetSubjectsServiceUnavailableCode int = 503

/*
GetSubjectsServiceUnavailable Service Unvailable

swagger:response getSubjectsServiceUnavailable
*/
type GetSubjectsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetSubjectsServiceUnavailable creates GetSubjectsServiceUnavailable with default headers values
func NewGetSubjectsServiceUnavailable() *GetSubjectsServiceUnavailable {

	return &GetSubjectsServiceUnavailable{}
}

// WithPayload adds the payload to the get subjects service unavailable response
func (o *GetSubjectsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetSubjectsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get subjects service unavailable response
func (o *GetSubjectsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetSubjectsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
