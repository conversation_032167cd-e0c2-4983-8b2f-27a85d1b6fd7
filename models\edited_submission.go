// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// EditedSubmission edited submission
//
// swagger:model EditedSubmission
type EditedSubmission struct {

	// student responses
	StudentResponses []*EditedStudentResponse `json:"studentResponses"`
}

// Validate validates this edited submission
func (m *EditedSubmission) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateStudentResponses(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *EditedSubmission) validateStudentResponses(formats strfmt.Registry) error {
	if swag.IsZero(m.StudentResponses) { // not required
		return nil
	}

	for i := 0; i < len(m.StudentResponses); i++ {
		if swag.IsZero(m.StudentResponses[i]) { // not required
			continue
		}

		if m.StudentResponses[i] != nil {
			if err := m.StudentResponses[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this edited submission based on the context it is used
func (m *EditedSubmission) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateStudentResponses(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *EditedSubmission) contextValidateStudentResponses(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.StudentResponses); i++ {

		if m.StudentResponses[i] != nil {

			if swag.IsZero(m.StudentResponses[i]) { // not required
				return nil
			}

			if err := m.StudentResponses[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *EditedSubmission) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *EditedSubmission) UnmarshalBinary(b []byte) error {
	var res EditedSubmission
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
