// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// PublishAllSubmissionsHandlerFunc turns a function with the right signature into a publish all submissions handler
type PublishAllSubmissionsHandlerFunc func(PublishAllSubmissionsParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn PublishAllSubmissionsHandlerFunc) Handle(params PublishAllSubmissionsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// PublishAllSubmissionsHandler interface for that can handle valid publish all submissions params
type PublishAllSubmissionsHandler interface {
	Handle(PublishAllSubmissionsParams, interface{}) middleware.Responder
}

// NewPublishAllSubmissions creates a new http.Handler for the publish all submissions operation
func NewPublishAllSubmissions(ctx *middleware.Context, handler PublishAllSub<PERSON><PERSON><PERSON><PERSON><PERSON>) *PublishAllSubmissions {
	return &PublishAllSubmissions{Context: ctx, Handler: handler}
}

/*
	PublishAllSubmissions swagger:route PUT /institute/{instituteId}/assignment/{assignmentId}/publish submission publishAllSubmissions

# Publish all submissions

Publish all submissions
*/
type PublishAllSubmissions struct {
	Context *middleware.Context
	Handler PublishAllSubmissionsHandler
}

func (o *PublishAllSubmissions) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewPublishAllSubmissionsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
