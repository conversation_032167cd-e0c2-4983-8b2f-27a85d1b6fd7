// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetSubmissionHandlerFunc turns a function with the right signature into a get submission handler
type GetSubmissionHandlerFunc func(GetSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetSubmissionHandlerFunc) Handle(params GetSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetSubmissionHandler interface for that can handle valid get submission params
type GetSubmissionHandler interface {
	Handle(GetSubmissionParams, interface{}) middleware.Responder
}

// NewGetSubmission creates a new http.Handler for the get submission operation
func NewGetSubmission(ctx *middleware.Context, handler GetSubmissionHandler) *GetSubmission {
	return &GetSubmission{Context: ctx, Handler: handler}
}

/*
	GetSubmission swagger:route GET /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission submission getSubmission

# Get graded student submission

Get graded submission
*/
type GetSubmission struct {
	Context *middleware.Context
	Handler GetSubmissionHandler
}

func (o *GetSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
