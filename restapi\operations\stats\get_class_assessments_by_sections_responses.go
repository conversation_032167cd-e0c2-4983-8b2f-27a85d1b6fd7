// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassAssessmentsBySectionsOKCode is the HTTP code returned for type GetClassAssessmentsBySectionsOK
const GetClassAssessmentsBySectionsOKCode int = 200

/*
GetClassAssessmentsBySectionsOK Successful operation

swagger:response getClassAssessmentsBySectionsOK
*/
type GetClassAssessmentsBySectionsOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassAssessmentsBySections `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsOK creates GetClassAssessmentsBySectionsOK with default headers values
func NewGetClassAssessmentsBySectionsOK() *GetClassAssessmentsBySectionsOK {

	return &GetClassAssessmentsBySectionsOK{}
}

// WithPayload adds the payload to the get class assessments by sections o k response
func (o *GetClassAssessmentsBySectionsOK) WithPayload(payload *models.ClassAssessmentsBySections) *GetClassAssessmentsBySectionsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections o k response
func (o *GetClassAssessmentsBySectionsOK) SetPayload(payload *models.ClassAssessmentsBySections) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassAssessmentsBySectionsBadRequestCode is the HTTP code returned for type GetClassAssessmentsBySectionsBadRequest
const GetClassAssessmentsBySectionsBadRequestCode int = 400

/*
GetClassAssessmentsBySectionsBadRequest Bad Request

swagger:response getClassAssessmentsBySectionsBadRequest
*/
type GetClassAssessmentsBySectionsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsBadRequest creates GetClassAssessmentsBySectionsBadRequest with default headers values
func NewGetClassAssessmentsBySectionsBadRequest() *GetClassAssessmentsBySectionsBadRequest {

	return &GetClassAssessmentsBySectionsBadRequest{}
}

// WithPayload adds the payload to the get class assessments by sections bad request response
func (o *GetClassAssessmentsBySectionsBadRequest) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections bad request response
func (o *GetClassAssessmentsBySectionsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySectionsForbiddenCode is the HTTP code returned for type GetClassAssessmentsBySectionsForbidden
const GetClassAssessmentsBySectionsForbiddenCode int = 403

/*
GetClassAssessmentsBySectionsForbidden Forbidden

swagger:response getClassAssessmentsBySectionsForbidden
*/
type GetClassAssessmentsBySectionsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsForbidden creates GetClassAssessmentsBySectionsForbidden with default headers values
func NewGetClassAssessmentsBySectionsForbidden() *GetClassAssessmentsBySectionsForbidden {

	return &GetClassAssessmentsBySectionsForbidden{}
}

// WithPayload adds the payload to the get class assessments by sections forbidden response
func (o *GetClassAssessmentsBySectionsForbidden) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections forbidden response
func (o *GetClassAssessmentsBySectionsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySectionsNotFoundCode is the HTTP code returned for type GetClassAssessmentsBySectionsNotFound
const GetClassAssessmentsBySectionsNotFoundCode int = 404

/*
GetClassAssessmentsBySectionsNotFound Not Found

swagger:response getClassAssessmentsBySectionsNotFound
*/
type GetClassAssessmentsBySectionsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsNotFound creates GetClassAssessmentsBySectionsNotFound with default headers values
func NewGetClassAssessmentsBySectionsNotFound() *GetClassAssessmentsBySectionsNotFound {

	return &GetClassAssessmentsBySectionsNotFound{}
}

// WithPayload adds the payload to the get class assessments by sections not found response
func (o *GetClassAssessmentsBySectionsNotFound) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections not found response
func (o *GetClassAssessmentsBySectionsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySectionsTooManyRequestsCode is the HTTP code returned for type GetClassAssessmentsBySectionsTooManyRequests
const GetClassAssessmentsBySectionsTooManyRequestsCode int = 429

/*
GetClassAssessmentsBySectionsTooManyRequests Too Many Requests

swagger:response getClassAssessmentsBySectionsTooManyRequests
*/
type GetClassAssessmentsBySectionsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsTooManyRequests creates GetClassAssessmentsBySectionsTooManyRequests with default headers values
func NewGetClassAssessmentsBySectionsTooManyRequests() *GetClassAssessmentsBySectionsTooManyRequests {

	return &GetClassAssessmentsBySectionsTooManyRequests{}
}

// WithPayload adds the payload to the get class assessments by sections too many requests response
func (o *GetClassAssessmentsBySectionsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections too many requests response
func (o *GetClassAssessmentsBySectionsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySectionsInternalServerErrorCode is the HTTP code returned for type GetClassAssessmentsBySectionsInternalServerError
const GetClassAssessmentsBySectionsInternalServerErrorCode int = 500

/*
GetClassAssessmentsBySectionsInternalServerError Internal Server Error

swagger:response getClassAssessmentsBySectionsInternalServerError
*/
type GetClassAssessmentsBySectionsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsInternalServerError creates GetClassAssessmentsBySectionsInternalServerError with default headers values
func NewGetClassAssessmentsBySectionsInternalServerError() *GetClassAssessmentsBySectionsInternalServerError {

	return &GetClassAssessmentsBySectionsInternalServerError{}
}

// WithPayload adds the payload to the get class assessments by sections internal server error response
func (o *GetClassAssessmentsBySectionsInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections internal server error response
func (o *GetClassAssessmentsBySectionsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySectionsServiceUnavailableCode is the HTTP code returned for type GetClassAssessmentsBySectionsServiceUnavailable
const GetClassAssessmentsBySectionsServiceUnavailableCode int = 503

/*
GetClassAssessmentsBySectionsServiceUnavailable Service Unvailable

swagger:response getClassAssessmentsBySectionsServiceUnavailable
*/
type GetClassAssessmentsBySectionsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySectionsServiceUnavailable creates GetClassAssessmentsBySectionsServiceUnavailable with default headers values
func NewGetClassAssessmentsBySectionsServiceUnavailable() *GetClassAssessmentsBySectionsServiceUnavailable {

	return &GetClassAssessmentsBySectionsServiceUnavailable{}
}

// WithPayload adds the payload to the get class assessments by sections service unavailable response
func (o *GetClassAssessmentsBySectionsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySectionsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by sections service unavailable response
func (o *GetClassAssessmentsBySectionsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySectionsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
