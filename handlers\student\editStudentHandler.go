package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type editStudentImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	termProvider      data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewEditStudentHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) student.EditStudentHandler {
	return &editStudentImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		termProvider:      termProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *editStudentImpl) Handle(params student.EditStudentParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : EditStudentHandler")
	defer span.End()

	principal = principal.(string)

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewEditStudentForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.editStudentValidator(params)
	if !valid {
		return validateResp
	}
	updatedBy := principal.(string)

	termId := constants.EmptyString
	if params.TermID != nil {
		term, err := impl.termProvider.Get(ctx, *params.TermID, params.InstituteID)
		if err != nil {
			log.Error().Msg(err.Error())
		}
		if term != nil {
			termId = *term.ID
		}
	} else {
		currentTerm, err := impl.termProvider.GetCurrent(ctx, params.InstituteID)
		if err != (nil) {
			log.Error().Msg(err.Error())
		}
		if currentTerm != nil {
			termId = *currentTerm.ID
		}
	}
	if termId == constants.EmptyString {
		return student.NewCreateNewStudentInternalServerError().WithPayload("Unable to fetch Term")
	}

	studentEntity, err := impl.studentProvider.Get(ctx, params.InstituteID, params.Student.StudentID)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewEditStudentInternalServerError().WithPayload("Unable to fetch Student")
	}
	if params.Student.Email != constants.EmptyString {
		studentEntity.Email = &params.Student.Email
	}
	if params.Student.FirstName != constants.EmptyString {
		studentEntity.FirstName = &params.Student.FirstName
	}
	if params.Student.LastName != constants.EmptyString {
		studentEntity.LastName = &params.Student.LastName
	}

	newRollNumber := studentEntity.AcademicHistory[termId].RollNumber
	newGrade := studentEntity.AcademicHistory[termId].Grade
	newSection := studentEntity.AcademicHistory[termId].Section
	if params.Student.RollNumber != 0 {
		newRollNumber = int(params.Student.RollNumber)
	}
	if params.Student.Class != 0 {
		newGrade = int(params.Student.Class)
	}
	if params.Student.Section != constants.EmptyString {
		newSection = &params.Student.Section
	}

	err = studentEntity.EditAcademicRecord(termId, &newRollNumber, &newGrade, newSection, nil)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewCreateNewStudentBadRequest().WithPayload("Term Not Found")
	}
	studentEntity.StudentID = &params.Student.StudentID
	studentEntity.UpdatedBy = &updatedBy
	err = studentEntity.Validate()
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewEditStudentBadRequest().WithPayload("Invalid Parameters")
	}

	err = impl.studentProvider.Edit(ctx, params.InstituteID, params.Student.StudentID, studentEntity)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewEditStudentInternalServerError().WithPayload("Unable to edit Student")
	}

	return student.NewEditStudentOK().WithPayload(
		&models.SuccessResponse{
			ID:      params.Student.StudentID,
			Message: "Successfully edited Student",
		},
	)
}

func (impl *editStudentImpl) editStudentValidator(params student.EditStudentParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "editStudentValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, student.NewEditStudentBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewEditStudentBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, student.NewEditStudentInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.Student.StudentID == constants.EmptyString {
		return false, student.NewEditStudentBadRequest().WithPayload("Invalid Student ID")
	}
	if params.Student == nil {
		return false, student.NewEditStudentBadRequest().WithPayload("Invalid Student Parameters")
	}
	if params.Student.RollNumber == 0 && params.Student.Class == 0 && params.Student.Section == constants.EmptyString && params.Student.Email == constants.EmptyString && params.Student.FirstName == constants.EmptyString && params.Student.LastName == constants.EmptyString {
		return false, student.NewEditStudentBadRequest().WithPayload("Invalid Student Parameters")
	}

	return true, nil
}
