package entities

type OktaUserInfoResponse struct {
	Sub                 string          `json:"sub"`
	Name                string          `json:"name"`
	GivenName           string          `json:"given_name"`
	FamilyName          string          `json:"family_name"`
	MiddleName          string          `json:"middle_name"`
	Nickname            string          `json:"nickname"`
	PreferredUsername   string          `json:"preferred_username"`
	Profile             string          `json:"profile"`
	Picture             string          `json:"picture"`
	Website             string          `json:"website"`
	Email               string          `json:"email"`
	EmailVerified       bool            `json:"email_verified"`
	Gender              string          `json:"gender"`
	Birthdate           string          `json:"birthdate"`
	Zoneinfo            string          `json:"zoneinfo"`
	Locale              string          `json:"locale"`
	PhoneNumber         string          `json:"phone_number"`
	PhoneNumberVerified bool            `json:"phone_number_verified"`
	Address             AuthUserAddress `json:"address"`
	UpdatedAt           string          `json:"updated_at"`
}

type AuthUserAddress struct {
	Country string `json:"country"`
}
