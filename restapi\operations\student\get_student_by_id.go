// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetStudentByIDHandlerFunc turns a function with the right signature into a get student by Id handler
type GetStudentByIDHandlerFunc func(GetStudentByIDParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetStudentByIDHandlerFunc) Handle(params GetStudentByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetStudentByIDHandler interface for that can handle valid get student by Id params
type GetStudentByIDHandler interface {
	Handle(GetStudentByIDParams, interface{}) middleware.Responder
}

// NewGetStudentByID creates a new http.Handler for the get student by Id operation
func NewGetStudentByID(ctx *middleware.Context, handler GetStudentByIDHandler) *GetStudentByID {
	return &GetStudentByID{Context: ctx, Handler: handler}
}

/*
	GetStudentByID swagger:route GET /institute/{instituteId}/student/{studentId} student getStudentById

# Get student

Get student by School Student Id
*/
type GetStudentByID struct {
	Context *middleware.Context
	Handler GetStudentByIDHandler
}

func (o *GetStudentByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetStudentByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
