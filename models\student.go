// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// Student student
//
// swagger:model Student
type Student struct {

	// class
	// Example: 10
	Class int32 `json:"class,omitempty"`

	// email
	Email string `json:"email,omitempty"`

	// first name
	FirstName string `json:"firstName,omitempty"`

	// last name
	LastName string `json:"lastName,omitempty"`

	// roll number
	RollNumber int32 `json:"rollNumber,omitempty"`

	// section
	// Example: C
	Section string `json:"section,omitempty"`

	// student Id
	StudentID string `json:"studentId,omitempty"`
}

// Validate validates this student
func (m *Student) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this student based on context it is used
func (m *Student) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Student) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Student) UnmarshalBinary(b []byte) error {
	var res Student
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
