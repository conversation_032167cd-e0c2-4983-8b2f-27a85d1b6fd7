package entities

import "errors"

var (
	ErrPincodeEmpty    = errors.New("pincode cannot be empty")
	ErrCityEmpty       = errors.New("city cannot be empty")
	ErrStateEmpty      = errors.New("state cannot be empty")
	ErrAddressOneEmpty = errors.New("address one cannot be empty")
)

type Address struct {
	AddressOne *string `json:"address_one,omitempty" bson:"address_one,omitempty"`
	AddressTwo *string `json:"address_two,omitempty" bson:"address_two,omitempty"`
	City       *string `json:"city,omitempty" bson:"city,omitempty"`
	Pincode    *string `json:"pincode,omitempty" bson:"pincode,omitempty"`
	State      *string `json:"state,omitempty" bson:"state,omitempty"`
	Country    *string `json:"country,omitempty" bson:"country,omitempty"`
}

func NewAddress(addressOne, addressTwo, city, state, pincode string) *Address {
	countryName := "India"
	return &Address{
		AddressOne: &addressOne,
		AddressTwo: &addressTwo,
		City:       &city,
		Pincode:    &pincode,
		State:      &state,
		Country:    &countryName,
	}
}

func (a *Address) Validate() error {
	if a.AddressOne == nil || *a.AddressOne == "" {
		return ErrAddressOneEmpty
	}
	if a.Pincode == nil || *a.Pincode == "" {
		return ErrPincodeEmpty
	}
	if a.City == nil || *a.City == "" {
		return ErrCityEmpty
	}
	if a.State == nil || *a.State == "" {
		return ErrStateEmpty
	}

	return nil
}
