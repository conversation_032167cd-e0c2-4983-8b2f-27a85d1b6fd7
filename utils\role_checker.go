package utils

import (
	"context"
	"eddyowl-backend/data_providers"
	"errors"
)

var (
	ErrUnauthorizedRole      = errors.New("unauthorized role")
	ErrUnauthorizedInstitute = errors.New("unauthorized institute access")
)

// CheckUserRoleAndInstitute checks if the user has one of the allowed roles and access to the specified institute
func CheckUserRoleAndInstitute(ctx context.Context, userRolesProvider data_providers.UserRolesProvider, email string, instituteID string, allowedRoles []int) error {
	userRoles, err := userRolesProvider.GetByEmail(ctx, email)
	if err != nil {
		return err
	}

	hasRole := false
	hasInstitute := false

	for _, userRole := range userRoles.Roles {
		hasRole = false
		hasInstitute = false

		// Check if user has any of the allowed roles
		for _, allowedRole := range allowedRoles {
			if userRole.Role == allowedRole {
				hasRole = true
			}
		}

		// Check if user has access to the institute
		if userRole.InstituteID == instituteID {
			hasInstitute = true
		}

		// Early return if both conditions are met
		if hasRole && hasInstitute {
			return nil
		}
	}

	return ErrUnauthorizedRole
}
