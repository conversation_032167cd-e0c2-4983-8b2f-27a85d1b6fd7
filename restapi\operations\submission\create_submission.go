// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// CreateSubmissionHandlerFunc turns a function with the right signature into a create submission handler
type CreateSubmissionHandlerFunc func(CreateSubmissionParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn CreateSubmissionHandlerFunc) Handle(params CreateSubmissionParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateSubmissionHandler interface for that can handle valid create submission params
type CreateSubmissionHandler interface {
	Handle(CreateSubmissionParams, interface{}) middleware.Responder
}

// NewCreateSubmission creates a new http.Handler for the create submission operation
func NewCreateSubmission(ctx *middleware.Context, handler CreateSubmissionHandler) *CreateSubmission {
	return &CreateSubmission{Context: ctx, Handler: handler}
}

/*
	CreateSubmission swagger:route PUT /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission submission createSubmission

# Upload student solutions

Upload solutions to S3
*/
type CreateSubmission struct {
	Context *middleware.Context
	Handler CreateSubmissionHandler
}

func (o *CreateSubmission) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateSubmissionParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
