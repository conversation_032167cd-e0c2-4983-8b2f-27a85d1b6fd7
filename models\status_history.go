// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// StatusHistory status history
//
// swagger:model StatusHistory
type StatusHistory struct {

	// 0: Due
	// 1: Submitted
	// 2: Processing
	// 3: Graded
	// 4: Failed
	// 5: Published
	//
	// Enum: [0,1,2,3,4,5]
	Status int32 `json:"status,omitempty"`

	// timestamp
	// Format: date-time
	Timestamp strfmt.DateTime `json:"timestamp,omitempty"`

	// updated by
	UpdatedBy string `json:"updated_by,omitempty"`
}

// Validate validates this status history
func (m *StatusHistory) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateStatus(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateTimestamp(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

var statusHistoryTypeStatusPropEnum []interface{}

func init() {
	var res []int32
	if err := json.Unmarshal([]byte(`[0,1,2,3,4,5]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		statusHistoryTypeStatusPropEnum = append(statusHistoryTypeStatusPropEnum, v)
	}
}

// prop value enum
func (m *StatusHistory) validateStatusEnum(path, location string, value int32) error {
	if err := validate.EnumCase(path, location, value, statusHistoryTypeStatusPropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *StatusHistory) validateStatus(formats strfmt.Registry) error {
	if swag.IsZero(m.Status) { // not required
		return nil
	}

	// value enum
	if err := m.validateStatusEnum("status", "body", m.Status); err != nil {
		return err
	}

	return nil
}

func (m *StatusHistory) validateTimestamp(formats strfmt.Registry) error {
	if swag.IsZero(m.Timestamp) { // not required
		return nil
	}

	if err := validate.FormatOf("timestamp", "body", "date-time", m.Timestamp.String(), formats); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this status history based on context it is used
func (m *StatusHistory) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *StatusHistory) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StatusHistory) UnmarshalBinary(b []byte) error {
	var res StatusHistory
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
