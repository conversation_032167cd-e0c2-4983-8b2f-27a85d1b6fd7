// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// QuestionList question list
//
// swagger:model QuestionList
type QuestionList struct {

	// question list
	QuestionList []*Question `json:"questionList"`
}

// Validate validates this question list
func (m *QuestionList) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateQuestionList(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *QuestionList) validateQuestionList(formats strfmt.Registry) error {
	if swag.IsZero(m.QuestionList) { // not required
		return nil
	}

	for i := 0; i < len(m.QuestionList); i++ {
		if swag.Is<PERSON>ero(m.QuestionList[i]) { // not required
			continue
		}

		if m.QuestionList[i] != nil {
			if err := m.QuestionList[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("questionList" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("questionList" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this question list based on the context it is used
func (m *QuestionList) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateQuestionList(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *QuestionList) contextValidateQuestionList(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.QuestionList); i++ {

		if m.QuestionList[i] != nil {

			if swag.IsZero(m.QuestionList[i]) { // not required
				return nil
			}

			if err := m.QuestionList[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("questionList" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("questionList" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *QuestionList) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *QuestionList) UnmarshalBinary(b []byte) error {
	var res QuestionList
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
