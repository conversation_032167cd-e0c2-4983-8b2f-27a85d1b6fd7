package handlers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/assignment"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

// getAllAssignmentImpl implements the GetAllAssignmentsHandler interface.
type getAllAssignmentImpl struct {
	assignmentProvider data_providers.AssignmentProvider
	instituteProvider  data_providers.InstituteProvider
	termProvider       data_providers.TermProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

// NewGetAllAssignmentHandler returns a handler for fetching all assignments.
func NewGetAllAssignmentHandler(
	assignmentProvider data_providers.AssignmentProvider,
	instituteProvider data_providers.InstituteProvider,
	termProvider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) assignment.GetAllAssignmentsHandler {
	return &getAllAssignmentImpl{
		assignmentProvider: assignmentProvider,
		instituteProvider:  instituteProvider,
		termProvider:       termProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

// Handle processes the GetAllAssignments request.
func (impl *getAllAssignmentImpl) Handle(params assignment.GetAllAssignmentsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetAllAssignmentsHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return assignment.NewGetAllAssignmentsForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.getAllAssignmentValidator(ctx, params)
	if !valid {
		return validateResp
	}

	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, params.TermID)
	if err != nil {
		if errors.Is(err, utils.ErrTermNotFound) {
			return assignment.NewGetAllAssignmentsBadRequest().WithPayload("Term not found")
		}
		return assignment.NewGetAllAssignmentsInternalServerError().WithPayload("Unable to resolve term")
	}

	var grade *int
	if params.Grade != nil {
		gradeVal := int(*params.Grade)
		grade = &gradeVal
	}

	var sections *[]string
	if len(params.Section) > 0 {
		sections = &params.Section
	}

	allAssignments, err := impl.assignmentProvider.GetAll(ctx, params.InstituteID, &termID, grade, sections, params.Subject)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch assignments")
		if errors.Is(err, mongo.ErrNoDocuments) {
			return assignment.NewGetAllAssignmentsNotFound().WithPayload("No assignments found")
		}
		return assignment.NewGetAllAssignmentsInternalServerError().WithPayload("Failed to fetch assignments")
	}

	// Map entities.Assignment to models.Assignment (excluding Questions field)
	var response []*models.Assignment
	for _, a := range *allAssignments {
		response = append(response, &models.Assignment{
			ID:          utils.GetString(a.ID),
			Name:        utils.GetString(a.Name),
			Class:       int32(a.Grade),
			SubjectName: utils.GetString(a.Subject),
			SectionList: utils.GetStringSlice(a.Sections),
			// Questions field is NOT populated
			TotalScore: (a.TotalScore),
		})
	}
	return assignment.NewGetAllAssignmentsOK().WithPayload(response)
}

func (impl *getAllAssignmentImpl) getAllAssignmentValidator(ctx context.Context, params assignment.GetAllAssignmentsParams) (bool, middleware.Responder) {
	if params.InstituteID == "" {
		log.Error().Msg("Invalid institute ID")
		return false, assignment.NewGetAllAssignmentsBadRequest().WithPayload("Invalid institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, assignment.NewGetAllAssignmentsBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, assignment.NewGetAllAssignmentsInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.Grade != nil {
		if *params.Grade < 1 || *params.Grade > 12 {
			return false, assignment.NewGetAllAssignmentsBadRequest().WithPayload("Invalid Grade")
		}
	}
	return true, nil
}
