db.createView("institute_all_stats_view", "terms", [
  {
    $lookup: {
      from: "institutes",
      localField: "institute_id",
      foreignField: "_id",
      as: "institute",
    },
  },
  {
    $unwind: "$institute",
  },
  {
    $lookup: {
      from: "instructors",
      localField: "institute_id",
      foreignField: "institute_id",
      as: "instructors",
    },
  },
  {
    $lookup: {
      from: "students",
      localField: "institute_id",
      foreignField: "institute_id",
      as: "students",
    },
  },
  {
    $lookup: {
      from: "submissions_view",
      let: {
        inst_id: "$institute_id",
        term_id: "$_id",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$institute_id", "$$inst_id"],
                },
                {
                  $eq: ["$term_id", "$$term_id"],
                },
              ],
            },
          },
        },
      ],
      as: "submissions",
    },
  },
  {
    $project: {
      _id: 0,
      institute_id: "$institute_id",
      term_id: "$_id",
      instructors: {
        $size: "$instructors",
      },
      students: {
        $size: {
          $filter: {
            input: "$students",
            as: "stu",
            cond: {
              $ne: [
                {
                  $getField: {
                    field: {
                      $toString: "$_id",
                    },
                    input: "$$stu.academic_history",
                  },
                },
                null,
              ],
            },
          },
        },
      },
      submissions_count: {
        $size: {
          $filter: {
            input: "$submissions",
            as: "s",
            cond: {
              $ne: [
                {
                  $type: "$$s.history",
                },
                "missing",
              ],
            },
          },
        },
      },
      average_percentage_score: {
        $cond: [
          {
            $eq: [
              {
                $size: "$submissions",
              },
              0,
            ],
          },
          0,
          {
            $avg: {
              $map: {
                input: {
                  $filter: {
                    input: "$submissions",
                    as: "s",
                    cond: {
                      $ne: ["$$s.total_achieved_score", null],
                    },
                  },
                },
                as: "sub",
                in: {
                  $multiply: [
                    {
                      $divide: [
                        "$$sub.total_achieved_score",
                        "$$sub.total_score",
                      ],
                    },
                    100,
                  ],
                },
              },
            },
          },
        ],
      },
      assessments: {
        $size: {
          $ifNull: [
            {
              $setUnion: [
                {
                  $map: {
                    input: "$submissions",
                    as: "sub",
                    in: "$$sub.assignment_id",
                  },
                },
                [],
              ],
            },
            [],
          ],
        },
      },
    },
  },
]);
