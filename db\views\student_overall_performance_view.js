db.createView("student_overall_percentage_view", "submissions_view", [
  {
    $match: {
      total_achieved_score: { $ne: null },
      assignment_id: { $ne: null },
    },
  },

  // Lookup assignments to get total_score
  {
    $lookup: {
      from: "assignments",
      localField: "assignment_id",
      foreignField: "_id",
      as: "assignment",
    },
  },
  { $unwind: "$assignment" },

  // Calculate percentage
  {
    $addFields: {
      submission_percentage: {
        $cond: [
          { $gt: ["$assignment.total_score", 0] },
          {
            $multiply: [
              { $divide: ["$total_achieved_score", "$assignment.total_score"] },
              100,
            ],
          },
          null,
        ],
      },
    },
  },

  // Lookup student to get grade (from academic_history by term_id)
  {
    $lookup: {
      from: "students",
      let: {
        inst_id: "$institute_id",
        stud_id: "$student_id",
        term_id: "$term_id",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$institute_id", "$$inst_id"] },
                { $eq: ["$student_id", "$$stud_id"] },
              ],
            },
          },
        },
        {
          $addFields: {
            academic_info: { $objectToArray: "$academic_history" },
          },
        },
        { $unwind: "$academic_info" },
        {
          $match: {
            $expr: {
              $eq: ["$academic_info.k", "$$term_id"],
            },
          },
        },
        {
          $project: {
            grade: "$academic_info.v.grade",
          },
        },
      ],
      as: "student_info",
    },
  },
  { $unwind: "$student_info" },

  // Group by student + term + institute
  {
    $group: {
      _id: {
        institute_id: "$institute_id",
        term_id: "$term_id",
        student_id: "$student_id",
      },
      grade: { $first: "$student_info.grade" },
      total_submission_count: { $sum: 1 },
      average_submission_percentage: { $avg: "$submission_percentage" },
      highest_submission_percentage: { $max: "$submission_percentage" },
      lowest_submission_percentage: { $min: "$submission_percentage" },
    },
  },

  // Final projection
  {
    $project: {
      _id: 1,
      grade: 1,
      total_submission_count: 1,
      average_submission_percentage: {
        $round: ["$average_submission_percentage", 2],
      },
      highest_submission_percentage: {
        $round: ["$highest_submission_percentage", 2],
      },
      lowest_submission_percentage: {
        $round: ["$lowest_submission_percentage", 2],
      },
    },
  },
]);
