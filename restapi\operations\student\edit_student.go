// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// EditStudentHandlerFunc turns a function with the right signature into a edit student handler
type EditStudentHandlerFunc func(EditStudentParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn EditStudentHandlerFunc) Handle(params EditStudentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// EditStudentHandler interface for that can handle valid edit student params
type EditStudentHandler interface {
	Handle(EditStudentParams, interface{}) middleware.Responder
}

// NewEditStudent creates a new http.Handler for the edit student operation
func NewEditStudent(ctx *middleware.Context, handler EditStudentHandler) *EditStudent {
	return &EditStudent{Context: ctx, Handler: handler}
}

/*
	EditStudent swagger:route PUT /institute/{instituteId}/student student editStudent

# Edit student

Edit student
*/
type EditStudent struct {
	Context *middleware.Context
	Handler EditStudentHandler
}

func (o *EditStudent) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewEditStudentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
