package utils

import (
	"context"
	"eddyowl-backend/entities"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/MicahParks/keyfunc"
	"github.com/golang-jwt/jwt/v4"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type TokenInfoProvider interface {
	ValidateToken(ctx context.Context, accessToken string, requiredScope string) (string, error)
	GetUserInfo(ctx context.Context, accessToken string) (*entities.OktaUserInfoResponse, error)
}

type tokenInfoProviderImpl struct {
	issuer string
	tracer trace.Tracer
	jwks   *keyfunc.JWKS
}

func NewTokenInfoProvider(issuer string, tracer trace.Tracer) (TokenInfoProvider, error) {
	jwksURL := fmt.Sprintf("https://%s/.well-known/jwks.json", issuer)
	jwks, err := keyfunc.Get(jwksURL, keyfunc.Options{
		RefreshInterval: time.Hour, // Automatically refresh keys every hour
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create JWKS from %q: %v", jwksURL, err)
	}

	return &tokenInfoProviderImpl{issuer: issuer, tracer: tracer, jwks: jwks}, nil
}

func (impl *tokenInfoProviderImpl) GetUserInfo(ctx context.Context, accessToken string) (*entities.OktaUserInfoResponse, error) {
	_, span := impl.tracer.Start(ctx, "TokenInfoProvider : GetUserInfo")
	defer span.End()
	// Replace these with your actual domain and access token

	// Construct the URL
	url := fmt.Sprintf("https://%s/userinfo", impl.issuer)

	// Create a new HTTP client
	client := http.Client{}

	// Create a new GET request with the authorization header
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error().Err(err).Msg("failed to create new request")
		return nil, err
	}
	req.Header.Add("Authorization", "Bearer "+accessToken)

	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("failed to send request")
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("unexpected status code: %d", resp.StatusCode)
		log.Error().Err(err).Msg("received non-OK status code")
		return nil, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error().Err(err).Msg("failed to read response body")
		return nil, err
	}

	var claims entities.OktaUserInfoResponse
	// Decode the JSON response into UserInfoResponse struct
	err = json.Unmarshal(body, &claims)
	if err != nil {
		log.Error().Err(err).Msg("failed to unmarshal response body")
		return nil, err
	}

	return &claims, nil
}

// ValidateToken and get email claim
func (impl *tokenInfoProviderImpl) ValidateToken(ctx context.Context, accessToken string, requiredScope string) (string, error) {
	_, span := impl.tracer.Start(ctx, "TokenInfoProvider : ValidateToken")
	defer span.End()
	// Parse the JWT token using the JWKS
	parsed, err := jwt.Parse(accessToken, impl.jwks.Keyfunc)
	if err != nil {
		return "", fmt.Errorf("invalid token: %w", err)
	}
	if !parsed.Valid {
		return "", errors.New("token is not valid")
	}

	// Extract claims
	claims, ok := parsed.Claims.(jwt.MapClaims)
	if !ok {
		return "", errors.New("could not parse claims")
	}

	// Validate issuer
	if claims["iss"] != nil {
		if iss, _ := claims["iss"].(string); iss != fmt.Sprintf("https://%s/", impl.issuer) {
			return "", fmt.Errorf("invalid issuer: %s", iss)
		}
	} else {
		return "", errors.New("invalid issuer")
	}

	if claims["aud"] != nil {
		// Validate scopes
		scopeVals := reflect.ValueOf(claims["aud"])
		found := false
		for i := 0; i < scopeVals.Len(); i++ {
			scopeStr := fmt.Sprint(scopeVals.Index(i))
			if scopeStr == requiredScope {
				found = true
				break
			}
		}
		if !found {
			log.Error().Msg("invalid scope out of scopes")
			return "", errors.New("invalid scope")
		}
	} else {
		log.Error().Msg("No Scope")
		return "", errors.New("invalid scope")
	}

	if claims["email"] != nil {
		return claims["email"].(string), nil
	} else {
		return "", errors.New("invalid email")
	}
}

func GetTokenFromRequest(request *http.Request) (string, error) {
	authHeader := request.Header.Get("Authorization")
	authHeaderParts := strings.Split(authHeader, " ")
	if len(authHeaderParts) < 2 {
		return "", errors.New("invalid or missing Authorization header")
	}
	return authHeaderParts[1], nil
}
