// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// AddInstructorByInstituteIDHandlerFunc turns a function with the right signature into a add instructor by institute Id handler
type AddInstructorByInstituteIDHandlerFunc func(AddInstructorByInstituteIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn AddInstructorByInstituteIDHandlerFunc) Handle(params AddInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// AddInstructorByInstituteIDHandler interface for that can handle valid add instructor by institute Id params
type AddInstructorByInstituteIDHandler interface {
	Handle(AddInstructorByInstituteIDParams, interface{}) middleware.Responder
}

// NewAddInstructorByInstituteID creates a new http.Handler for the add instructor by institute Id operation
func NewAddInstructorByInstituteID(ctx *middleware.Context, handler AddInstructorByInstituteIDHandler) *AddInstructorByInstituteID {
	return &AddInstructorByInstituteID{Context: ctx, Handler: handler}
}

/*
	AddInstructorByInstituteID swagger:route POST /institute/{instituteId}/instructor instructor addInstructorByInstituteId

# Add instructor

Add instructor by institute id
*/
type AddInstructorByInstituteID struct {
	Context *middleware.Context
	Handler AddInstructorByInstituteIDHandler
}

func (o *AddInstructorByInstituteID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewAddInstructorByInstituteIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
