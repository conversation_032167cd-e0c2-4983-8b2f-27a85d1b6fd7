package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassStudentsBySectionsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassStudentsBySectionsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassStudentsBySectionsHandler {
	return &getClassStudentsBySectionsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassStudentsBySectionsImpl) Handle(params stats.GetClassStudentsBySectionsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassStudentsBySectionsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassStudentsBySectionsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get all sections data
	result, err := impl.statsProvider.GetAllSectionData(ctx, params.InstituteID, termID, params.Class)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get sections data")
		return stats.NewGetClassStudentsBySectionsInternalServerError().WithPayload("Unable to get sections data")
	}

	// Filter results for the specified grade and organize data
	response := &models.ClassStudentsBySections{
		Sections: make([]string, 0),
		Students: make([]int32, 0),
	}

	for _, section := range *result {
		if section.ID.Grade == params.Class {
			response.Sections = append(response.Sections, section.ID.Section)
			response.Students = append(response.Students, section.StudentCount)
		}
	}

	return stats.NewGetClassStudentsBySectionsOK().WithPayload(response)
}
