package entities

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

type User struct {
	ID        *string    `json:"id" bson:"_id"`
	Email     *string    `json:"email" bson:"email"`
	FirstName *string    `json:"first_name" bson:"first_name"`
	LastName  *string    `json:"last_name" bson:"last_name"`
	CreatedBy *string    `json:"created_by" bson:"created_by"`
	CreatedAt *time.Time `json:"created_at" bson:"created_at"`
	UpdatedBy *string    `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	DeletedBy *string    `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
	IsDeleted bool       `json:"is_deleted" bson:"is_deleted"`
}

func NewUser(email *string, firstName *string, lastName *string, createdBy string) *User {
	now := time.Now()
	id := uuid.New().String()
	return &User{
		ID:        &id,
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		CreatedBy: &createdBy,
		CreatedAt: &now,
	}
}

func (i *User) Validate() error {
	if i.ID == nil || *i.ID == "" {
		return errors.New("id cannot be empty")
	}
	if i.Email == nil || *i.Email == "" {
		return errors.New("email cannot be empty")
	}
	if i.FirstName == nil || *i.FirstName == "" {
		return errors.New("first name cannot be empty")
	}
	if i.LastName == nil || *i.LastName == "" {
		return errors.New("last name cannot be empty")
	}
	return nil
}
