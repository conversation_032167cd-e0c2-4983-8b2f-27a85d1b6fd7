// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllStudentsOKCode is the HTTP code returned for type GetAllStudentsOK
const GetAllStudentsOKCode int = 200

/*
GetAllStudentsOK Successful operation

swagger:response getAllStudentsOK
*/
type GetAllStudentsOK struct {

	/*
	  In: Body
	*/
	Payload models.StudentList `json:"body,omitempty"`
}

// NewGetAllStudentsOK creates GetAllStudentsOK with default headers values
func NewGetAllStudentsOK() *GetAllStudentsOK {

	return &GetAllStudentsOK{}
}

// WithPayload adds the payload to the get all students o k response
func (o *GetAllStudentsOK) WithPayload(payload models.StudentList) *GetAllStudentsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students o k response
func (o *GetAllStudentsOK) SetPayload(payload models.StudentList) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	payload := o.Payload
	if payload == nil {
		// return empty array
		payload = models.StudentList{}
	}

	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsBadRequestCode is the HTTP code returned for type GetAllStudentsBadRequest
const GetAllStudentsBadRequestCode int = 400

/*
GetAllStudentsBadRequest Bad Request

swagger:response getAllStudentsBadRequest
*/
type GetAllStudentsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsBadRequest creates GetAllStudentsBadRequest with default headers values
func NewGetAllStudentsBadRequest() *GetAllStudentsBadRequest {

	return &GetAllStudentsBadRequest{}
}

// WithPayload adds the payload to the get all students bad request response
func (o *GetAllStudentsBadRequest) WithPayload(payload models.ErrorResponse) *GetAllStudentsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students bad request response
func (o *GetAllStudentsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsForbiddenCode is the HTTP code returned for type GetAllStudentsForbidden
const GetAllStudentsForbiddenCode int = 403

/*
GetAllStudentsForbidden Forbidden

swagger:response getAllStudentsForbidden
*/
type GetAllStudentsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsForbidden creates GetAllStudentsForbidden with default headers values
func NewGetAllStudentsForbidden() *GetAllStudentsForbidden {

	return &GetAllStudentsForbidden{}
}

// WithPayload adds the payload to the get all students forbidden response
func (o *GetAllStudentsForbidden) WithPayload(payload models.ErrorResponse) *GetAllStudentsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students forbidden response
func (o *GetAllStudentsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsNotFoundCode is the HTTP code returned for type GetAllStudentsNotFound
const GetAllStudentsNotFoundCode int = 404

/*
GetAllStudentsNotFound Not Found

swagger:response getAllStudentsNotFound
*/
type GetAllStudentsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsNotFound creates GetAllStudentsNotFound with default headers values
func NewGetAllStudentsNotFound() *GetAllStudentsNotFound {

	return &GetAllStudentsNotFound{}
}

// WithPayload adds the payload to the get all students not found response
func (o *GetAllStudentsNotFound) WithPayload(payload models.ErrorResponse) *GetAllStudentsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students not found response
func (o *GetAllStudentsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsTooManyRequestsCode is the HTTP code returned for type GetAllStudentsTooManyRequests
const GetAllStudentsTooManyRequestsCode int = 429

/*
GetAllStudentsTooManyRequests Too Many Requests

swagger:response getAllStudentsTooManyRequests
*/
type GetAllStudentsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsTooManyRequests creates GetAllStudentsTooManyRequests with default headers values
func NewGetAllStudentsTooManyRequests() *GetAllStudentsTooManyRequests {

	return &GetAllStudentsTooManyRequests{}
}

// WithPayload adds the payload to the get all students too many requests response
func (o *GetAllStudentsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAllStudentsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students too many requests response
func (o *GetAllStudentsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsInternalServerErrorCode is the HTTP code returned for type GetAllStudentsInternalServerError
const GetAllStudentsInternalServerErrorCode int = 500

/*
GetAllStudentsInternalServerError Internal Server Error

swagger:response getAllStudentsInternalServerError
*/
type GetAllStudentsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsInternalServerError creates GetAllStudentsInternalServerError with default headers values
func NewGetAllStudentsInternalServerError() *GetAllStudentsInternalServerError {

	return &GetAllStudentsInternalServerError{}
}

// WithPayload adds the payload to the get all students internal server error response
func (o *GetAllStudentsInternalServerError) WithPayload(payload models.ErrorResponse) *GetAllStudentsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students internal server error response
func (o *GetAllStudentsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStudentsServiceUnavailableCode is the HTTP code returned for type GetAllStudentsServiceUnavailable
const GetAllStudentsServiceUnavailableCode int = 503

/*
GetAllStudentsServiceUnavailable Service Unvailable

swagger:response getAllStudentsServiceUnavailable
*/
type GetAllStudentsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStudentsServiceUnavailable creates GetAllStudentsServiceUnavailable with default headers values
func NewGetAllStudentsServiceUnavailable() *GetAllStudentsServiceUnavailable {

	return &GetAllStudentsServiceUnavailable{}
}

// WithPayload adds the payload to the get all students service unavailable response
func (o *GetAllStudentsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAllStudentsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all students service unavailable response
func (o *GetAllStudentsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStudentsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
