// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// MonthlyAssessmentsGraded monthly assessments graded
//
// swagger:model MonthlyAssessmentsGraded
type MonthlyAssessmentsGraded struct {

	// assessments
	Assessments []int32 `json:"assessments"`

	// months
	Months []string `json:"months"`
}

// Validate validates this monthly assessments graded
func (m *MonthlyAssessmentsGraded) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this monthly assessments graded based on context it is used
func (m *MonthlyAssessmentsGraded) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *MonthlyAssessmentsGraded) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *MonthlyAssessmentsGraded) UnmarshalBinary(b []byte) error {
	var res MonthlyAssessmentsGraded
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
