package entities

import (
	"eddyowl-backend/constants"
	"errors"
	"time"

	"github.com/google/uuid"
)

type Submission struct {
	ID               *string            `json:"id,omitempty" bson:"_id,omitempty"`
	InstituteID      *string            `json:"institute_id,omitempty" bson:"institute_id,omitempty"`
	AssignmentID     *string            `json:"assignment_id,omitempty" bson:"assignment_id,omitempty"`
	StudentID        *string            `json:"student_id,omitempty" bson:"student_id,omitempty"`
	History          *[]StatusHistory   `json:"history,omitempty" bson:"history,omitempty"`
	StudentResponses *[]StudentResponse `json:"student_responses,omitempty" bson:"student_responses,omitempty"`
	MissedQuestions  *[]int32           `json:"missed_questions,omitempty" bson:"missed_questions,omitempty"`
	ImageIds         *[]string          `json:"image_ids,omitempty" bson:"image_ids,omitempty"`
	TotalScore       *float32           `json:"total_achieved_score,omitempty" bson:"total_achieved_score,omitempty"`
	CreatedAt        *time.Time         `json:"created_at,omitempty" bson:"created_at,omitempty"`
	CreatedBy        *string            `json:"created_by,omitempty" bson:"created_by,omitempty"`
	UpdatedAt        *time.Time         `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	UpdatedBy        *string            `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	DeletedAt        *time.Time         `json:"deleted_at,omitempty" bson:"deleted_at,omitempty"`
	DeletedBy        *string            `json:"deleted_by,omitempty" bson:"deleted_by,omitempty"`
}

type StudentResponse struct {
	QuestionNumber int     `json:"question_number,omitempty" bson:"question_number,omitempty"`
	Response       *string `json:"response,omitempty" bson:"response,omitempty"`
	Score          float32 `json:"score" bson:"score"`
	Feedback       *string `json:"feedback,omitempty" bson:"feedback,omitempty"`
}

func NewSubmission(instituteId, assignmentId, studentId string, studentResponses []StudentResponse, createdBy string, image_ids []string) *Submission {
	id := uuid.New().String()
	now := time.Now()
	history := NewStatusHistory(constants.SubmissionStatusSubmitted, createdBy)
	historyArr := make([]StatusHistory, 0)
	historyArr = append(historyArr, *history)
	return &Submission{
		ID:               &id,
		InstituteID:      &instituteId,
		AssignmentID:     &assignmentId,
		StudentID:        &studentId,
		History:          &historyArr,
		StudentResponses: &studentResponses,
		ImageIds:         &image_ids,
		CreatedAt:        &now,
		CreatedBy:        &createdBy,
	}
}

func (s *Submission) Validate() error {
	if s.InstituteID == nil || *s.InstituteID == "" {
		return errors.New("institute ID is required")
	}
	if s.AssignmentID == nil || *s.AssignmentID == "" {
		return errors.New("test ID is required")
	}
	if s.StudentID == nil || *s.StudentID == "" {
		return errors.New("student ID is required")
	}
	return nil
}

func NewStudentResponse(questionNumber int, response string, score float32, feedback string) *StudentResponse {
	return &StudentResponse{
		QuestionNumber: questionNumber,
		Response:       &response,
		Score:          score,
		Feedback:       &feedback,
	}
}
