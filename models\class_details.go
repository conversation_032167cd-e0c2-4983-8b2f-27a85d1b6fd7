// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassDetails class details
//
// swagger:model ClassDetails
type ClassDetails struct {

	// assessments
	Assessments []int32 `json:"assessments"`

	// classes
	Classes []string `json:"classes"`

	// students
	Students []int32 `json:"students"`
}

// Validate validates this class details
func (m *ClassDetails) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class details based on context it is used
func (m *ClassDetails) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassDetails) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassDetails) UnmarshalBinary(b []byte) error {
	var res ClassDetails
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
