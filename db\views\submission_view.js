db.createView("submissions_view", "assignments", [
  {
    $lookup: {
      from: "students",
      let: {
        assignment_grade: "$grade",
        assignment_sections: "$sections",
        institute_id: "$institute_id",
        term_id: "$term_id",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$institute_id", "$$institute_id"],
                },
              ],
            },
          },
        },
        {
          $addFields: {
            academic: {
              $getField: {
                field: "$$term_id",
                input: "$academic_history",
              },
            },
          },
        },
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$academic.grade", "$$assignment_grade"],
                },
                {
                  $in: ["$academic.section", "$$assignment_sections"],
                },
              ],
            },
          },
        },
      ],
      as: "students",
    },
  },
  {
    $unwind: "$students",
  },
  {
    $lookup: {
      from: "submissions",
      let: {
        assignment_id: "$_id",
        student_id: "$students.student_id",
        institute_id: "$institute_id",
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$assignment_id", "$$assignment_id"],
                },
                {
                  $eq: ["$student_id", "$$student_id"],
                },
                {
                  $eq: ["$institute_id", "$$institute_id"],
                },
              ],
            },
          },
        },
      ],
      as: "submission",
    },
  },
  {
    $unwind: {
      path: "$submission",
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $set: {
      student_first_name: "$students.first_name",
      student_last_name: "$students.last_name",
      student_email: "$students.email",
      student_grade: "$students.academic.grade",
      student_section: "$students.academic.section",
      student_roll_number: "$students.academic.roll_number",
      assignment_name: "$name",
      grade: "$grade",
      subject: "$subject",
      term_id: "$term_id",
      assignment_id: "$_id",
      student_id: "$students.student_id",
      history: "$submission.history",
      student_responses: {
        $map: {
          input: "$questions",
          as: "q",
          in: {
            $let: {
              vars: {
                matchedResponse: {
                  $first: {
                    $filter: {
                      input: { $ifNull: ["$submission.student_responses", []] },
                      as: "resp",
                      cond: {
                        $eq: ["$$resp.question_number", "$$q.question_number"],
                      },
                    },
                  },
                },
              },
              in: {
                question_number: "$$q.question_number",
                question: "$$q.question",
                questionScore: "$$q.score",
                rubric: "$$q.rubric",
                topics: "$$q.topics",
                response: { $ifNull: ["$$matchedResponse.response", ""] },
                score: { $ifNull: ["$$matchedResponse.score", 0] },
                feedback: { $ifNull: ["$$matchedResponse.feedback", ""] },
              },
            },
          },
        },
      },

      total_achieved_score: "$submission.total_achieved_score",
      missed_questions: "$submission.missed_questions",
      image_ids: "$submission.image_ids",
      file_list: "$submission.file_list",
      created_at: "$submission.created_at",
      created_by: "$submission.created_by",
      updated_at: "$submission.updated_at",
      updated_by: "$submission.updated_by",
      deleted_at: "$submission.deleted_at",
      deleted_by: "$submission.deleted_by",
    },
  },
  {
    $project: {
      _id: 0,
      institute_id: 1,
      assignment_id: 1,
      student_id: 1,
      student_first_name: 1,
      student_last_name: 1,
      student_email: 1,
      student_grade: 1,
      student_section: 1,
      student_roll_number: 1,
      assignment_name: 1,
      grade: 1,
      subject: 1,
      term_id: 1,
      history: 1,
      student_responses: 1,
      missed_questions: 1,
      image_ids: 1,
      total_achieved_score: 1,
      total_score: 1,
      file_list: 1,
      created_at: 1,
      created_by: 1,
      updated_at: 1,
      updated_by: 1,
      deleted_at: 1,
      deleted_by: 1,
    },
  },
]);
