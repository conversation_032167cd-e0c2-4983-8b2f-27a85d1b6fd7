// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// SubjectStats subject stats
//
// swagger:model SubjectStats
type SubjectStats struct {

	// chapters
	Chapters []*ChapterStats `json:"chapters"`

	// subject
	Subject string `json:"subject,omitempty"`
}

// Validate validates this subject stats
func (m *SubjectStats) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateChapters(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *SubjectStats) validateChapters(formats strfmt.Registry) error {
	if swag.IsZero(m.Chapters) { // not required
		return nil
	}

	for i := 0; i < len(m.Chapters); i++ {
		if swag.IsZero(m.Chapters[i]) { // not required
			continue
		}

		if m.Chapters[i] != nil {
			if err := m.Chapters[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("chapters" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("chapters" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this subject stats based on the context it is used
func (m *SubjectStats) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateChapters(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *SubjectStats) contextValidateChapters(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Chapters); i++ {

		if m.Chapters[i] != nil {

			if swag.IsZero(m.Chapters[i]) { // not required
				return nil
			}

			if err := m.Chapters[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("chapters" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("chapters" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *SubjectStats) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *SubjectStats) UnmarshalBinary(b []byte) error {
	var res SubjectStats
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
