package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type UserProvider interface {
	Add(ctx context.Context, eddyOwlUser *entities.User) (string, error)
	Get(ctx context.Context, id string) (*entities.User, error)
	GetAll(ctx context.Context, role int) (*[]entities.User, error)
	Edit(ctx context.Context, id string, eddyOwlUser *entities.User) error
	Delete(ctx context.Context, id string, deletedBy string) error
	GetByEmail(ctx context.Context, email string) (*entities.User, error)
}
