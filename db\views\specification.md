# MongoDB Views Specification

This document provides detailed specifications for all database views in the EddyOwl system.

## Core Views

### submissions_view
**Purpose**: Primary view combining assignment and student data
**Base Collection**: assignments
**Key Features**:
- Joins assignments with student information
- Includes detailed submission data
- Handles academic term mapping

**Fields**:
- institute_id
- assignment_id
- student_id
- student details (first_name, last_name, email, grade, section)
- assignment details (name, grade, subject)
- submission details (history, responses, scores)
- timestamps (created_at, updated_at, deleted_at)

## Count-Based Views

### daily_grade_submissions_count_view
**Purpose**: Track daily submission counts per grade
**Base View**: submissions_view
**Grouping**:
- institute_id
- term_id
- grade
- date (YYYY-MM-DD format)
**Metrics**: submissionCount (daily total)

### monthly_grade_submissions_count_view
**Purpose**: Track monthly submission counts per grade
**Base View**: submissions_view
**Grouping**:
- institute_id
- term_id
- grade
- date (YYYY-MM format)
**Metrics**: submissionCount (monthly total)

### monthly_subject_submissions_count_view
**Purpose**: Track monthly submission counts per subject
**Base View**: submissions_view
**Grouping**:
- institute_id
- term_id
- subject
- date (YYYY-MM format)
**Metrics**: submissionCount (monthly total)

### monthly_grade_subject_submissions_count_view
**Purpose**: Track monthly submission counts per grade and subject
**Base View**: submissions_view
**Grouping**:
- institute_id
- term_id
- grade
- subject
- date (YYYY-MM format)
**Metrics**: submissionCount (monthly total)

## Performance Views

### student_overall_percentage_view
**Purpose**: Calculate student performance metrics
**Base View**: submissions_view
**Key Calculations**:
- Submission percentage = (total_achieved_score / assignment.total_score) * 100
- Average performance per student
- Highest and lowest scores

**Grouping**:
- institute_id
- term_id
- student_id

**Metrics**:
- submission_percentage
- avg_percentage
- highest_percentage
- lowest_percentage

## Statistical Views

### grade_subject_submission_data_view
**Purpose**: Comprehensive subject-wise analysis per grade
**Base Collection**: institutes
**Pipeline Steps**:
1. Lookup terms
2. Generate grade array (1-12)
3. Lookup subjects from topics
4. Calculate statistics

**Metrics**:
- Total assignments
- Total submissions
- Average performance
- Subject-wise breakdowns

## Data Aggregation Rules

### Common Sorting Pattern
Most views implement consistent sorting:
1. institute_id (ascending)
2. term_id (ascending)
3. Additional fields (grade, date, etc.)

### Null Handling
- Explicit null checks for critical fields
- Conditional aggregation for division operations
- Null exclusion in count-based views

### Date Formatting
- Daily views: "%Y-%m-%d"
- Monthly views: "%Y-%m"

## Usage Guidelines

### Performance Considerations
- Views are computed on-demand
- Heavy use of $lookup operations may impact performance
- Consider indexing on frequently used fields:
  - institute_id
  - term_id
  - grade
  - created_at

### Maintenance
- Views should be recreated when base collection schemas change
- Regular validation of calculation accuracy
- Monitor view performance and usage patterns

### Dependencies
Views have hierarchical dependencies:
1. submissions_view (primary view)
2. Count-based views
3. Performance and statistical views

## Error Handling

### Division by Zero Protection
- Implemented in percentage calculations
- Uses $cond operators for safe division
- Returns null for invalid calculations

### Missing Data Handling
- Explicit $match stages to filter null values
- Proper handling of missing foreign keys in lookups
- Default values where appropriate

## Security Considerations

### Access Control
- Views inherit collection-level permissions
- Consider creating specific roles for view access
- Monitor and audit view usage

### Data Filtering
- Institute-level data separation
- Term-based data isolation
- Grade and section-specific access controls