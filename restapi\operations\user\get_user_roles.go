// Code generated by go-swagger; DO NOT EDIT.

package user

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetUserRolesHandlerFunc turns a function with the right signature into a get user roles handler
type GetUserRolesHandlerFunc func(GetUserRolesParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetUserRolesHandlerFunc) Handle(params GetUserRolesParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetUserRolesHandler interface for that can handle valid get user roles params
type GetUserRolesHandler interface {
	Handle(GetUserRolesParams, interface{}) middleware.Responder
}

// NewGetUserRoles creates a new http.Handler for the get user roles operation
func NewGetUserRoles(ctx *middleware.Context, handler GetUserRolesHandler) *GetUserRoles {
	return &GetUserRoles{Context: ctx, Handler: handler}
}

/*
	GetUserRoles swagger:route GET /userRoles user getUserRoles

# Get user roles

Returns all roles for a user
*/
type GetUserRoles struct {
	Context *middleware.Context
	Handler GetUserRolesHandler
}

func (o *GetUserRoles) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetUserRolesParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
