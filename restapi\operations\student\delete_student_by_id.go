// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// DeleteStudentByIDHandlerFunc turns a function with the right signature into a delete student by Id handler
type DeleteStudentByIDHandlerFunc func(DeleteStudentByIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn DeleteStudentByIDHandlerFunc) Handle(params DeleteStudentByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// DeleteStudentByIDHandler interface for that can handle valid delete student by Id params
type DeleteStudentByIDHandler interface {
	Handle(DeleteStudentByIDParams, interface{}) middleware.Responder
}

// NewDeleteStudentByID creates a new http.Handler for the delete student by Id operation
func NewDeleteStudentByID(ctx *middleware.Context, handler Delete<PERSON>tude<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) *DeleteStudentByID {
	return &DeleteStudentByID{Context: ctx, Handler: handler}
}

/*
	DeleteStudentByID swagger:route DELETE /institute/{instituteId}/student/{studentId} student deleteStudentById

# Delete student

Delete student by School Student Id
*/
type DeleteStudentByID struct {
	Context *middleware.Context
	Handler DeleteStudentByIDHandler
}

func (o *DeleteStudentByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewDeleteStudentByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
