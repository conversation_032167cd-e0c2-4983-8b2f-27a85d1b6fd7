// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetTermByIDHandlerFunc turns a function with the right signature into a get term by Id handler
type GetTermByIDHandlerFunc func(GetTermByIDParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetTermByIDHandlerFunc) Handle(params GetTermByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetTermByIDHandler interface for that can handle valid get term by Id params
type GetTermByIDHandler interface {
	Handle(GetTermByIDParams, interface{}) middleware.Responder
}

// NewGetTermByID creates a new http.Handler for the get term by Id operation
func NewGetTermByID(ctx *middleware.Context, handler GetTermByIDHandler) *GetTermByID {
	return &GetTermByID{Context: ctx, Handler: handler}
}

/*
	GetTermByID swagger:route GET /institute/{instituteId}/term/{termId} term getTermById

# Get term

Get term by School Term Id
*/
type GetTermByID struct {
	Context *middleware.Context
	Handler GetTermByIDHandler
}

func (o *GetTermByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetTermByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
