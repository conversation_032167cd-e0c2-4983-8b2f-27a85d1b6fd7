// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassAssessmentsBySubjectHandlerFunc turns a function with the right signature into a get class assessments by subject handler
type GetClassAssessmentsBySubjectHandlerFunc func(GetClassAssessmentsBySubjectParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassAssessmentsBySubjectHandlerFunc) Handle(params GetClassAssessmentsBySubjectParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassAssessmentsBySubjectHandler interface for that can handle valid get class assessments by subject params
type GetClassAssessmentsBySubjectHandler interface {
	Handle(GetClassAssessmentsBySubjectParams, interface{}) middleware.Responder
}

// NewGetClassAssessmentsBySubject creates a new http.Handler for the get class assessments by subject operation
func NewGetClassAssessmentsBySubject(ctx *middleware.Context, handler GetClassAssessmentsBySubjectHandler) *GetClassAssessmentsBySubject {
	return &GetClassAssessmentsBySubject{Context: ctx, Handler: handler}
}

/*
	GetClassAssessmentsBySubject swagger:route GET /institute/{instituteId}/class/{class}/assessmentsbysub stats getClassAssessmentsBySubject

# Get Class Assessments By Subject

Get Class Assessments By Subject
*/
type GetClassAssessmentsBySubject struct {
	Context *middleware.Context
	Handler GetClassAssessmentsBySubjectHandler
}

func (o *GetClassAssessmentsBySubject) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassAssessmentsBySubjectParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
