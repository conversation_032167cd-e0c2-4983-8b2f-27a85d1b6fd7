package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type StudentProvider interface {
	Add(ctx context.Context, student entities.Student) (string, error)
	GetAll(ctx context.Context, instituteId, email *string, grade *int, section *[]string, termId *string) (*[]entities.Student, error)
	Get(ctx context.Context, instituteId, studentId string) (*entities.Student, error)
	Delete(ctx context.Context, instituteId, studentId string, deletedBy string) error
	Edit(ctx context.Context, instituteId, studentId string, student *entities.Student) error
	GetByEmail(ctx context.Context, instituteId, email string) (*entities.Student, error)
}
