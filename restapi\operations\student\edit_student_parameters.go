// Code generated by go-swagger; DO NOT EDIT.

package student

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/validate"

	"eddyowl-backend/models"
)

// NewEditStudentParams creates a new EditStudentParams object
//
// There are no default values defined in the spec.
func NewEditStudentParams() EditStudentParams {

	return EditStudentParams{}
}

// EditStudentParams contains all the bound params for the edit student operation
// typically these are obtained from a http.Request
//
// swagger:parameters EditStudent
type EditStudentParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  In: body
	*/
	Student *models.Student
	/*
	  In: query
	*/
	TermID *string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewEditStudentParams() beforehand.
func (o *EditStudentParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	qs := runtime.Values(r.URL.Query())

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	if runtime.HasBody(r) {
		defer r.Body.Close()
		var body models.Student
		if err := route.Consumer.Consume(r.Body, &body); err != nil {
			res = append(res, errors.NewParseError("student", "body", "", err))
		} else {
			// validate body object
			if err := body.Validate(route.Formats); err != nil {
				res = append(res, err)
			}

			ctx := validate.WithOperationRequest(r.Context())
			if err := body.ContextValidate(ctx, route.Formats); err != nil {
				res = append(res, err)
			}

			if len(res) == 0 {
				o.Student = &body
			}
		}
	}

	qTermID, qhkTermID, _ := qs.GetOK("termId")
	if err := o.bindTermID(qTermID, qhkTermID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *EditStudentParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}

// bindTermID binds and validates parameter TermID from query.
func (o *EditStudentParams) bindTermID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: false
	// AllowEmptyValue: false

	if raw == "" { // empty values pass all other validations
		return nil
	}
	o.TermID = &raw

	return nil
}
