// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetAssignmentByIDHandlerFunc turns a function with the right signature into a get assignment by ID handler
type GetAssignmentByIDHandlerFunc func(GetAssignmentByIDParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetAssignmentByIDHandlerFunc) Handle(params GetAssignmentByIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetAssignmentByIDHandler interface for that can handle valid get assignment by ID params
type GetAssignmentByIDHandler interface {
	Handle(GetAssignmentByIDParams, interface{}) middleware.Responder
}

// NewGetAssignmentByID creates a new http.Handler for the get assignment by ID operation
func NewGetAssignmentByID(ctx *middleware.Context, handler GetAssignmentByIDHandler) *GetAssignmentByID {
	return &GetAssignmentByID{Context: ctx, Handler: handler}
}

/*
	GetAssignmentByID swagger:route GET /institute/{instituteId}/assignment/{assignmentId} assignment getAssignmentById

# Get assignment

Get assignment by assignmentid for an institute
*/
type GetAssignmentByID struct {
	Context *middleware.Context
	Handler GetAssignmentByIDHandler
}

func (o *GetAssignmentByID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetAssignmentByIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
