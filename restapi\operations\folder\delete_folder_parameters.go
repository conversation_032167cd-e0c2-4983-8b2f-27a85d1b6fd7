// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
)

// NewDeleteFolderParams creates a new DeleteFolderParams object
//
// There are no default values defined in the spec.
func NewDeleteFolderParams() DeleteFolderParams {

	return DeleteFolderParams{}
}

// DeleteFolderParams contains all the bound params for the delete folder operation
// typically these are obtained from a http.Request
//
// swagger:parameters DeleteFolder
type DeleteFolderParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	FolderID string
	/*
	  Required: true
	  In: path
	*/
	InstituteID string
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewDeleteFolderParams() beforehand.
func (o *DeleteFolderParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rFolderID, rhkFolderID, _ := route.Params.GetOK("folderId")
	if err := o.bindFolderID(rFolderID, rhkFolderID, route.Formats); err != nil {
		res = append(res, err)
	}

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindFolderID binds and validates parameter FolderID from path.
func (o *DeleteFolderParams) bindFolderID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.FolderID = raw

	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *DeleteFolderParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}
