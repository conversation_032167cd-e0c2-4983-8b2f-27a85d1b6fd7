// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetGradedSubmissionPerformanceHandlerFunc turns a function with the right signature into a get graded submission performance handler
type GetGradedSubmissionPerformanceHandlerFunc func(GetGradedSubmissionPerformanceParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetGradedSubmissionPerformanceHandlerFunc) Handle(params GetGradedSubmissionPerformanceParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetGradedSubmissionPerformanceHandler interface for that can handle valid get graded submission performance params
type GetGradedSubmissionPerformanceHandler interface {
	Handle(GetGradedSubmissionPerformanceParams, interface{}) middleware.Responder
}

// NewGetGradedSubmissionPerformance creates a new http.Handler for the get graded submission performance operation
func NewGetGradedSubmissionPerformance(ctx *middleware.Context, handler GetGradedSubmissionPerformanceHandler) *GetGradedSubmissionPerformance {
	return &GetGradedSubmissionPerformance{Context: ctx, Handler: handler}
}

/*
	GetGradedSubmissionPerformance swagger:route GET /institute/{instituteId}/assignment/{assignmentId}/student/{studentId}/submission/performance stats getGradedSubmissionPerformance

# Get graded student submission performance

Get graded submission performance
*/
type GetGradedSubmissionPerformance struct {
	Context *middleware.Context
	Handler GetGradedSubmissionPerformanceHandler
}

func (o *GetGradedSubmissionPerformance) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetGradedSubmissionPerformanceParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
