// Code generated by go-swagger; DO NOT EDIT.

package instructor

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetInstructorByInstituteIDHandlerFunc turns a function with the right signature into a get instructor by institute Id handler
type GetInstructorByInstituteIDHandlerFunc func(GetInstructorByInstituteIDParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetInstructorByInstituteIDHandlerFunc) Handle(params GetInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetInstructorByInstituteIDHandler interface for that can handle valid get instructor by institute Id params
type GetInstructorByInstituteIDHandler interface {
	Handle(GetInstructorByInstituteIDParams, interface{}) middleware.Responder
}

// NewGetInstructorByInstituteID creates a new http.Handler for the get instructor by institute Id operation
func NewGetInstructorByInstituteID(ctx *middleware.Context, handler GetInstructorByInstituteIDHandler) *GetInstructorByInstituteID {
	return &GetInstructorByInstituteID{Context: ctx, Handler: handler}
}

/*
	GetInstructorByInstituteID swagger:route GET /institute/{instituteId}/instructor instructor getInstructorByInstituteId

# Get instructors

Get instructors by institute id
*/
type GetInstructorByInstituteID struct {
	Context *middleware.Context
	Handler GetInstructorByInstituteIDHandler
}

func (o *GetInstructorByInstituteID) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetInstructorByInstituteIDParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
