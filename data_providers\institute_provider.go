package data_providers

import (
	"context"
	"eddyowl-backend/entities"
)

type InstituteProvider interface {
	Add(ctx context.Context, institute *entities.Institute) (string, error)
	Get(ctx context.Context, instituteId string) (*entities.Institute, error)
	Edit(ctx context.Context, instituteId string, institute *entities.Institute) error
	Delete(ctx context.Context, instituteId string, deletedBy string) error
}
