// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateNewTermOKCode is the HTTP code returned for type CreateNewTermOK
const CreateNewTermOKCode int = 200

/*
CreateNewTermOK Successful operation

swagger:response createNewTermOK
*/
type CreateNewTermOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewCreateNewTermOK creates CreateNewTermOK with default headers values
func NewCreateNewTermOK() *CreateNewTermOK {

	return &CreateNewTermOK{}
}

// WithPayload adds the payload to the create new term o k response
func (o *CreateNewTermOK) WithPayload(payload *models.SuccessResponse) *CreateNewTermOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term o k response
func (o *CreateNewTermOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateNewTermBadRequestCode is the HTTP code returned for type CreateNewTermBadRequest
const CreateNewTermBadRequestCode int = 400

/*
CreateNewTermBadRequest Bad Request

swagger:response createNewTermBadRequest
*/
type CreateNewTermBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermBadRequest creates CreateNewTermBadRequest with default headers values
func NewCreateNewTermBadRequest() *CreateNewTermBadRequest {

	return &CreateNewTermBadRequest{}
}

// WithPayload adds the payload to the create new term bad request response
func (o *CreateNewTermBadRequest) WithPayload(payload models.ErrorResponse) *CreateNewTermBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term bad request response
func (o *CreateNewTermBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewTermForbiddenCode is the HTTP code returned for type CreateNewTermForbidden
const CreateNewTermForbiddenCode int = 403

/*
CreateNewTermForbidden Forbidden

swagger:response createNewTermForbidden
*/
type CreateNewTermForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermForbidden creates CreateNewTermForbidden with default headers values
func NewCreateNewTermForbidden() *CreateNewTermForbidden {

	return &CreateNewTermForbidden{}
}

// WithPayload adds the payload to the create new term forbidden response
func (o *CreateNewTermForbidden) WithPayload(payload models.ErrorResponse) *CreateNewTermForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term forbidden response
func (o *CreateNewTermForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewTermNotFoundCode is the HTTP code returned for type CreateNewTermNotFound
const CreateNewTermNotFoundCode int = 404

/*
CreateNewTermNotFound Not Found

swagger:response createNewTermNotFound
*/
type CreateNewTermNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermNotFound creates CreateNewTermNotFound with default headers values
func NewCreateNewTermNotFound() *CreateNewTermNotFound {

	return &CreateNewTermNotFound{}
}

// WithPayload adds the payload to the create new term not found response
func (o *CreateNewTermNotFound) WithPayload(payload models.ErrorResponse) *CreateNewTermNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term not found response
func (o *CreateNewTermNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewTermTooManyRequestsCode is the HTTP code returned for type CreateNewTermTooManyRequests
const CreateNewTermTooManyRequestsCode int = 429

/*
CreateNewTermTooManyRequests Too Many Requests

swagger:response createNewTermTooManyRequests
*/
type CreateNewTermTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermTooManyRequests creates CreateNewTermTooManyRequests with default headers values
func NewCreateNewTermTooManyRequests() *CreateNewTermTooManyRequests {

	return &CreateNewTermTooManyRequests{}
}

// WithPayload adds the payload to the create new term too many requests response
func (o *CreateNewTermTooManyRequests) WithPayload(payload models.ErrorResponse) *CreateNewTermTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term too many requests response
func (o *CreateNewTermTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewTermInternalServerErrorCode is the HTTP code returned for type CreateNewTermInternalServerError
const CreateNewTermInternalServerErrorCode int = 500

/*
CreateNewTermInternalServerError Internal Server Error

swagger:response createNewTermInternalServerError
*/
type CreateNewTermInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermInternalServerError creates CreateNewTermInternalServerError with default headers values
func NewCreateNewTermInternalServerError() *CreateNewTermInternalServerError {

	return &CreateNewTermInternalServerError{}
}

// WithPayload adds the payload to the create new term internal server error response
func (o *CreateNewTermInternalServerError) WithPayload(payload models.ErrorResponse) *CreateNewTermInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term internal server error response
func (o *CreateNewTermInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// CreateNewTermServiceUnavailableCode is the HTTP code returned for type CreateNewTermServiceUnavailable
const CreateNewTermServiceUnavailableCode int = 503

/*
CreateNewTermServiceUnavailable Service Unvailable

swagger:response createNewTermServiceUnavailable
*/
type CreateNewTermServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateNewTermServiceUnavailable creates CreateNewTermServiceUnavailable with default headers values
func NewCreateNewTermServiceUnavailable() *CreateNewTermServiceUnavailable {

	return &CreateNewTermServiceUnavailable{}
}

// WithPayload adds the payload to the create new term service unavailable response
func (o *CreateNewTermServiceUnavailable) WithPayload(payload models.ErrorResponse) *CreateNewTermServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create new term service unavailable response
func (o *CreateNewTermServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateNewTermServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
