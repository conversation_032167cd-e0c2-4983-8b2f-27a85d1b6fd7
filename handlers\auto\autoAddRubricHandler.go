package handlers

import (
	"context"
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/auto"
	"eddyowl-backend/utils"

	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type autoAddRubricImpl struct {
	aiComponent       components.AIComponent
	instituteProvider data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewAutoAddRubricHandler(
	aiComponent components.AIComponent,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) auto.AutoAddRubricHandler {
	return &autoAddRubricImpl{
		aiComponent:       aiComponent,
		instituteProvider: instituteProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *autoAddRubricImpl) Handle(params auto.AutoAddRubricParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : AutoAddRubricHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return auto.NewAutoAddRubricForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.autoAddRubricValidator(ctx, params)
	if !valid {
		return validateResp
	}
	questionSchema := []components.QuestionSchema{}
	for _, question := range params.Questions.Questions.QuestionList {
		questionSchema = append(questionSchema, components.QuestionSchema{
			QuestionNumber: int(question.QuestionNumber),
			Question:       question.Question,
			QuestionScore:  float64(question.QuestionScore),
		})
	}
	questionWithRubricSchema, err := impl.aiComponent.CreateAutoRubric(ctx, int(params.Questions.Class), params.Questions.Subject, questionSchema)
	if err != nil {
		log.Error().Msg(err.Error())
		return auto.NewAutoAddRubricInternalServerError().WithPayload("Unable to create Rubric")
	}
	questionList := []*models.Question{}
	for _, question := range *questionWithRubricSchema {
		questionList = append(questionList, &models.Question{
			QuestionNumber: int32(question.QuestionNumber),
			Question:       question.Question,
			QuestionScore:  float32(question.QuestionScore),
			QuestionRubric: question.QuestionRubric,
		})
	}
	return auto.NewAutoAddRubricOK().WithPayload(
		&models.QuestionList{
			QuestionList: questionList,
		},
	)
}

func (impl *autoAddRubricImpl) autoAddRubricValidator(ctx context.Context, params auto.AutoAddRubricParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, auto.NewAutoAddRubricBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, auto.NewAutoAddRubricBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, auto.NewAutoAddRubricInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.Questions.Class <= 0 {
		return false, auto.NewAutoAddRubricBadRequest().WithPayload("Invalid Grade")
	}
	if params.Questions.Subject == "" {
		return false, auto.NewAutoAddRubricBadRequest().WithPayload("Invalid Subject")
	}
	if len(params.Questions.Questions.QuestionList) <= 0 {
		return false, auto.NewAutoAddRubricBadRequest().WithPayload("Invalid Question List")
	}
	return true, nil
}
