// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// CreateAssignmentHandlerFunc turns a function with the right signature into a create assignment handler
type CreateAssignmentHandlerFunc func(CreateAssignmentParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn CreateAssignmentHandlerFunc) Handle(params CreateAssignmentParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateAssignmentHandler interface for that can handle valid create assignment params
type CreateAssignmentHandler interface {
	Handle(CreateAssignmentParams, interface{}) middleware.Responder
}

// NewCreateAssignment creates a new http.Handler for the create assignment operation
func NewCreateAssignment(ctx *middleware.Context, handler CreateAssignmentHandler) *CreateAssignment {
	return &CreateAssignment{Context: ctx, Handler: handler}
}

/*
	CreateAssignment swagger:route POST /institute/{instituteId}/assignment assignment createAssignment

# Create new assignment

Create new assignment
*/
type CreateAssignment struct {
	Context *middleware.Context
	Handler CreateAssignmentHandler
}

func (o *CreateAssignment) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateAssignmentParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
