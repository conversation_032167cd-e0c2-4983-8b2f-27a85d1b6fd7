package handlers

import (
	"context"
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/auto"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type autoFileRubricImpl struct {
	aiComponent       components.AIComponent
	instituteProvider data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewAutoFileRubricHandler(
	aiComponent components.AIComponent,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) auto.AutoFileRubricHandler {
	return &autoFileRubricImpl{
		aiComponent:       aiComponent,
		instituteProvider: instituteProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *autoFileRubricImpl) Handle(params auto.AutoFileRubricParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : AutoFileRubricHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return auto.NewAutoFileRubricForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.autoFileRubricValidator(ctx, params)
	if !valid {
		return validateResp
	}
	questionSchema := []components.QuestionSchema{}
	for _, question := range params.Options.Questions.QuestionList {
		questionSchema = append(questionSchema, components.QuestionSchema{
			QuestionNumber: int(question.QuestionNumber),
			Question:       question.Question,
			QuestionScore:  float64(question.QuestionScore),
		})
	}
	questionWithRubricSchema, err := impl.aiComponent.CreateFileRubric(ctx, int(params.Options.Class), params.Options.Subject, questionSchema, params.Options.FilePaths)
	if err != nil {
		log.Error().Msg(err.Error())
		return auto.NewAutoFileRubricInternalServerError().WithPayload("Unable to create Rubric")
	}
	questionList := []*models.Question{}
	for _, question := range *questionWithRubricSchema {
		questionList = append(questionList, &models.Question{
			QuestionNumber: int32(question.QuestionNumber),
			Question:       question.Question,
			QuestionScore:  float32(question.QuestionScore),
			QuestionRubric: question.QuestionRubric,
		})
	}
	return auto.NewAutoFileRubricOK().WithPayload(
		&models.QuestionList{
			QuestionList: questionList,
		},
	)
}

func (impl *autoFileRubricImpl) autoFileRubricValidator(ctx context.Context, params auto.AutoFileRubricParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, auto.NewAutoFileRubricInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.Options.Class <= 0 {
		return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Grade")
	}
	if params.Options.Subject == "" {
		return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Subject")
	}
	if len(params.Options.Questions.QuestionList) <= 0 {
		return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Question List")
	}
	if len(params.Options.FilePaths) <= 0 {
		return false, auto.NewAutoFileRubricBadRequest().WithPayload("Invalid Image Urls")
	}
	return true, nil
}
