package handlers

import (
	"eddyowl-backend/components"
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type createSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	assignmentProvider data_providers.AssignmentProvider
	studentProvider    data_providers.StudentProvider
	aiComponent        components.AIComponent
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewCreateSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	assignmentProvider data_providers.AssignmentProvider,
	studentProvider data_providers.StudentProvider,
	aiComponent components.AIComponent,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.CreateSubmissionHandler {
	return &createSubmissionImpl{
		submissionProvider: submissionProvider,
		assignmentProvider: assignmentProvider,
		studentProvider:    studentProvider,
		aiComponent:        aiComponent,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *createSubmissionImpl) Handle(params submission.CreateSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateSubmissionHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewCreateSubmissionForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.createSubmissionValidator(params)
	if !valid {
		return validateResp
	}
	createdBy := principal.(string)
	studentResponses := make([]entities.StudentResponse, 0)
	submissionEntity := entities.NewSubmission(params.InstituteID, params.AssignmentID, params.StudentID, studentResponses, createdBy, params.Files.Files)
	err = submissionEntity.Validate()
	if err != nil {
		log.Error().Msg(err.Error())
		return submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Parameters")
	}

	id, err := impl.submissionProvider.Add(ctx, submissionEntity)
	if err != nil {
		log.Error().Msg(err.Error())
		if mongo.IsDuplicateKeyError(err) {
			return submission.NewCreateSubmissionBadRequest().WithPayload("Submission already exists for student")
		}

		return submission.NewCreateSubmissionInternalServerError().WithPayload("Unable to create submission")
	}

	err_1 := impl.aiComponent.GradeAssignment(ctx, params.InstituteID, params.AssignmentID, params.StudentID)
	if err_1 != nil {
		log.Error().Msg("Grading Failed for : Institute :" + params.InstituteID + "|Assignment:" + params.AssignmentID + "|student:" + params.StudentID)
		now := time.Now()
		newHistory := entities.StatusHistory{
			Status:    constants.SubmissionStatusFailed,
			Timestamp: &now,
			UpdatedBy: &createdBy,
		}

		if submissionEntity.History == nil {
			submissionEntity.History = &[]entities.StatusHistory{}
		}

		// Append to the in-memory slice
		updatedHistory := append(*submissionEntity.History, newHistory)
		submissionEntity.History = &updatedHistory
		_, err_2 := impl.submissionProvider.Edit(ctx, params.InstituteID, params.AssignmentID, params.StudentID, submissionEntity)
		if err_2 != nil {
			log.Error().Msg(err_2.Error())
			return submission.NewCreateSubmissionInternalServerError().WithPayload("Unable to mark submission as failed")
		}
		return submission.NewCreateSubmissionInternalServerError().WithPayload("Unable to send Grading Request")
	}
	log.Info().Msg("Grading Assignment : " + params.AssignmentID + " for student : " + params.StudentID)

	return submission.NewCreateSubmissionOK().WithPayload(
		&models.SuccessResponse{
			ID:      id,
			Message: "Successfully created submission",
		},
	)
}

func (impl *createSubmissionImpl) createSubmissionValidator(params submission.CreateSubmissionParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "createSubmissionValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Institute ID")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Assignment ID")
	}
	if params.StudentID == constants.EmptyString {
		return false, submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Student ID")
	}
	_, err := impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Assignment ID")
		}
		return false, submission.NewCreateSubmissionInternalServerError().WithPayload("Unable to fetch Assignment")
	}
	_, err = impl.studentProvider.Get(ctx, params.InstituteID, params.StudentID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, submission.NewCreateSubmissionBadRequest().WithPayload("Invalid Student ID")
		}
		return false, submission.NewCreateSubmissionInternalServerError().WithPayload("Unable to fetch Student")
	}
	return true, nil
}
