package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/term"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type getTermImpl struct {
	provider          data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

// NewGetTermByIDHandler constructs a new handler for getting a term
func NewGetTermByIDHandler(
	provider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) term.GetTermByIDHandler {
	return &getTermImpl{
		provider:          provider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

// <PERSON>le processes the get term request
func (impl *getTermImpl) Handle(params term.GetTermByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetTermByIDHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return term.NewGetTermByIDForbidden().WithPayload("Unauthorized")
	}

	if params.InstituteID == constants.EmptyString || params.TermID == constants.EmptyString {
		return term.NewGetTermByIDBadRequest().WithPayload("Invalid termID or instituteID")
	}

	termEntity, err := impl.provider.Get(ctx, params.TermID, params.InstituteID)
	if err != nil {
		log.Error().Err(err).
			Str("termId", params.TermID).
			Str("instituteId", params.InstituteID).
			Msg("Failed to get term")

		if errors.Is(err, mongo.ErrNoDocuments) {
			return term.NewGetTermByIDNotFound().WithPayload("Term not found")
		}
		return term.NewGetTermByIDInternalServerError().WithPayload("Unable to get term")
	}

	termModel := convertTermEntityToModel(termEntity)
	return term.NewGetTermByIDOK().WithPayload(termModel)
}

func convertTermEntityToModel(entity *entities.Term) *models.Term {
	return &models.Term{
		Name:      *entity.Name,
		StartDate: entity.StartDate.String(),
		EndDate:   entity.EndDate.String(),
		TermID:    *entity.ID,
	}
}
