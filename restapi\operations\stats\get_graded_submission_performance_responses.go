// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetGradedSubmissionPerformanceOKCode is the HTTP code returned for type GetGradedSubmissionPerformanceOK
const GetGradedSubmissionPerformanceOKCode int = 200

/*
GetGradedSubmissionPerformanceOK Successful operation

swagger:response getGradedSubmissionPerformanceOK
*/
type GetGradedSubmissionPerformanceOK struct {

	/*
	  In: Body
	*/
	Payload *models.GradedSubmissionPerformance `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceOK creates GetGradedSubmissionPerformanceOK with default headers values
func NewGetGradedSubmissionPerformanceOK() *GetGradedSubmissionPerformanceOK {

	return &GetGradedSubmissionPerformanceOK{}
}

// WithPayload adds the payload to the get graded submission performance o k response
func (o *GetGradedSubmissionPerformanceOK) WithPayload(payload *models.GradedSubmissionPerformance) *GetGradedSubmissionPerformanceOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance o k response
func (o *GetGradedSubmissionPerformanceOK) SetPayload(payload *models.GradedSubmissionPerformance) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetGradedSubmissionPerformanceBadRequestCode is the HTTP code returned for type GetGradedSubmissionPerformanceBadRequest
const GetGradedSubmissionPerformanceBadRequestCode int = 400

/*
GetGradedSubmissionPerformanceBadRequest Bad Request

swagger:response getGradedSubmissionPerformanceBadRequest
*/
type GetGradedSubmissionPerformanceBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceBadRequest creates GetGradedSubmissionPerformanceBadRequest with default headers values
func NewGetGradedSubmissionPerformanceBadRequest() *GetGradedSubmissionPerformanceBadRequest {

	return &GetGradedSubmissionPerformanceBadRequest{}
}

// WithPayload adds the payload to the get graded submission performance bad request response
func (o *GetGradedSubmissionPerformanceBadRequest) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance bad request response
func (o *GetGradedSubmissionPerformanceBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetGradedSubmissionPerformanceForbiddenCode is the HTTP code returned for type GetGradedSubmissionPerformanceForbidden
const GetGradedSubmissionPerformanceForbiddenCode int = 403

/*
GetGradedSubmissionPerformanceForbidden Forbidden

swagger:response getGradedSubmissionPerformanceForbidden
*/
type GetGradedSubmissionPerformanceForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceForbidden creates GetGradedSubmissionPerformanceForbidden with default headers values
func NewGetGradedSubmissionPerformanceForbidden() *GetGradedSubmissionPerformanceForbidden {

	return &GetGradedSubmissionPerformanceForbidden{}
}

// WithPayload adds the payload to the get graded submission performance forbidden response
func (o *GetGradedSubmissionPerformanceForbidden) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance forbidden response
func (o *GetGradedSubmissionPerformanceForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetGradedSubmissionPerformanceNotFoundCode is the HTTP code returned for type GetGradedSubmissionPerformanceNotFound
const GetGradedSubmissionPerformanceNotFoundCode int = 404

/*
GetGradedSubmissionPerformanceNotFound Not Found

swagger:response getGradedSubmissionPerformanceNotFound
*/
type GetGradedSubmissionPerformanceNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceNotFound creates GetGradedSubmissionPerformanceNotFound with default headers values
func NewGetGradedSubmissionPerformanceNotFound() *GetGradedSubmissionPerformanceNotFound {

	return &GetGradedSubmissionPerformanceNotFound{}
}

// WithPayload adds the payload to the get graded submission performance not found response
func (o *GetGradedSubmissionPerformanceNotFound) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance not found response
func (o *GetGradedSubmissionPerformanceNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetGradedSubmissionPerformanceTooManyRequestsCode is the HTTP code returned for type GetGradedSubmissionPerformanceTooManyRequests
const GetGradedSubmissionPerformanceTooManyRequestsCode int = 429

/*
GetGradedSubmissionPerformanceTooManyRequests Too Many Requests

swagger:response getGradedSubmissionPerformanceTooManyRequests
*/
type GetGradedSubmissionPerformanceTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceTooManyRequests creates GetGradedSubmissionPerformanceTooManyRequests with default headers values
func NewGetGradedSubmissionPerformanceTooManyRequests() *GetGradedSubmissionPerformanceTooManyRequests {

	return &GetGradedSubmissionPerformanceTooManyRequests{}
}

// WithPayload adds the payload to the get graded submission performance too many requests response
func (o *GetGradedSubmissionPerformanceTooManyRequests) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance too many requests response
func (o *GetGradedSubmissionPerformanceTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetGradedSubmissionPerformanceInternalServerErrorCode is the HTTP code returned for type GetGradedSubmissionPerformanceInternalServerError
const GetGradedSubmissionPerformanceInternalServerErrorCode int = 500

/*
GetGradedSubmissionPerformanceInternalServerError Internal Server Error

swagger:response getGradedSubmissionPerformanceInternalServerError
*/
type GetGradedSubmissionPerformanceInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceInternalServerError creates GetGradedSubmissionPerformanceInternalServerError with default headers values
func NewGetGradedSubmissionPerformanceInternalServerError() *GetGradedSubmissionPerformanceInternalServerError {

	return &GetGradedSubmissionPerformanceInternalServerError{}
}

// WithPayload adds the payload to the get graded submission performance internal server error response
func (o *GetGradedSubmissionPerformanceInternalServerError) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance internal server error response
func (o *GetGradedSubmissionPerformanceInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetGradedSubmissionPerformanceServiceUnavailableCode is the HTTP code returned for type GetGradedSubmissionPerformanceServiceUnavailable
const GetGradedSubmissionPerformanceServiceUnavailableCode int = 503

/*
GetGradedSubmissionPerformanceServiceUnavailable Service Unvailable

swagger:response getGradedSubmissionPerformanceServiceUnavailable
*/
type GetGradedSubmissionPerformanceServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetGradedSubmissionPerformanceServiceUnavailable creates GetGradedSubmissionPerformanceServiceUnavailable with default headers values
func NewGetGradedSubmissionPerformanceServiceUnavailable() *GetGradedSubmissionPerformanceServiceUnavailable {

	return &GetGradedSubmissionPerformanceServiceUnavailable{}
}

// WithPayload adds the payload to the get graded submission performance service unavailable response
func (o *GetGradedSubmissionPerformanceServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetGradedSubmissionPerformanceServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get graded submission performance service unavailable response
func (o *GetGradedSubmissionPerformanceServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetGradedSubmissionPerformanceServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
