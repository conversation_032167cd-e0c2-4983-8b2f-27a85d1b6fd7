// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// ClassStudentsBySections class students by sections
//
// swagger:model ClassStudentsBySections
type ClassStudentsBySections struct {

	// sections
	Sections []string `json:"sections"`

	// students
	Students []int32 `json:"students"`
}

// Validate validates this class students by sections
func (m *ClassStudentsBySections) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this class students by sections based on context it is used
func (m *ClassStudentsBySections) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *ClassStudentsBySections) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *ClassStudentsBySections) UnmarshalBinary(b []byte) error {
	var res ClassStudentsBySections
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
