package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/student"
	"eddyowl-backend/utils"
	"errors"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type deleteStudentImpl struct {
	studentProvider   data_providers.StudentProvider
	instituteProvider data_providers.InstituteProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewDeleteStudentHandler(
	studentProvider data_providers.StudentProvider,
	instituteProvider data_providers.InstituteProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) student.DeleteStudentByIDHandler {
	return &deleteStudentImpl{
		studentProvider:   studentProvider,
		instituteProvider: instituteProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *deleteStudentImpl) Handle(params student.DeleteStudentByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteStudentByIDHandler")
	defer span.End()

	principal = principal.(string)
	if params.InstituteID == constants.EmptyString {
		return student.NewDeleteStudentByIDBadRequest().WithPayload("Invalid InstituteID")
	}
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return student.NewDeleteStudentByIDForbidden().WithPayload("Unauthorized")
	}

	// Validate request parameters (using a custom validator similar to your institute validator).
	valid, validateResp := impl.deleteStudentValidator(params)
	if !valid {
		return validateResp
	}
	deltedBy := principal.(string)
	err = impl.studentProvider.Delete(ctx, params.InstituteID, params.StudentID, deltedBy)
	if err != nil {
		log.Error().Msg(err.Error())
		return student.NewDeleteStudentByIDInternalServerError().WithPayload("Unable to delete Student")
	}

	return student.NewDeleteStudentByIDOK().WithPayload(
		&models.SuccessResponse{
			ID:      params.StudentID,
			Message: "Successfully deleted Student",
		},
	)
}

func (impl *deleteStudentImpl) deleteStudentValidator(params student.DeleteStudentByIDParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "deleteStudentValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, student.NewDeleteStudentByIDBadRequest().WithPayload("Invalid Institute ID")
	}
	_, err := impl.instituteProvider.Get(ctx, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, student.NewDeleteStudentByIDBadRequest().WithPayload("Invalid Institute ID")
		}
		log.Error().Msg(err.Error())
		return false, student.NewDeleteStudentByIDInternalServerError().WithPayload("Unable to fetch Institute")
	}
	if params.StudentID == constants.EmptyString {
		return false, student.NewDeleteStudentByIDBadRequest().WithPayload("Invalid Student ID")
	}
	return true, nil
}
