package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"

	"sort"

	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	statsProvider      data_providers.StatsProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewGetSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	userRolesProvider data_providers.UserRolesProvider,
	statsProvider data_providers.StatsProvider,
	tracer trace.Tracer,
) submission.GetSubmissionHandler {
	return &getSubmissionImpl{
		submissionProvider: submissionProvider,
		userRolesProvider:  userRolesProvider,
		statsProvider:      statsProvider,
		tracer:             tracer,
	}
}

func (impl *getSubmissionImpl) Handle(params submission.GetSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetSubmissionHandler")
	defer span.End()

	email := principal.(string)
	isStudent := false

	// First check if user has admin/instructor role
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, email, params.InstituteID,
		[]int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		// If not admin/instructor, check if user is a student
		err = utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, email, params.InstituteID,
			[]int{constants.StudentRole})
		if err != nil {
			log.Error().Err(err).Msg("Failed to check user roles")
			return submission.NewGetSubmissionForbidden().WithPayload("Unauthorized")
		}
		isStudent = true
	}

	valid, validateResp := impl.getSubmissionValidator(params)
	if !valid {
		return validateResp
	}

	submissionView, err := impl.submissionProvider.Get(ctx, params.InstituteID, params.AssignmentID, params.StudentID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch submission")
		return submission.NewGetSubmissionInternalServerError().WithPayload("Unable to fetch submission")
	}

	// For students, only return submission if it's published
	if isStudent && submissionView.History != nil && len(*submissionView.History) > 0 {
		lastStatus := (*submissionView.History)[len(*submissionView.History)-1].Status
		if lastStatus != constants.SubmissionStatusPublished {
			return submission.NewGetSubmissionForbidden().WithPayload("Submission not available")
		}
	}

	submissionResponse := &models.StudentSubmission{
		// ID:                *submissionView.ID,
		InstituteID:     *submissionView.InstituteID,
		AssignmentID:    *submissionView.AssignmentID,
		AssignmentName:  *submissionView.AssignmentName,
		AssignmentScore: *submissionView.TotalScore,
		StudentID:       *submissionView.StudentID,
		StudentEmail: func() string {
			if submissionView.StudentEmail != nil {
				return *submissionView.StudentEmail
			}
			return ""
		}(),
		StudentFirstName:  *submissionView.StudentFirstName,
		StudentLastName:   *submissionView.StudentLastName,
		StudentGrade:      *submissionView.StudentGrade,
		StudentSection:    *submissionView.StudentSection,
		StudentRollNumber: *submissionView.StudentRollNumber,
		Grade:             *submissionView.Grade,
		Subject:           *submissionView.Subject,
		TermID:            *submissionView.TermID,
		ImageIds: func() []string {
			if submissionView.ImageIds == nil {
				return nil
			}
			return *submissionView.ImageIds
		}(),
		MissedQuestions: func() []int32 {
			if submissionView.MissedQuestions == nil {
				return nil
			}
			return *submissionView.MissedQuestions
		}(),
		History: func() []*models.StatusHistory {
			if submissionView.History == nil {
				return nil
			}
			history := make([]*models.StatusHistory, len(*submissionView.History))
			for i, h := range *submissionView.History {
				history[i] = &models.StatusHistory{
					Status:    int32(h.Status),
					Timestamp: strfmt.DateTime(*h.Timestamp),
					UpdatedBy: *h.UpdatedBy,
				}
			}
			return history
		}(),
		CreatedAt: func() strfmt.DateTime {
			if submissionView.CreatedAt != nil {
				return strfmt.DateTime(*submissionView.CreatedAt)
			}
			return strfmt.DateTime{}
		}(),
		CreatedBy: func() string {
			if submissionView.CreatedBy != nil {
				return *submissionView.CreatedBy
			}
			return ""
		}(),
		UpdatedAt: func() strfmt.DateTime {
			if submissionView.UpdatedAt != nil {
				return strfmt.DateTime(*submissionView.UpdatedAt)
			}
			return strfmt.DateTime{}
		}(),
		UpdatedBy: func() string {
			if submissionView.UpdatedBy != nil {
				return *submissionView.UpdatedBy
			}
			return ""
		}(),
	}

	if submissionView.MissedQuestions != nil {
		submissionResponse.MissedQuestions = *submissionView.MissedQuestions
	} else {
		submissionResponse.MissedQuestions = []int32{}
	}
	submissionResponse.ImageIds = utils.GetStringSlice(submissionView.ImageIds)

	if submissionView.TotalAchievedScore != nil {
		submissionResponse.TotalScore = float32(*submissionView.TotalAchievedScore)
	}

	if submissionView.History != nil {
		history := make([]*models.StatusHistory, len(*submissionView.History))
		for i, h := range *submissionView.History {
			history[i] = &models.StatusHistory{
				Status:    int32(h.Status),
				Timestamp: strfmt.DateTime(*h.Timestamp),
				UpdatedBy: *h.UpdatedBy,
			}
		}
		submissionResponse.History = history
	}

	if submissionView.StudentResponses != nil {
		// Create and sort student responses slice
		studentResponses := make([]*models.StudentSubmissionStudentResponsesItems0, len(*submissionView.StudentResponses))

		// First create all responses
		for i, resp := range *submissionView.StudentResponses {
			studentResponses[i] = &models.StudentSubmissionStudentResponsesItems0{
				QuestionNumber: int32(resp.QuestionNumber),
				Response:       *resp.Response,
				Score:          float32(*resp.Score),
				Feedback:       *resp.Feedback,
				Question:       *resp.Question,
				QuestionScore:  *resp.QuestionScore,
				Topics: func() []*models.ChapterTopics {
					if resp.Topics == nil {
						return nil
					}
					topics := make([]*models.ChapterTopics, len(*resp.Topics))
					for i, t := range *resp.Topics {
						topics[i] = &models.ChapterTopics{
							Chapter: *t.Chapter,
							Topics:  *t.Topics,
						}
					}
					return topics
				}(),
			}
			
			// Only include rubric for non-student roles
			if !isStudent && resp.Rubric != nil {
				studentResponses[i].Rubric = *resp.Rubric
			}
		}

		// Sort by QuestionNumber
		sort.Slice(studentResponses, func(i, j int) bool {
			return studentResponses[i].QuestionNumber < studentResponses[j].QuestionNumber
		})

		submissionResponse.StudentResponses = studentResponses
	}

	topicStats, err := impl.statsProvider.GetStudentTopicPerformance(ctx, params.AssignmentID, *submissionView.TermID, params.StudentID)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			submissionResponse.ChapterPerformance = nil
		} else {
			log.Error().Err(err).Msg("Failed to get student topic performance")
			return submission.NewGetSubmissionInternalServerError().WithPayload("Unable to get student topic performance")
		}
	} else {
		submissionResponse.ChapterPerformance = make([]*models.ChaptersWithScore, len(topicStats.ChapterPerformance))
		for i, chapter := range topicStats.ChapterPerformance {
			submissionResponse.ChapterPerformance[i] = &models.ChaptersWithScore{
				Chapter: chapter.Name,
				Score:   float32(chapter.Score),
				TopicsWithScore: func() []*models.TopicsWithScore {
					topicsWithScore := make([]*models.TopicsWithScore, len(chapter.TopicPerformance))
					for i, topic := range chapter.TopicPerformance {
						topicsWithScore[i] = &models.TopicsWithScore{
							Topic:      topic.Name,
							TopicScore: float32(topic.Score),
						}
					}
					return topicsWithScore
				}(),
			}
		}
	}

	return submission.NewGetSubmissionOK().WithPayload(submissionResponse)
}

func (impl *getSubmissionImpl) getSubmissionValidator(params submission.GetSubmissionParams) (bool, middleware.Responder) {
	if params.InstituteID == constants.EmptyString {
		return false, submission.NewGetSubmissionBadRequest().WithPayload("Invalid Institute ID")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, submission.NewGetSubmissionBadRequest().WithPayload("Invalid Assignment ID")
	}
	if params.StudentID == constants.EmptyString {
		return false, submission.NewGetSubmissionBadRequest().WithPayload("Invalid Student ID")
	}
	return true, nil
}
