// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassTopPerformingStudentsOKCode is the HTTP code returned for type GetClassTopPerformingStudentsOK
const GetClassTopPerformingStudentsOKCode int = 200

/*
GetClassTopPerformingStudentsOK Successful operation

swagger:response getClassTopPerformingStudentsOK
*/
type GetClassTopPerformingStudentsOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassTopPerformingStudents `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsOK creates GetClassTopPerformingStudentsOK with default headers values
func NewGetClassTopPerformingStudentsOK() *GetClassTopPerformingStudentsOK {

	return &GetClassTopPerformingStudentsOK{}
}

// WithPayload adds the payload to the get class top performing students o k response
func (o *GetClassTopPerformingStudentsOK) WithPayload(payload *models.ClassTopPerformingStudents) *GetClassTopPerformingStudentsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students o k response
func (o *GetClassTopPerformingStudentsOK) SetPayload(payload *models.ClassTopPerformingStudents) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassTopPerformingStudentsBadRequestCode is the HTTP code returned for type GetClassTopPerformingStudentsBadRequest
const GetClassTopPerformingStudentsBadRequestCode int = 400

/*
GetClassTopPerformingStudentsBadRequest Bad Request

swagger:response getClassTopPerformingStudentsBadRequest
*/
type GetClassTopPerformingStudentsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsBadRequest creates GetClassTopPerformingStudentsBadRequest with default headers values
func NewGetClassTopPerformingStudentsBadRequest() *GetClassTopPerformingStudentsBadRequest {

	return &GetClassTopPerformingStudentsBadRequest{}
}

// WithPayload adds the payload to the get class top performing students bad request response
func (o *GetClassTopPerformingStudentsBadRequest) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students bad request response
func (o *GetClassTopPerformingStudentsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassTopPerformingStudentsForbiddenCode is the HTTP code returned for type GetClassTopPerformingStudentsForbidden
const GetClassTopPerformingStudentsForbiddenCode int = 403

/*
GetClassTopPerformingStudentsForbidden Forbidden

swagger:response getClassTopPerformingStudentsForbidden
*/
type GetClassTopPerformingStudentsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsForbidden creates GetClassTopPerformingStudentsForbidden with default headers values
func NewGetClassTopPerformingStudentsForbidden() *GetClassTopPerformingStudentsForbidden {

	return &GetClassTopPerformingStudentsForbidden{}
}

// WithPayload adds the payload to the get class top performing students forbidden response
func (o *GetClassTopPerformingStudentsForbidden) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students forbidden response
func (o *GetClassTopPerformingStudentsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassTopPerformingStudentsNotFoundCode is the HTTP code returned for type GetClassTopPerformingStudentsNotFound
const GetClassTopPerformingStudentsNotFoundCode int = 404

/*
GetClassTopPerformingStudentsNotFound Not Found

swagger:response getClassTopPerformingStudentsNotFound
*/
type GetClassTopPerformingStudentsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsNotFound creates GetClassTopPerformingStudentsNotFound with default headers values
func NewGetClassTopPerformingStudentsNotFound() *GetClassTopPerformingStudentsNotFound {

	return &GetClassTopPerformingStudentsNotFound{}
}

// WithPayload adds the payload to the get class top performing students not found response
func (o *GetClassTopPerformingStudentsNotFound) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students not found response
func (o *GetClassTopPerformingStudentsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassTopPerformingStudentsTooManyRequestsCode is the HTTP code returned for type GetClassTopPerformingStudentsTooManyRequests
const GetClassTopPerformingStudentsTooManyRequestsCode int = 429

/*
GetClassTopPerformingStudentsTooManyRequests Too Many Requests

swagger:response getClassTopPerformingStudentsTooManyRequests
*/
type GetClassTopPerformingStudentsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsTooManyRequests creates GetClassTopPerformingStudentsTooManyRequests with default headers values
func NewGetClassTopPerformingStudentsTooManyRequests() *GetClassTopPerformingStudentsTooManyRequests {

	return &GetClassTopPerformingStudentsTooManyRequests{}
}

// WithPayload adds the payload to the get class top performing students too many requests response
func (o *GetClassTopPerformingStudentsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students too many requests response
func (o *GetClassTopPerformingStudentsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassTopPerformingStudentsInternalServerErrorCode is the HTTP code returned for type GetClassTopPerformingStudentsInternalServerError
const GetClassTopPerformingStudentsInternalServerErrorCode int = 500

/*
GetClassTopPerformingStudentsInternalServerError Internal Server Error

swagger:response getClassTopPerformingStudentsInternalServerError
*/
type GetClassTopPerformingStudentsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsInternalServerError creates GetClassTopPerformingStudentsInternalServerError with default headers values
func NewGetClassTopPerformingStudentsInternalServerError() *GetClassTopPerformingStudentsInternalServerError {

	return &GetClassTopPerformingStudentsInternalServerError{}
}

// WithPayload adds the payload to the get class top performing students internal server error response
func (o *GetClassTopPerformingStudentsInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students internal server error response
func (o *GetClassTopPerformingStudentsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassTopPerformingStudentsServiceUnavailableCode is the HTTP code returned for type GetClassTopPerformingStudentsServiceUnavailable
const GetClassTopPerformingStudentsServiceUnavailableCode int = 503

/*
GetClassTopPerformingStudentsServiceUnavailable Service Unvailable

swagger:response getClassTopPerformingStudentsServiceUnavailable
*/
type GetClassTopPerformingStudentsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassTopPerformingStudentsServiceUnavailable creates GetClassTopPerformingStudentsServiceUnavailable with default headers values
func NewGetClassTopPerformingStudentsServiceUnavailable() *GetClassTopPerformingStudentsServiceUnavailable {

	return &GetClassTopPerformingStudentsServiceUnavailable{}
}

// WithPayload adds the payload to the get class top performing students service unavailable response
func (o *GetClassTopPerformingStudentsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassTopPerformingStudentsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class top performing students service unavailable response
func (o *GetClassTopPerformingStudentsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassTopPerformingStudentsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
