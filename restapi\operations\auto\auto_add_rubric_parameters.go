// Code generated by go-swagger; DO NOT EDIT.

package auto

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"io"
	"net/http"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/validate"

	"eddyowl-backend/models"
)

// NewAutoAddRubricParams creates a new AutoAddRubricParams object
//
// There are no default values defined in the spec.
func NewAutoAddRubricParams() AutoAddRubricParams {

	return AutoAddRubricParams{}
}

// AutoAddRubricParams contains all the bound params for the auto add rubric operation
// typically these are obtained from a http.Request
//
// swagger:parameters AutoAddRubric
type AutoAddRubricParams struct {

	// HTTP Request Object
	HTTPRequest *http.Request `json:"-"`

	/*
	  Required: true
	  In: path
	*/
	InstituteID string
	/*
	  Required: true
	  In: body
	*/
	Questions *models.RubricInput
}

// BindRequest both binds and validates a request, it assumes that complex things implement a Validatable(strfmt.Registry) error interface
// for simple values it will use straight method calls.
//
// To ensure default values, the struct must have been initialized with NewAutoAddRubricParams() beforehand.
func (o *AutoAddRubricParams) BindRequest(r *http.Request, route *middleware.MatchedRoute) error {
	var res []error

	o.HTTPRequest = r

	rInstituteID, rhkInstituteID, _ := route.Params.GetOK("instituteId")
	if err := o.bindInstituteID(rInstituteID, rhkInstituteID, route.Formats); err != nil {
		res = append(res, err)
	}

	if runtime.HasBody(r) {
		defer r.Body.Close()
		var body models.RubricInput
		if err := route.Consumer.Consume(r.Body, &body); err != nil {
			if err == io.EOF {
				res = append(res, errors.Required("questions", "body", ""))
			} else {
				res = append(res, errors.NewParseError("questions", "body", "", err))
			}
		} else {
			// validate body object
			if err := body.Validate(route.Formats); err != nil {
				res = append(res, err)
			}

			ctx := validate.WithOperationRequest(r.Context())
			if err := body.ContextValidate(ctx, route.Formats); err != nil {
				res = append(res, err)
			}

			if len(res) == 0 {
				o.Questions = &body
			}
		}
	} else {
		res = append(res, errors.Required("questions", "body", ""))
	}
	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

// bindInstituteID binds and validates parameter InstituteID from path.
func (o *AutoAddRubricParams) bindInstituteID(rawData []string, hasKey bool, formats strfmt.Registry) error {
	var raw string
	if len(rawData) > 0 {
		raw = rawData[len(rawData)-1]
	}

	// Required: true
	// Parameter is provided by construction from the route
	o.InstituteID = raw

	return nil
}
