// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetAllStatsOKCode is the HTTP code returned for type GetAllStatsOK
const GetAllStatsOKCode int = 200

/*
GetAllStatsOK Successful operation

swagger:response getAllStatsOK
*/
type GetAllStatsOK struct {

	/*
	  In: Body
	*/
	Payload *models.AllStats `json:"body,omitempty"`
}

// NewGetAllStatsOK creates GetAllStatsOK with default headers values
func NewGetAllStatsOK() *GetAllStatsOK {

	return &GetAllStatsOK{}
}

// WithPayload adds the payload to the get all stats o k response
func (o *GetAllStatsOK) WithPayload(payload *models.AllStats) *GetAllStatsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats o k response
func (o *GetAllStatsOK) SetPayload(payload *models.AllStats) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetAllStatsBadRequestCode is the HTTP code returned for type GetAllStatsBadRequest
const GetAllStatsBadRequestCode int = 400

/*
GetAllStatsBadRequest Bad Request

swagger:response getAllStatsBadRequest
*/
type GetAllStatsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsBadRequest creates GetAllStatsBadRequest with default headers values
func NewGetAllStatsBadRequest() *GetAllStatsBadRequest {

	return &GetAllStatsBadRequest{}
}

// WithPayload adds the payload to the get all stats bad request response
func (o *GetAllStatsBadRequest) WithPayload(payload models.ErrorResponse) *GetAllStatsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats bad request response
func (o *GetAllStatsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStatsForbiddenCode is the HTTP code returned for type GetAllStatsForbidden
const GetAllStatsForbiddenCode int = 403

/*
GetAllStatsForbidden Forbidden

swagger:response getAllStatsForbidden
*/
type GetAllStatsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsForbidden creates GetAllStatsForbidden with default headers values
func NewGetAllStatsForbidden() *GetAllStatsForbidden {

	return &GetAllStatsForbidden{}
}

// WithPayload adds the payload to the get all stats forbidden response
func (o *GetAllStatsForbidden) WithPayload(payload models.ErrorResponse) *GetAllStatsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats forbidden response
func (o *GetAllStatsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStatsNotFoundCode is the HTTP code returned for type GetAllStatsNotFound
const GetAllStatsNotFoundCode int = 404

/*
GetAllStatsNotFound Not Found

swagger:response getAllStatsNotFound
*/
type GetAllStatsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsNotFound creates GetAllStatsNotFound with default headers values
func NewGetAllStatsNotFound() *GetAllStatsNotFound {

	return &GetAllStatsNotFound{}
}

// WithPayload adds the payload to the get all stats not found response
func (o *GetAllStatsNotFound) WithPayload(payload models.ErrorResponse) *GetAllStatsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats not found response
func (o *GetAllStatsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStatsTooManyRequestsCode is the HTTP code returned for type GetAllStatsTooManyRequests
const GetAllStatsTooManyRequestsCode int = 429

/*
GetAllStatsTooManyRequests Too Many Requests

swagger:response getAllStatsTooManyRequests
*/
type GetAllStatsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsTooManyRequests creates GetAllStatsTooManyRequests with default headers values
func NewGetAllStatsTooManyRequests() *GetAllStatsTooManyRequests {

	return &GetAllStatsTooManyRequests{}
}

// WithPayload adds the payload to the get all stats too many requests response
func (o *GetAllStatsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetAllStatsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats too many requests response
func (o *GetAllStatsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStatsInternalServerErrorCode is the HTTP code returned for type GetAllStatsInternalServerError
const GetAllStatsInternalServerErrorCode int = 500

/*
GetAllStatsInternalServerError Internal Server Error

swagger:response getAllStatsInternalServerError
*/
type GetAllStatsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsInternalServerError creates GetAllStatsInternalServerError with default headers values
func NewGetAllStatsInternalServerError() *GetAllStatsInternalServerError {

	return &GetAllStatsInternalServerError{}
}

// WithPayload adds the payload to the get all stats internal server error response
func (o *GetAllStatsInternalServerError) WithPayload(payload models.ErrorResponse) *GetAllStatsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats internal server error response
func (o *GetAllStatsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetAllStatsServiceUnavailableCode is the HTTP code returned for type GetAllStatsServiceUnavailable
const GetAllStatsServiceUnavailableCode int = 503

/*
GetAllStatsServiceUnavailable Service Unvailable

swagger:response getAllStatsServiceUnavailable
*/
type GetAllStatsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetAllStatsServiceUnavailable creates GetAllStatsServiceUnavailable with default headers values
func NewGetAllStatsServiceUnavailable() *GetAllStatsServiceUnavailable {

	return &GetAllStatsServiceUnavailable{}
}

// WithPayload adds the payload to the get all stats service unavailable response
func (o *GetAllStatsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetAllStatsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get all stats service unavailable response
func (o *GetAllStatsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetAllStatsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
