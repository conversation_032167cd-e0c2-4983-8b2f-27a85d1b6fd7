// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"encoding/json"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// Instructor instructor
//
// swagger:model Instructor
type Instructor struct {

	// email
	Email string `json:"email,omitempty"`

	// first name
	FirstName string `json:"firstName,omitempty"`

	// last name
	LastName string `json:"lastName,omitempty"`

	// 0: AdminRole
	// 1: InstructorRole
	//
	// Enum: [1,2]
	Role int32 `json:"role,omitempty"`
}

// Validate validates this instructor
func (m *Instructor) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateRole(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

var instructorTypeRolePropEnum []interface{}

func init() {
	var res []int32
	if err := json.Unmarshal([]byte(`[1,2]`), &res); err != nil {
		panic(err)
	}
	for _, v := range res {
		instructorTypeRolePropEnum = append(instructorTypeRolePropEnum, v)
	}
}

// prop value enum
func (m *Instructor) validateRoleEnum(path, location string, value int32) error {
	if err := validate.EnumCase(path, location, value, instructorTypeRolePropEnum, true); err != nil {
		return err
	}
	return nil
}

func (m *Instructor) validateRole(formats strfmt.Registry) error {
	if swag.IsZero(m.Role) { // not required
		return nil
	}

	// value enum
	if err := m.validateRoleEnum("role", "body", m.Role); err != nil {
		return err
	}

	return nil
}

// ContextValidate validates this instructor based on context it is used
func (m *Instructor) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *Instructor) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *Instructor) UnmarshalBinary(b []byte) error {
	var res Instructor
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
