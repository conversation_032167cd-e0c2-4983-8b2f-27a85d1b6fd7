package entities

// Daily submission counts
type DailySubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Date        string `bson:"date"` // YYYY-MM-DD format
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Monthly submission counts
type MonthlySubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Date        string `bson:"date"` // YYYY-MM format
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Monthly submission counts
type GradeMonthlySubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Date        string `bson:"date"` // YYYY-MM format
		Grade       int32  `bson:"grade"`
		Subject     string `bson:"subject"`
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Grade-wise submission counts
type GradeSubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Grade       int32  `bson:"grade"`
		Date        string `bson:"date"` // YYYY-MM-DD format
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Subject-wise submission counts
type SubjectSubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Subject     string `bson:"subject"`
		Date        string `bson:"date"` // YYYY-MM format
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Grade and subject-wise submission counts
type GradeSubjectSubmissionsCountView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Grade       int32  `bson:"grade"`
		Subject     string `bson:"subject"`
		Date        string `bson:"date"` // YYYY-MM format
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submissionCount"`
}

// Student overall performance
type StudentOverallPercentageView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		StudentID   string `bson:"student_id"`
	} `bson:"_id"`
	Grade                       int32   `bson:"grade"`
	TotalSubmissionCount        int32   `bson:"total_submission_count"`
	AverageSubmissionPercentage float64 `bson:"average_submission_percentage"`
	HighestSubmissionPercentage float64 `bson:"highest_submission_percentage"`
	LowestSubmissionPercentage  float64 `bson:"lowest_submission_percentage"`
}

// Grade subject submission data
type GradeSubjectSubmissionDataView struct {
	InstituteID                 string  `bson:"institute_id"`
	TermID                      string  `bson:"term_id"`
	Grade                       int32   `bson:"grade"`
	Subject                     string  `bson:"subject"`
	AssignmentCount             int32   `bson:"assignment_count"`
	SubmissionCount             int32   `bson:"submission_count"`
	AverageSubmissionPercentage float64 `bson:"average_submission_percentage"`
}

// Grade overall stats
type GradeOverallStats struct {
	InstituteID                 string  `bson:"institute_id"`
	TermID                      string  `bson:"term_id"`
	Grade                       int32   `bson:"grade"`
	Students                    int32   `bson:"students"`
	Instructors                 int32   `bson:"instructors"`
	Assessments                 int32   `bson:"assessments"`
	AverageSubmissionPercentage float64 `bson:"average_submission_percentage"`
}

// Section submission count
type SectionDataView struct {
	ID struct {
		InstituteID string `bson:"institute_id"`
		TermID      string `bson:"term_id"`
		Grade       int32  `bson:"grade"`
		Section     string `bson:"section"`
	} `bson:"_id"`
	SubmissionCount int32 `bson:"submission_count"`
	AssignmentCount int32 `bson:"assignment_count"`
	StudentCount    int32 `bson:"student_count"`
}

// Institute overall stats
type InstituteOverallStatsView struct {
	InstituteID            string  `bson:"institute_id"`
	TermID                 string  `bson:"term_id"`
	Students               int32   `bson:"students"`
	Instructors            int32   `bson:"instructors"`
	Assessments            int32   `bson:"assessments"`
	Submissions            int32   `bson:"submissions_count"`
	AveragePercentageScore float64 `bson:"average_percentage_score"`
}
