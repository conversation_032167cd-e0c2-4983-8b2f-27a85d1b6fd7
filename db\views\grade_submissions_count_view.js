db.createView("daily_grade_submissions_count_view", "submissions_view", [
  {
    $match: {
      created_at: { $ne: null },
    },
  },
  {
    $group: {
      _id: {
        institute_id: "$institute_id",
        term_id: "$term_id",
        grade: "$grade", // Added grade to grouping
        date: { $dateToString: { format: "%Y-%m-%d", date: "$created_at" } },
      },
      submissionCount: { $sum: 1 },
    },
  },
  {
    $sort: {
      "_id.institute_id": 1,
      "_id.term_id": 1,
      "_id.grade": 1,
      "_id.date": 1,
    },
  },
]);
