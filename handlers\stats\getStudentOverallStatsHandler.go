package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getStudentOverallStatsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetStudentOverallStatsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetStudentOverallStatsHandler {
	return &getStudentOverallStatsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getStudentOverallStatsImpl) Handle(params stats.GetStudentOverallStatsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetStudentOverallStatsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetStudentOverallStatsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get student overall performance
	result, err := impl.statsProvider.GetStudentOverallPerformance(ctx, params.InstituteID, termID, params.StudentID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get student overall performance")
		return stats.NewGetStudentOverallStatsInternalServerError().WithPayload("Unable to get student performance data")
	}

	// Transform the data into StudentOverallStats model
	response := &models.StudentOverallStats{
		AverageStudentPerformance: float32(result.AverageSubmissionPercentage),
		TotalAssignmentsSolved:    result.TotalSubmissionCount,
	}

	return stats.NewGetStudentOverallStatsOK().WithPayload(response)
}
