// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetInstituteByIDOKCode is the HTTP code returned for type GetInstituteByIDOK
const GetInstituteByIDOKCode int = 200

/*
GetInstituteByIDOK Successful operation

swagger:response getInstituteByIdOK
*/
type GetInstituteByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.Institute `json:"body,omitempty"`
}

// NewGetInstituteByIDOK creates GetInstituteByIDOK with default headers values
func NewGetInstituteByIDOK() *GetInstituteByIDOK {

	return &GetInstituteByIDOK{}
}

// WithPayload adds the payload to the get institute by Id o k response
func (o *GetInstituteByIDOK) WithPayload(payload *models.Institute) *GetInstituteByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id o k response
func (o *GetInstituteByIDOK) SetPayload(payload *models.Institute) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetInstituteByIDBadRequestCode is the HTTP code returned for type GetInstituteByIDBadRequest
const GetInstituteByIDBadRequestCode int = 400

/*
GetInstituteByIDBadRequest Bad Request

swagger:response getInstituteByIdBadRequest
*/
type GetInstituteByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDBadRequest creates GetInstituteByIDBadRequest with default headers values
func NewGetInstituteByIDBadRequest() *GetInstituteByIDBadRequest {

	return &GetInstituteByIDBadRequest{}
}

// WithPayload adds the payload to the get institute by Id bad request response
func (o *GetInstituteByIDBadRequest) WithPayload(payload models.ErrorResponse) *GetInstituteByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id bad request response
func (o *GetInstituteByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstituteByIDForbiddenCode is the HTTP code returned for type GetInstituteByIDForbidden
const GetInstituteByIDForbiddenCode int = 403

/*
GetInstituteByIDForbidden Forbidden

swagger:response getInstituteByIdForbidden
*/
type GetInstituteByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDForbidden creates GetInstituteByIDForbidden with default headers values
func NewGetInstituteByIDForbidden() *GetInstituteByIDForbidden {

	return &GetInstituteByIDForbidden{}
}

// WithPayload adds the payload to the get institute by Id forbidden response
func (o *GetInstituteByIDForbidden) WithPayload(payload models.ErrorResponse) *GetInstituteByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id forbidden response
func (o *GetInstituteByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstituteByIDNotFoundCode is the HTTP code returned for type GetInstituteByIDNotFound
const GetInstituteByIDNotFoundCode int = 404

/*
GetInstituteByIDNotFound Not Found

swagger:response getInstituteByIdNotFound
*/
type GetInstituteByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDNotFound creates GetInstituteByIDNotFound with default headers values
func NewGetInstituteByIDNotFound() *GetInstituteByIDNotFound {

	return &GetInstituteByIDNotFound{}
}

// WithPayload adds the payload to the get institute by Id not found response
func (o *GetInstituteByIDNotFound) WithPayload(payload models.ErrorResponse) *GetInstituteByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id not found response
func (o *GetInstituteByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstituteByIDTooManyRequestsCode is the HTTP code returned for type GetInstituteByIDTooManyRequests
const GetInstituteByIDTooManyRequestsCode int = 429

/*
GetInstituteByIDTooManyRequests Too Many Requests

swagger:response getInstituteByIdTooManyRequests
*/
type GetInstituteByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDTooManyRequests creates GetInstituteByIDTooManyRequests with default headers values
func NewGetInstituteByIDTooManyRequests() *GetInstituteByIDTooManyRequests {

	return &GetInstituteByIDTooManyRequests{}
}

// WithPayload adds the payload to the get institute by Id too many requests response
func (o *GetInstituteByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *GetInstituteByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id too many requests response
func (o *GetInstituteByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstituteByIDInternalServerErrorCode is the HTTP code returned for type GetInstituteByIDInternalServerError
const GetInstituteByIDInternalServerErrorCode int = 500

/*
GetInstituteByIDInternalServerError Internal Server Error

swagger:response getInstituteByIdInternalServerError
*/
type GetInstituteByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDInternalServerError creates GetInstituteByIDInternalServerError with default headers values
func NewGetInstituteByIDInternalServerError() *GetInstituteByIDInternalServerError {

	return &GetInstituteByIDInternalServerError{}
}

// WithPayload adds the payload to the get institute by Id internal server error response
func (o *GetInstituteByIDInternalServerError) WithPayload(payload models.ErrorResponse) *GetInstituteByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id internal server error response
func (o *GetInstituteByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetInstituteByIDServiceUnavailableCode is the HTTP code returned for type GetInstituteByIDServiceUnavailable
const GetInstituteByIDServiceUnavailableCode int = 503

/*
GetInstituteByIDServiceUnavailable Service Unvailable

swagger:response getInstituteByIdServiceUnavailable
*/
type GetInstituteByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetInstituteByIDServiceUnavailable creates GetInstituteByIDServiceUnavailable with default headers values
func NewGetInstituteByIDServiceUnavailable() *GetInstituteByIDServiceUnavailable {

	return &GetInstituteByIDServiceUnavailable{}
}

// WithPayload adds the payload to the get institute by Id service unavailable response
func (o *GetInstituteByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetInstituteByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get institute by Id service unavailable response
func (o *GetInstituteByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetInstituteByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
