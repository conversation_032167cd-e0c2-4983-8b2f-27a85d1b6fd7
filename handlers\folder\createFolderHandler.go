package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/folder"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type createFolderImpl struct {
	folderProvider    data_providers.FolderProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewCreateFolderHandler(folderProvider data_providers.FolderProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) folder.CreateFolderHandler {
	return &createFolderImpl{
		folderProvider:    folderProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *createFolderImpl) Handle(params folder.CreateFolderParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : CreateFolderHandler")
	defer span.End()

	// Extract user ID from principal
	userID := principal.(string)

	// Check user role and institute access
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, userID, params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return folder.NewCreateFolderBadRequest().WithPayload("Unauthorized")
	}

	// Validate folder name
	if params.Folder.Name == "" {
		log.Error().Msg("Folder name is required")
		return folder.NewCreateFolderBadRequest().WithPayload("Folder name is required")
	}

	// Create folder entity
	folderEntity := entities.NewFolder(params.InstituteID, params.Folder.Name)

	// Validate folder entity
	if err := folderEntity.Validate(); err != nil {
		log.Error().Err(err).Msg("Invalid folder entity")
		return folder.NewCreateFolderBadRequest().WithPayload("Invalid folder data")
	}

	// Save folder to database
	err = impl.folderProvider.Create(ctx, folderEntity)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create folder")
		return folder.NewCreateFolderBadRequest().WithPayload("Failed to create folder")
	}

	// Convert entity to response model
	responseFolder := &models.Folder{
		ID:        *folderEntity.ID,
		Name:      *folderEntity.Name,
		CreatedAt: strfmt.DateTime(*folderEntity.CreatedAt),
	}

	log.Info().Str("folder_id", *folderEntity.ID).Str("institute_id", params.InstituteID).Msg("Folder created successfully")

	return folder.NewCreateFolderOK().WithPayload(responseFolder)
}
