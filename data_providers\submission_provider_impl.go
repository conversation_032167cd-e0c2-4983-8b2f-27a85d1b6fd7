package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.opentelemetry.io/otel/trace"
)

type submissionProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

func NewSubmissionProvider(mongoClient *mongo.Client, dbName string, tracer trace.Tracer) SubmissionProvider {
	return &submissionProvider{
		mongoClient: mongoClient,
		dbName:      dbName,
		tracer:      tracer,
	}
}

// Add creates the submission in the constants.MongoDBCollectionSubmissions collection.
func (s *submissionProvider) Add(ctx context.Context, submission *entities.Submission) (string, error) {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.Add")
	defer span.End()

	if submission.ID == nil {
		return "", errors.New("submission ID is required")
	}

	collection := s.mongoClient.Database(s.dbName).
		Collection(constants.MongoDBCollectionSubmissions)

	_, err := collection.InsertOne(ctx, submission)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", err
		}
		return "", fmt.Errorf("error inserting submission: %w", err)
	}

	return *submission.ID, nil
}

// Delete deletes submission from constants.MongoDBCollectionSubmissions and add to constants.MongoDBCollectionSubmissionsArchive.
func (s *submissionProvider) Delete(ctx context.Context, instituteId, assignmentId, studentId string, deletedBy string) error {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.Delete")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionSubmissions)
	archiveCollection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionSubmissionsArchive)

	filter := bson.M{
		"institute_id":  instituteId,
		"assignment_id": assignmentId,
		"student_id":    studentId,
	}
	// Find the submission to archive
	var submission entities.Submission
	err := collection.FindOne(ctx, filter).Decode(&submission)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return fmt.Errorf("submission not found: %w", err)
		}
		return fmt.Errorf("error finding submission: %w", err)
	}
	now := time.Now()
	submission.DeletedAt = &now
	submission.DeletedBy = &deletedBy

	// Insert into archive
	_, err = archiveCollection.InsertOne(ctx, submission)
	if err != nil {
		return fmt.Errorf("error archiving submission: %w", err)
	}

	// Delete from main collection
	_, err = collection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("error deleting submission: %w", err)
	}

	return nil
}

// Get fetches from constants.MongoDBCollectionSubmissionsView mongodb view.
func (s *submissionProvider) Get(ctx context.Context, instituteId string, assignmentId string, studentId string) (*entities.SubmissionView, error) {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.Get")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBViewSubmissions)

	filter := bson.M{
		"institute_id":  instituteId,
		"assignment_id": assignmentId,
		"student_id":    studentId,
	}

	var submission entities.SubmissionView
	err := collection.FindOne(ctx, filter).Decode(&submission)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("submission not found: %w", err)
		}
		return nil, fmt.Errorf("error finding submission: %w", err)
	}

	return &submission, nil
}

// Edit edits in constants.MongoDBCollectionSubmissions.
func (s *submissionProvider) Edit(ctx context.Context, instituteId string, assignmentId string, studentId string, submission *entities.Submission) (string, error) {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.Edit")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionSubmissions)

	filter := bson.M{
		"institute_id":  instituteId,
		"assignment_id": assignmentId,
		"student_id":    studentId,
	}

	update := bson.M{
		"$set": submission,
	}

	_, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return "", fmt.Errorf("error updating submission: %w", err)
	}

	return assignmentId, nil
}

// GetAll fetches from constants.MongoDBCollectionSubmissionsView mongodb view.
func (s *submissionProvider) GetAll(ctx context.Context, instituteId string, assignmentId *string, class *int32, section *[]string, studentId *string, termId *string, status *string) (*[]entities.SubmissionView, error) {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.GetAll")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBViewSubmissions)

	filter := bson.M{"institute_id": instituteId}

	if assignmentId != nil {
		filter["assignment_id"] = *assignmentId
	}
	if class != nil {
		filter["grade"] = *class
	}
	if section != nil && len(*section) > 0 {
		filter["student_section"] = bson.M{"$in": *section}
	}
	if studentId != nil {
		filter["student_id"] = *studentId
	}
	if termId != nil {
		filter["term_id"] = *termId
	}
	if status != nil {
		filter["history.status"] = *status
	}

	findOptions := options.Find()
	cursor, err := collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("error finding submissions: %w", err)
	}
	defer cursor.Close(ctx)

	var submissions []entities.SubmissionView
	for cursor.Next(ctx) {
		var submission entities.SubmissionView
		if err := cursor.Decode(&submission); err != nil {
			return nil, fmt.Errorf("error decoding submission: %w", err)
		}
		submissions = append(submissions, submission)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return &submissions, nil
}

// GetForEdit fetches from constants.MongoDBCollectionSubmissions collection.
func (s *submissionProvider) GetForEdit(ctx context.Context, instituteId string, assignmentId string, studentId string) (*entities.Submission, error) {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.GetForEdit")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionSubmissions)

	filter := bson.M{
		"institute_id":  instituteId,
		"assignment_id": assignmentId,
		"student_id":    studentId,
	}

	var submission entities.Submission
	err := collection.FindOne(ctx, filter).Decode(&submission)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("submission not found: %w", err)
		}
		return nil, fmt.Errorf("error finding submission: %w", err)
	}

	return &submission, nil
}

// Publish
func (s *submissionProvider) Publish(ctx context.Context, instituteId string, assignmentId string, studentId *string, updatedBy string) error {
	ctx, span := s.tracer.Start(ctx, "submissionProvider.PublishAll")
	defer span.End()

	collection := s.mongoClient.Database(s.dbName).Collection(constants.MongoDBCollectionSubmissions)

	// Find all submissions that are graded
	filter := bson.M{
		"institute_id":  instituteId,
		"assignment_id": assignmentId,
		"history.0": bson.M{
			"$exists": true,
		},
		"history": bson.M{
			"$not": bson.M{
				"$elemMatch": bson.M{
					"status": constants.SubmissionStatusPublished,
				},
			},
		},
	}

	// Add pipeline stage to match only documents where last history status is "GRADED"
	pipeline := []bson.M{
		{"$match": filter},
		{"$match": bson.M{
			"$expr": bson.M{
				"$eq": []interface{}{
					bson.M{"$arrayElemAt": []interface{}{
						"$history.status",
						-1,
					}},
					constants.SubmissionStatusGraded,
				},
			},
		}},
	}

	if studentId != nil {
		filter["student_id"] = *studentId
	}

	// Create new status history entry
	now := time.Now()
	update := bson.M{
		"$push": bson.M{
			"history": bson.M{
				"status":     constants.SubmissionStatusPublished,
				"timestamp":  now,
				"updated_by": updatedBy,
			},
		},
	}

	// Use aggregate with update
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return fmt.Errorf("error finding submissions to publish: %w", err)
	}
	defer cursor.Close(ctx)

	var docs []bson.M
	if err = cursor.All(ctx, &docs); err != nil {
		return fmt.Errorf("error reading submissions to publish: %w", err)
	}

	if len(docs) == 0 {
		return mongo.ErrNoDocuments
	}

	// Extract IDs for update
	var ids []interface{}
	for _, doc := range docs {
		ids = append(ids, doc["_id"])
	}

	// Perform update
	result, err := collection.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": ids}}, update)
	if err != nil {
		return fmt.Errorf("error publishing submissions: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("no graded submissions found")
	}

	return nil
}
