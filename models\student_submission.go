// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
	"github.com/go-openapi/validate"
)

// StudentSubmission student submission
//
// swagger:model StudentSubmission
type StudentSubmission struct {

	// assignment Id
	AssignmentID string `json:"assignmentId,omitempty"`

	// assignment name
	AssignmentName string `json:"assignmentName,omitempty"`

	// assignment score
	AssignmentScore int32 `json:"assignmentScore,omitempty"`

	// chapter performance
	ChapterPerformance []*ChaptersWithScore `json:"chapterPerformance"`

	// created at
	// Format: date-time
	CreatedAt strfmt.DateTime `json:"createdAt,omitempty"`

	// created by
	Created<PERSON>y string `json:"createdBy,omitempty"`

	// grade
	Grade int32 `json:"grade,omitempty"`

	// history
	History []*StatusHistory `json:"history"`

	// id
	ID string `json:"id,omitempty"`

	// image ids
	ImageIds []string `json:"imageIds"`

	// institute Id
	InstituteID string `json:"instituteId,omitempty"`

	// missed questions
	MissedQuestions []int32 `json:"missedQuestions"`

	// student email
	StudentEmail string `json:"studentEmail,omitempty"`

	// student first name
	StudentFirstName string `json:"studentFirstName,omitempty"`

	// student grade
	StudentGrade int32 `json:"studentGrade,omitempty"`

	// student Id
	StudentID string `json:"studentId,omitempty"`

	// student last name
	StudentLastName string `json:"studentLastName,omitempty"`

	// student responses
	StudentResponses []*StudentSubmissionStudentResponsesItems0 `json:"studentResponses"`

	// student roll number
	StudentRollNumber int32 `json:"studentRollNumber,omitempty"`

	// student section
	StudentSection string `json:"studentSection,omitempty"`

	// subject
	Subject string `json:"subject,omitempty"`

	// term Id
	TermID string `json:"termId,omitempty"`

	// total score
	TotalScore float32 `json:"totalScore,omitempty"`

	// updated at
	// Format: date-time
	UpdatedAt strfmt.DateTime `json:"updatedAt,omitempty"`

	// updated by
	UpdatedBy string `json:"updatedBy,omitempty"`
}

// Validate validates this student submission
func (m *StudentSubmission) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateChapterPerformance(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateCreatedAt(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateHistory(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateStudentResponses(formats); err != nil {
		res = append(res, err)
	}

	if err := m.validateUpdatedAt(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentSubmission) validateChapterPerformance(formats strfmt.Registry) error {
	if swag.IsZero(m.ChapterPerformance) { // not required
		return nil
	}

	for i := 0; i < len(m.ChapterPerformance); i++ {
		if swag.IsZero(m.ChapterPerformance[i]) { // not required
			continue
		}

		if m.ChapterPerformance[i] != nil {
			if err := m.ChapterPerformance[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("chapterPerformance" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("chapterPerformance" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentSubmission) validateCreatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.CreatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("createdAt", "body", "date-time", m.CreatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

func (m *StudentSubmission) validateHistory(formats strfmt.Registry) error {
	if swag.IsZero(m.History) { // not required
		return nil
	}

	for i := 0; i < len(m.History); i++ {
		if swag.IsZero(m.History[i]) { // not required
			continue
		}

		if m.History[i] != nil {
			if err := m.History[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("history" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("history" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentSubmission) validateStudentResponses(formats strfmt.Registry) error {
	if swag.IsZero(m.StudentResponses) { // not required
		return nil
	}

	for i := 0; i < len(m.StudentResponses); i++ {
		if swag.IsZero(m.StudentResponses[i]) { // not required
			continue
		}

		if m.StudentResponses[i] != nil {
			if err := m.StudentResponses[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentSubmission) validateUpdatedAt(formats strfmt.Registry) error {
	if swag.IsZero(m.UpdatedAt) { // not required
		return nil
	}

	if err := validate.FormatOf("updatedAt", "body", "date-time", m.UpdatedAt.String(), formats); err != nil {
		return err
	}

	return nil
}

// ContextValidate validate this student submission based on the context it is used
func (m *StudentSubmission) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateChapterPerformance(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateHistory(ctx, formats); err != nil {
		res = append(res, err)
	}

	if err := m.contextValidateStudentResponses(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentSubmission) contextValidateChapterPerformance(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.ChapterPerformance); i++ {

		if m.ChapterPerformance[i] != nil {

			if swag.IsZero(m.ChapterPerformance[i]) { // not required
				return nil
			}

			if err := m.ChapterPerformance[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("chapterPerformance" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("chapterPerformance" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentSubmission) contextValidateHistory(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.History); i++ {

		if m.History[i] != nil {

			if swag.IsZero(m.History[i]) { // not required
				return nil
			}

			if err := m.History[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("history" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("history" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

func (m *StudentSubmission) contextValidateStudentResponses(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.StudentResponses); i++ {

		if m.StudentResponses[i] != nil {

			if swag.IsZero(m.StudentResponses[i]) { // not required
				return nil
			}

			if err := m.StudentResponses[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("studentResponses" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *StudentSubmission) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentSubmission) UnmarshalBinary(b []byte) error {
	var res StudentSubmission
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}

// StudentSubmissionStudentResponsesItems0 student submission student responses items0
//
// swagger:model StudentSubmissionStudentResponsesItems0
type StudentSubmissionStudentResponsesItems0 struct {

	// feedback
	Feedback string `json:"feedback,omitempty"`

	// question
	Question string `json:"question,omitempty"`

	// question number
	QuestionNumber int32 `json:"questionNumber,omitempty"`

	// question score
	QuestionScore int32 `json:"questionScore,omitempty"`

	// response
	Response string `json:"response,omitempty"`

	// rubric
	Rubric string `json:"rubric,omitempty"`

	// score
	Score float32 `json:"score,omitempty"`

	// topics
	Topics []*ChapterTopics `json:"topics"`
}

// Validate validates this student submission student responses items0
func (m *StudentSubmissionStudentResponsesItems0) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateTopics(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentSubmissionStudentResponsesItems0) validateTopics(formats strfmt.Registry) error {
	if swag.IsZero(m.Topics) { // not required
		return nil
	}

	for i := 0; i < len(m.Topics); i++ {
		if swag.IsZero(m.Topics[i]) { // not required
			continue
		}

		if m.Topics[i] != nil {
			if err := m.Topics[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this student submission student responses items0 based on the context it is used
func (m *StudentSubmissionStudentResponsesItems0) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateTopics(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *StudentSubmissionStudentResponsesItems0) contextValidateTopics(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Topics); i++ {

		if m.Topics[i] != nil {

			if swag.IsZero(m.Topics[i]) { // not required
				return nil
			}

			if err := m.Topics[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("topics" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("topics" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *StudentSubmissionStudentResponsesItems0) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentSubmissionStudentResponsesItems0) UnmarshalBinary(b []byte) error {
	var res StudentSubmissionStudentResponsesItems0
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
