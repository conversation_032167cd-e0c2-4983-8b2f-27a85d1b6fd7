package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/instructor"
	"eddyowl-backend/utils"
	"eddyowl-backend/validators"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type addInstructorByInstituteIDImpl struct {
	instructorProvider data_providers.InstructorProvider
	userProvider       data_providers.UserProvider
	instituteProvider  data_providers.InstituteProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewAddInstructorByInstituteIDHandler(
	instructorProvider data_providers.InstructorProvider, userProvider data_providers.UserProvider, instituteProvider data_providers.InstituteProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) instructor.AddInstructorByInstituteIDHandler {
	return &addInstructorByInstituteIDImpl{instructorProvider: instructorProvider, userProvider: userProvider, instituteProvider: instituteProvider, userRolesProvider: userRolesProvider, tracer: tracer}
}

func (impl *addInstructorByInstituteIDImpl) Handle(params instructor.AddInstructorByInstituteIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : AddInstructorByInstituteIDHandler")
	defer span.End()

	valid, validErr := impl.validateAddInstructor(params)
	if !valid {
		if validErr != nil {
			return validErr
		}
		return instructor.NewAddInstructorByInstituteIDBadRequest().WithPayload("Invalid Parameters")
	}
	createdBy := principal.(string)

	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, createdBy, params.InstituteID, []int{constants.AdminRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return instructor.NewAddInstructorByInstituteIDForbidden().WithPayload("Unauthorized")
	}

	user, err := impl.userProvider.GetByEmail(ctx, params.Instructor.Email)
	if err != nil {
		if err != mongo.ErrNoDocuments {
			log.Error().Err(err).Msg("Unable to fetch User")
			return instructor.NewAddInstructorByInstituteIDInternalServerError().WithPayload("Unable to fetch User")
		}
		user = nil
	}

	status := constants.UserStatusInactive
	firstName := params.Instructor.FirstName
	lastName := params.Instructor.LastName
	if user != nil {
		status = constants.UserStatusActive
		firstName = *user.FirstName
		lastName = *user.LastName
	}
	role := params.Instructor.Role

	eddyOwlInstructorEntity := entities.NewInstructor(params.InstituteID, params.Instructor.Email, role, createdBy)
	eddyOwlInstructorEntity.Status = status
	eddyOwlInstructorEntity.FirstName = &firstName
	eddyOwlInstructorEntity.LastName = &lastName

	instructorID, err := impl.instructorProvider.Add(ctx, eddyOwlInstructorEntity)
	if err != nil {
		log.Error().Err(err).Msg("Unable to add Instructor")
		return instructor.NewAddInstructorByInstituteIDInternalServerError().WithPayload("Unable to add Instructor")
	}
	return instructor.NewAddInstructorByInstituteIDOK().WithPayload(&models.SuccessResponse{ID: instructorID, Message: "Successfully added instructor"})
}

func (impl *addInstructorByInstituteIDImpl) validateAddInstructor(params instructor.AddInstructorByInstituteIDParams) (bool, middleware.Responder) {
	valid := validators.AddInstructorByInstituteIDValidator(params)
	if !valid {
		return false, instructor.NewAddInstructorByInstituteIDBadRequest().WithPayload("Invalid Parameters")
	}
	_, err := impl.instituteProvider.Get(params.HTTPRequest.Context(), params.InstituteID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return false, instructor.NewAddInstructorByInstituteIDBadRequest().WithPayload("Institute not found")
		}
		log.Error().Err(err).Msg("Unable to fetch Institute")
		return false, instructor.NewAddInstructorByInstituteIDInternalServerError().WithPayload("Unable to fetch Institute")
	}
	return true, nil
}
