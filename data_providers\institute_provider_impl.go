package data_providers

import (
	"context"
	"eddyowl-backend/constants"
	"eddyowl-backend/entities"
	"errors"
	"time"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type instituteProvider struct {
	mongoClient *mongo.Client
	dbName      string
	tracer      trace.Tracer
}

func (i *instituteProvider) Add(ctx context.Context, institute *entities.Institute) (string, error) {
	ctx, span := i.tracer.Start(ctx, "InstituteProvider : Add")
	defer span.End()

	if institute.ID == nil {
		return constants.EmptyString, errors.New("institute ID is required")
	}

	_, err := i.mongoClient.Database(i.dbName).
		Collection(constants.MongoDBCollectionInstitutes).
		InsertOne(ctx, institute)
	if err != nil {
		log.Error().Msg(err.Error())
		return constants.EmptyString, err
	}
	return *institute.ID, nil
}

func (i *instituteProvider) Get(ctx context.Context, instituteId string) (*entities.Institute, error) {
	ctx, span := i.tracer.Start(ctx, "InstituteProvider : Get")
	defer span.End()
	institute := &entities.Institute{}
	err := i.mongoClient.Database(i.dbName).Collection(constants.MongoDBCollectionInstitutes).FindOne(ctx, bson.D{{"_id", instituteId}}).Decode(institute)
	if err != nil {
		log.Error().Msg(err.Error())
		return nil, err
	}
	return institute, nil
}

func (i *instituteProvider) Edit(ctx context.Context, instituteId string, institute *entities.Institute) error {
	ctx, span := i.tracer.Start(ctx, "InstituteProvider : Edit")
	defer span.End()
	now := time.Now()
	institute.UpdatedAt = &now
	institute.ID = nil
	res, err := i.mongoClient.Database(i.dbName).Collection(constants.MongoDBCollectionInstitutes).UpdateOne(ctx, bson.D{{"_id", instituteId}}, bson.M{"$set": institute})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.MatchedCount != 1 {
		log.Error().Msg("Institute not found : " + instituteId)
		return mongo.ErrNoDocuments
	}
	return nil
}

func (i *instituteProvider) Delete(ctx context.Context, instituteId string, deletedBy string) error {
	ctx, span := i.tracer.Start(ctx, "InstituteProvider : Delete")
	defer span.End()
	institute, err := i.Get(ctx, instituteId)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	currentTime := time.Now()
	institute.DeletedAt = &currentTime
	institute.DeletedBy = &deletedBy
	_, err = i.mongoClient.Database(i.dbName).Collection(constants.MongoDBCollectionInstitutesArcive).InsertOne(ctx, institute)
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	res, err := i.mongoClient.Database(i.dbName).Collection(constants.MongoDBCollectionInstitutes).DeleteOne(ctx, bson.D{{"_id", instituteId}})
	if err != nil {
		log.Error().Msg(err.Error())
		return err
	}
	if res.DeletedCount != 1 {
		log.Error().Msg("Institute not found : " + instituteId)
		return mongo.ErrNoDocuments
	}
	return nil
}

func NewInstituteProvider(mongoClient *mongo.Client, databaseName string, tracer trace.Tracer) InstituteProvider {
	return &instituteProvider{mongoClient: mongoClient, dbName: databaseName, tracer: tracer}
}
