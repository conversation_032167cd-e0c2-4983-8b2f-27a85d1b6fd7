// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassDetailsOKCode is the HTTP code returned for type GetClassDetailsOK
const GetClassDetailsOKCode int = 200

/*
GetClassDetailsOK Successful operation

swagger:response getClassDetailsOK
*/
type GetClassDetailsOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassDetails `json:"body,omitempty"`
}

// NewGetClassDetailsOK creates GetClassDetailsOK with default headers values
func NewGetClassDetailsOK() *GetClassDetailsOK {

	return &GetClassDetailsOK{}
}

// WithPayload adds the payload to the get class details o k response
func (o *GetClassDetailsOK) WithPayload(payload *models.ClassDetails) *GetClassDetailsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details o k response
func (o *GetClassDetailsOK) SetPayload(payload *models.ClassDetails) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassDetailsBadRequestCode is the HTTP code returned for type GetClassDetailsBadRequest
const GetClassDetailsBadRequestCode int = 400

/*
GetClassDetailsBadRequest Bad Request

swagger:response getClassDetailsBadRequest
*/
type GetClassDetailsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsBadRequest creates GetClassDetailsBadRequest with default headers values
func NewGetClassDetailsBadRequest() *GetClassDetailsBadRequest {

	return &GetClassDetailsBadRequest{}
}

// WithPayload adds the payload to the get class details bad request response
func (o *GetClassDetailsBadRequest) WithPayload(payload models.ErrorResponse) *GetClassDetailsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details bad request response
func (o *GetClassDetailsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassDetailsForbiddenCode is the HTTP code returned for type GetClassDetailsForbidden
const GetClassDetailsForbiddenCode int = 403

/*
GetClassDetailsForbidden Forbidden

swagger:response getClassDetailsForbidden
*/
type GetClassDetailsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsForbidden creates GetClassDetailsForbidden with default headers values
func NewGetClassDetailsForbidden() *GetClassDetailsForbidden {

	return &GetClassDetailsForbidden{}
}

// WithPayload adds the payload to the get class details forbidden response
func (o *GetClassDetailsForbidden) WithPayload(payload models.ErrorResponse) *GetClassDetailsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details forbidden response
func (o *GetClassDetailsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassDetailsNotFoundCode is the HTTP code returned for type GetClassDetailsNotFound
const GetClassDetailsNotFoundCode int = 404

/*
GetClassDetailsNotFound Not Found

swagger:response getClassDetailsNotFound
*/
type GetClassDetailsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsNotFound creates GetClassDetailsNotFound with default headers values
func NewGetClassDetailsNotFound() *GetClassDetailsNotFound {

	return &GetClassDetailsNotFound{}
}

// WithPayload adds the payload to the get class details not found response
func (o *GetClassDetailsNotFound) WithPayload(payload models.ErrorResponse) *GetClassDetailsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details not found response
func (o *GetClassDetailsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassDetailsTooManyRequestsCode is the HTTP code returned for type GetClassDetailsTooManyRequests
const GetClassDetailsTooManyRequestsCode int = 429

/*
GetClassDetailsTooManyRequests Too Many Requests

swagger:response getClassDetailsTooManyRequests
*/
type GetClassDetailsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsTooManyRequests creates GetClassDetailsTooManyRequests with default headers values
func NewGetClassDetailsTooManyRequests() *GetClassDetailsTooManyRequests {

	return &GetClassDetailsTooManyRequests{}
}

// WithPayload adds the payload to the get class details too many requests response
func (o *GetClassDetailsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassDetailsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details too many requests response
func (o *GetClassDetailsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassDetailsInternalServerErrorCode is the HTTP code returned for type GetClassDetailsInternalServerError
const GetClassDetailsInternalServerErrorCode int = 500

/*
GetClassDetailsInternalServerError Internal Server Error

swagger:response getClassDetailsInternalServerError
*/
type GetClassDetailsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsInternalServerError creates GetClassDetailsInternalServerError with default headers values
func NewGetClassDetailsInternalServerError() *GetClassDetailsInternalServerError {

	return &GetClassDetailsInternalServerError{}
}

// WithPayload adds the payload to the get class details internal server error response
func (o *GetClassDetailsInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassDetailsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details internal server error response
func (o *GetClassDetailsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassDetailsServiceUnavailableCode is the HTTP code returned for type GetClassDetailsServiceUnavailable
const GetClassDetailsServiceUnavailableCode int = 503

/*
GetClassDetailsServiceUnavailable Service Unvailable

swagger:response getClassDetailsServiceUnavailable
*/
type GetClassDetailsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassDetailsServiceUnavailable creates GetClassDetailsServiceUnavailable with default headers values
func NewGetClassDetailsServiceUnavailable() *GetClassDetailsServiceUnavailable {

	return &GetClassDetailsServiceUnavailable{}
}

// WithPayload adds the payload to the get class details service unavailable response
func (o *GetClassDetailsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassDetailsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class details service unavailable response
func (o *GetClassDetailsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassDetailsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
