package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/folder"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/go-openapi/strfmt"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getAllFoldersImpl struct {
	folderProvider    data_providers.FolderProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

func NewGetAllFoldersHandler(folderProvider data_providers.FolderProvider, userRolesProvider data_providers.UserRolesProvider, tracer trace.Tracer) folder.GetAllFoldersHandler {
	return &getAllFoldersImpl{
		folderProvider:    folderProvider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

func (impl *getAllFoldersImpl) Handle(params folder.GetAllFoldersParams, principal interface{}) middleware.Responder {
	ctx := params.HTTPRequest.Context()
	ctx, span := impl.tracer.Start(ctx, "GetAllFoldersHandler : Handle")
	defer span.End()

	// Extract user ID from principal
	userID := principal.(string)

	// Check user role and institute access - allow Admin, Instructor, and Student roles
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, userID, params.InstituteID, []int{constants.AdminRole, constants.InstructorRole, constants.StudentRole})
	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return folder.NewGetAllFoldersBadRequest().WithPayload("Unauthorized")
	}

	// Get all folders for the institute
	folders, err := impl.folderProvider.GetAll(ctx, params.InstituteID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get folders")
		return folder.NewGetAllFoldersBadRequest().WithPayload("Failed to get folders")
	}

	// Convert entities to response models
	var responseFolders []*models.Folder
	if folders != nil {
		for _, folderEntity := range *folders {
			responseFolder := &models.Folder{
				ID:        *folderEntity.ID,
				Name:      *folderEntity.Name,
				CreatedAt: strfmt.DateTime(*folderEntity.CreatedAt),
			}
			responseFolders = append(responseFolders, responseFolder)
		}
	}

	// If no folders found, return empty array
	if responseFolders == nil {
		responseFolders = make([]*models.Folder, 0)
	}

	log.Info().Str("institute_id", params.InstituteID).Int("folder_count", len(responseFolders)).Msg("Folders retrieved successfully")

	return folder.NewGetAllFoldersOK().WithPayload(responseFolders)
}
