// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassDetailsHandlerFunc turns a function with the right signature into a get class details handler
type GetClassDetailsHandlerFunc func(GetClassDetailsParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassDetailsHandlerFunc) Handle(params GetClassDetailsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassDetailsHandler interface for that can handle valid get class details params
type GetClassDetailsHandler interface {
	Handle(GetClassDetailsParams, interface{}) middleware.Responder
}

// NewGetClassDetails creates a new http.Handler for the get class details operation
func NewGetClassDetails(ctx *middleware.Context, handler GetClassDetailsHandler) *GetClassDetails {
	return &GetClassDetails{Context: ctx, Handler: handler}
}

/*
	GetClassDetails swagger:route GET /institute/{instituteId}/classdetails stats getClassDetails

# Get Class Details

Get Class Details
*/
type GetClassDetails struct {
	Context *middleware.Context
	Handler GetClassDetailsHandler
}

func (o *GetClassDetails) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassDetailsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
