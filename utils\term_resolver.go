package utils

import (
	"context"
	"eddyowl-backend/data_providers"
	"errors"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrTermNotFound = errors.New("term not found")
)

// ResolveTerm attempts to get a term either by ID or falls back to current term
func ResolveTerm(ctx context.Context, termProvider data_providers.TermProvider, instituteID string, termID *string) (string, error) {
	if termID != nil {
		term, err := termProvider.Get(ctx, *termID, instituteID)
		if err != nil {
			log.Error().Err(err).
				Str("instituteID", instituteID).
				Str("termID", *termID).
				Msg("Failed to get term by ID")

			if errors.Is(err, mongo.ErrNoDocuments) {
				return "", ErrTermNotFound
			}
			return "", err
		}
		if term != nil {
			return *term.ID, nil
		}
	}

	currentTerm, err := termProvider.GetCurrent(ctx, instituteID)
	if err != nil {
		log.Error().Err(err).
			Str("instituteID", instituteID).
			Msg("Failed to get current term")

		if errors.Is(err, data_providers.ErrNoCurrentTerm) {
			return "", ErrTermNotFound
		}
		return "", err
	}

	if currentTerm != nil {
		return *currentTerm.ID, nil
	}

	return "", ErrTermNotFound
}
