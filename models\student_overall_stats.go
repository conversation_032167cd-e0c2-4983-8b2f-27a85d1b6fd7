// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"

	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// StudentOverallStats student overall stats
//
// swagger:model StudentOverallStats
type StudentOverallStats struct {

	// average student performance
	AverageStudentPerformance float32 `json:"averageStudentPerformance,omitempty"`

	// total assignments solved
	TotalAssignmentsSolved int32 `json:"totalAssignmentsSolved,omitempty"`
}

// Validate validates this student overall stats
func (m *StudentOverallStats) Validate(formats strfmt.Registry) error {
	return nil
}

// ContextValidate validates this student overall stats based on context it is used
func (m *StudentOverallStats) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	return nil
}

// MarshalBinary interface implementation
func (m *StudentOverallStats) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *StudentOverallStats) UnmarshalBinary(b []byte) error {
	var res StudentOverallStats
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
