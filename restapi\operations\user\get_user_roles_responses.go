// Code generated by go-swagger; DO NOT EDIT.

package user

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetUserRolesOKCode is the HTTP code returned for type GetUserRolesOK
const GetUserRolesOKCode int = 200

/*
GetUserRolesOK Successful operation

swagger:response getUserRolesOK
*/
type GetUserRolesOK struct {

	/*
	  In: Body
	*/
	Payload *models.UserRoles `json:"body,omitempty"`
}

// NewGetUserRolesOK creates GetUserRolesOK with default headers values
func NewGetUserRolesOK() *GetUserRolesOK {

	return &GetUserRolesOK{}
}

// WithPayload adds the payload to the get user roles o k response
func (o *GetUserRolesOK) WithPayload(payload *models.UserRoles) *GetUserRolesOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles o k response
func (o *GetUserRolesOK) SetPayload(payload *models.UserRoles) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetUserRolesBadRequestCode is the HTTP code returned for type GetUserRolesBadRequest
const GetUserRolesBadRequestCode int = 400

/*
GetUserRolesBadRequest Bad Request

swagger:response getUserRolesBadRequest
*/
type GetUserRolesBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesBadRequest creates GetUserRolesBadRequest with default headers values
func NewGetUserRolesBadRequest() *GetUserRolesBadRequest {

	return &GetUserRolesBadRequest{}
}

// WithPayload adds the payload to the get user roles bad request response
func (o *GetUserRolesBadRequest) WithPayload(payload models.ErrorResponse) *GetUserRolesBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles bad request response
func (o *GetUserRolesBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesUnauthorizedCode is the HTTP code returned for type GetUserRolesUnauthorized
const GetUserRolesUnauthorizedCode int = 401

/*
GetUserRolesUnauthorized Unauthorized

swagger:response getUserRolesUnauthorized
*/
type GetUserRolesUnauthorized struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesUnauthorized creates GetUserRolesUnauthorized with default headers values
func NewGetUserRolesUnauthorized() *GetUserRolesUnauthorized {

	return &GetUserRolesUnauthorized{}
}

// WithPayload adds the payload to the get user roles unauthorized response
func (o *GetUserRolesUnauthorized) WithPayload(payload models.ErrorResponse) *GetUserRolesUnauthorized {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles unauthorized response
func (o *GetUserRolesUnauthorized) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesUnauthorized) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(401)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesForbiddenCode is the HTTP code returned for type GetUserRolesForbidden
const GetUserRolesForbiddenCode int = 403

/*
GetUserRolesForbidden Forbidden

swagger:response getUserRolesForbidden
*/
type GetUserRolesForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesForbidden creates GetUserRolesForbidden with default headers values
func NewGetUserRolesForbidden() *GetUserRolesForbidden {

	return &GetUserRolesForbidden{}
}

// WithPayload adds the payload to the get user roles forbidden response
func (o *GetUserRolesForbidden) WithPayload(payload models.ErrorResponse) *GetUserRolesForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles forbidden response
func (o *GetUserRolesForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesNotFoundCode is the HTTP code returned for type GetUserRolesNotFound
const GetUserRolesNotFoundCode int = 404

/*
GetUserRolesNotFound Not Found

swagger:response getUserRolesNotFound
*/
type GetUserRolesNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesNotFound creates GetUserRolesNotFound with default headers values
func NewGetUserRolesNotFound() *GetUserRolesNotFound {

	return &GetUserRolesNotFound{}
}

// WithPayload adds the payload to the get user roles not found response
func (o *GetUserRolesNotFound) WithPayload(payload models.ErrorResponse) *GetUserRolesNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles not found response
func (o *GetUserRolesNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesTooManyRequestsCode is the HTTP code returned for type GetUserRolesTooManyRequests
const GetUserRolesTooManyRequestsCode int = 429

/*
GetUserRolesTooManyRequests Too Many Requests

swagger:response getUserRolesTooManyRequests
*/
type GetUserRolesTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesTooManyRequests creates GetUserRolesTooManyRequests with default headers values
func NewGetUserRolesTooManyRequests() *GetUserRolesTooManyRequests {

	return &GetUserRolesTooManyRequests{}
}

// WithPayload adds the payload to the get user roles too many requests response
func (o *GetUserRolesTooManyRequests) WithPayload(payload models.ErrorResponse) *GetUserRolesTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles too many requests response
func (o *GetUserRolesTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesInternalServerErrorCode is the HTTP code returned for type GetUserRolesInternalServerError
const GetUserRolesInternalServerErrorCode int = 500

/*
GetUserRolesInternalServerError Internal Server Error

swagger:response getUserRolesInternalServerError
*/
type GetUserRolesInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesInternalServerError creates GetUserRolesInternalServerError with default headers values
func NewGetUserRolesInternalServerError() *GetUserRolesInternalServerError {

	return &GetUserRolesInternalServerError{}
}

// WithPayload adds the payload to the get user roles internal server error response
func (o *GetUserRolesInternalServerError) WithPayload(payload models.ErrorResponse) *GetUserRolesInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles internal server error response
func (o *GetUserRolesInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetUserRolesServiceUnavailableCode is the HTTP code returned for type GetUserRolesServiceUnavailable
const GetUserRolesServiceUnavailableCode int = 503

/*
GetUserRolesServiceUnavailable Service Unvailable

swagger:response getUserRolesServiceUnavailable
*/
type GetUserRolesServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetUserRolesServiceUnavailable creates GetUserRolesServiceUnavailable with default headers values
func NewGetUserRolesServiceUnavailable() *GetUserRolesServiceUnavailable {

	return &GetUserRolesServiceUnavailable{}
}

// WithPayload adds the payload to the get user roles service unavailable response
func (o *GetUserRolesServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetUserRolesServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get user roles service unavailable response
func (o *GetUserRolesServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetUserRolesServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
