// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassStudentsBySectionsOKCode is the HTTP code returned for type GetClassStudentsBySectionsOK
const GetClassStudentsBySectionsOKCode int = 200

/*
GetClassStudentsBySectionsOK Successful operation

swagger:response getClassStudentsBySectionsOK
*/
type GetClassStudentsBySectionsOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassStudentsBySections `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsOK creates GetClassStudentsBySectionsOK with default headers values
func NewGetClassStudentsBySectionsOK() *GetClassStudentsBySectionsOK {

	return &GetClassStudentsBySectionsOK{}
}

// WithPayload adds the payload to the get class students by sections o k response
func (o *GetClassStudentsBySectionsOK) WithPayload(payload *models.ClassStudentsBySections) *GetClassStudentsBySectionsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections o k response
func (o *GetClassStudentsBySectionsOK) SetPayload(payload *models.ClassStudentsBySections) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassStudentsBySectionsBadRequestCode is the HTTP code returned for type GetClassStudentsBySectionsBadRequest
const GetClassStudentsBySectionsBadRequestCode int = 400

/*
GetClassStudentsBySectionsBadRequest Bad Request

swagger:response getClassStudentsBySectionsBadRequest
*/
type GetClassStudentsBySectionsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsBadRequest creates GetClassStudentsBySectionsBadRequest with default headers values
func NewGetClassStudentsBySectionsBadRequest() *GetClassStudentsBySectionsBadRequest {

	return &GetClassStudentsBySectionsBadRequest{}
}

// WithPayload adds the payload to the get class students by sections bad request response
func (o *GetClassStudentsBySectionsBadRequest) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections bad request response
func (o *GetClassStudentsBySectionsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStudentsBySectionsForbiddenCode is the HTTP code returned for type GetClassStudentsBySectionsForbidden
const GetClassStudentsBySectionsForbiddenCode int = 403

/*
GetClassStudentsBySectionsForbidden Forbidden

swagger:response getClassStudentsBySectionsForbidden
*/
type GetClassStudentsBySectionsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsForbidden creates GetClassStudentsBySectionsForbidden with default headers values
func NewGetClassStudentsBySectionsForbidden() *GetClassStudentsBySectionsForbidden {

	return &GetClassStudentsBySectionsForbidden{}
}

// WithPayload adds the payload to the get class students by sections forbidden response
func (o *GetClassStudentsBySectionsForbidden) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections forbidden response
func (o *GetClassStudentsBySectionsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStudentsBySectionsNotFoundCode is the HTTP code returned for type GetClassStudentsBySectionsNotFound
const GetClassStudentsBySectionsNotFoundCode int = 404

/*
GetClassStudentsBySectionsNotFound Not Found

swagger:response getClassStudentsBySectionsNotFound
*/
type GetClassStudentsBySectionsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsNotFound creates GetClassStudentsBySectionsNotFound with default headers values
func NewGetClassStudentsBySectionsNotFound() *GetClassStudentsBySectionsNotFound {

	return &GetClassStudentsBySectionsNotFound{}
}

// WithPayload adds the payload to the get class students by sections not found response
func (o *GetClassStudentsBySectionsNotFound) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections not found response
func (o *GetClassStudentsBySectionsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStudentsBySectionsTooManyRequestsCode is the HTTP code returned for type GetClassStudentsBySectionsTooManyRequests
const GetClassStudentsBySectionsTooManyRequestsCode int = 429

/*
GetClassStudentsBySectionsTooManyRequests Too Many Requests

swagger:response getClassStudentsBySectionsTooManyRequests
*/
type GetClassStudentsBySectionsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsTooManyRequests creates GetClassStudentsBySectionsTooManyRequests with default headers values
func NewGetClassStudentsBySectionsTooManyRequests() *GetClassStudentsBySectionsTooManyRequests {

	return &GetClassStudentsBySectionsTooManyRequests{}
}

// WithPayload adds the payload to the get class students by sections too many requests response
func (o *GetClassStudentsBySectionsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections too many requests response
func (o *GetClassStudentsBySectionsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStudentsBySectionsInternalServerErrorCode is the HTTP code returned for type GetClassStudentsBySectionsInternalServerError
const GetClassStudentsBySectionsInternalServerErrorCode int = 500

/*
GetClassStudentsBySectionsInternalServerError Internal Server Error

swagger:response getClassStudentsBySectionsInternalServerError
*/
type GetClassStudentsBySectionsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsInternalServerError creates GetClassStudentsBySectionsInternalServerError with default headers values
func NewGetClassStudentsBySectionsInternalServerError() *GetClassStudentsBySectionsInternalServerError {

	return &GetClassStudentsBySectionsInternalServerError{}
}

// WithPayload adds the payload to the get class students by sections internal server error response
func (o *GetClassStudentsBySectionsInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections internal server error response
func (o *GetClassStudentsBySectionsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassStudentsBySectionsServiceUnavailableCode is the HTTP code returned for type GetClassStudentsBySectionsServiceUnavailable
const GetClassStudentsBySectionsServiceUnavailableCode int = 503

/*
GetClassStudentsBySectionsServiceUnavailable Service Unvailable

swagger:response getClassStudentsBySectionsServiceUnavailable
*/
type GetClassStudentsBySectionsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassStudentsBySectionsServiceUnavailable creates GetClassStudentsBySectionsServiceUnavailable with default headers values
func NewGetClassStudentsBySectionsServiceUnavailable() *GetClassStudentsBySectionsServiceUnavailable {

	return &GetClassStudentsBySectionsServiceUnavailable{}
}

// WithPayload adds the payload to the get class students by sections service unavailable response
func (o *GetClassStudentsBySectionsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassStudentsBySectionsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class students by sections service unavailable response
func (o *GetClassStudentsBySectionsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassStudentsBySectionsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
