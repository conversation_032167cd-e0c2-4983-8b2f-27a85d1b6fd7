package handlers

import (
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/stats"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type getClassStatsImpl struct {
	statsProvider data_providers.StatsProvider
	termProvider  data_providers.TermProvider
	tracer        trace.Tracer
}

func NewGetClassStatsHandler(
	statsProvider data_providers.StatsProvider,
	termProvider data_providers.TermProvider,
	tracer trace.Tracer,
) stats.GetClassStatsHandler {
	return &getClassStatsImpl{
		statsProvider: statsProvider,
		termProvider:  termProvider,
		tracer:        tracer,
	}
}

func (impl *getClassStatsImpl) Handle(params stats.GetClassStatsParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : GetClassStatsHandler")
	defer span.End()

	// Resolve current term
	termID, err := utils.ResolveTerm(ctx, impl.termProvider, params.InstituteID, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve term")
		return stats.NewGetClassStatsInternalServerError().WithPayload("Unable to resolve term")
	}

	// Get grade stats
	gradeStats, err := impl.statsProvider.GetGradeOverallStats(ctx, params.InstituteID, termID, params.Class)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get grade stats")
		return stats.NewGetClassStatsInternalServerError().WithPayload("Failed to fetch class stats")
	}

	// Transform the data into ClassStats model
	response := &models.AllStats{
		Students:                  gradeStats.Students,
		Assessments:               gradeStats.Assessments,
		OverallStudentPerformance: int32(gradeStats.AverageSubmissionPercentage),
	}

	return stats.NewGetClassStatsOK().WithPayload(response)
}
