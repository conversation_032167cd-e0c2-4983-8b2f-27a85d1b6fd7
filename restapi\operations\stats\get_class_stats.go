// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassStatsHandlerFunc turns a function with the right signature into a get class stats handler
type GetClassStatsHandlerFunc func(GetClassStatsParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn GetClassStatsHandlerFunc) Handle(params GetClassStatsParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassStatsHandler interface for that can handle valid get class stats params
type GetClassStatsHandler interface {
	Handle(GetClassStatsParams, interface{}) middleware.Responder
}

// NewGetClassStats creates a new http.Handler for the get class stats operation
func NewGetClassStats(ctx *middleware.Context, handler GetClassStatsHandler) *GetClassStats {
	return &GetClassStats{Context: ctx, Handler: handler}
}

/*
	GetClassStats swagger:route GET /institute/{instituteId}/class/{class}/allstats stats getClassStats

# Get Class Stats

Get Class Stats
*/
type GetClassStats struct {
	Context *middleware.Context
	Handler GetClassStatsHandler
}

func (o *GetClassStats) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassStatsParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
