package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/entities"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/submission"
	"eddyowl-backend/utils"
	"errors"
	"time"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.opentelemetry.io/otel/trace"
)

type editSubmissionImpl struct {
	submissionProvider data_providers.SubmissionProvider
	assignmentProvider data_providers.AssignmentProvider
	userRolesProvider  data_providers.UserRolesProvider
	tracer             trace.Tracer
}

func NewEditSubmissionHandler(
	submissionProvider data_providers.SubmissionProvider,
	assignmentProvider data_providers.AssignmentProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) submission.EditSubmissionHandler {
	return &editSubmissionImpl{
		submissionProvider: submissionProvider,
		assignmentProvider: assignmentProvider,
		userRolesProvider:  userRolesProvider,
		tracer:             tracer,
	}
}

func (impl *editSubmissionImpl) Handle(params submission.EditSubmissionParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : EditSubmissionHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole, constants.InstructorRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return submission.NewEditSubmissionForbidden().WithPayload("Unauthorized")
	}

	valid, validateResp := impl.editSubmissionValidator(params)
	if !valid {
		return validateResp
	}

	submissionEntity, err := impl.submissionProvider.GetForEdit(ctx, params.InstituteID, params.AssignmentID, params.StudentID)
	if err != nil {
		log.Error().Msg(err.Error())
		return submission.NewEditSubmissionInternalServerError().WithPayload("Unable to fetch submission")
	}

	if params.Submission.StudentResponses != nil {
		studentResponses := make([]entities.StudentResponse, len(params.Submission.StudentResponses))
		totalScore := float32(0)

		for i, resp := range params.Submission.StudentResponses {
			editedScore := float32(resp.Score)
			studentResponses[i] = entities.StudentResponse{
				QuestionNumber: int(resp.QuestionNumber),
				Response:       &resp.StudentResponse,
				Score:          editedScore,
				Feedback:       &resp.Feedback,
			}
			totalScore += editedScore
		}
		submissionEntity.StudentResponses = &studentResponses
		submissionEntity.TotalScore = &totalScore
		principal := principal.(string)
		timeNow := time.Now()
		submissionEntity.UpdatedBy = &principal
		submissionEntity.UpdatedAt = &timeNow
	}

	id, err := impl.submissionProvider.Edit(ctx, params.InstituteID, params.AssignmentID, params.StudentID, submissionEntity)
	if err != nil {
		return submission.NewEditSubmissionInternalServerError().WithPayload("Unable to edit submission")
	}

	return submission.NewEditSubmissionOK().WithPayload(
		&models.SuccessResponse{
			ID:      id,
			Message: "Successfully edited submission",
		},
	)
}

func (impl *editSubmissionImpl) editSubmissionValidator(params submission.EditSubmissionParams) (bool, middleware.Responder) {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "editSubmissionValidator")
	defer span.End()
	if params.InstituteID == constants.EmptyString {
		return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Institute ID")
	}
	if params.AssignmentID == constants.EmptyString {
		return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Assignment ID")
	}
	if params.StudentID == constants.EmptyString {
		return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Student ID")
	}
	_, err := impl.assignmentProvider.Get(ctx, params.AssignmentID, params.InstituteID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Assignment ID")
		}
		return false, submission.NewEditSubmissionInternalServerError().WithPayload("Unable to fetch Assignment")
	}
	if params.Submission == nil {
		return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Submission Parameters")
	}
	if params.Submission.StudentResponses == nil {
		return false, submission.NewEditSubmissionBadRequest().WithPayload("Invalid Submission Parameters")
	}

	return true, nil
}
