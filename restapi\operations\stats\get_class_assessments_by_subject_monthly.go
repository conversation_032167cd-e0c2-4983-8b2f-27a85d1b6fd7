// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// GetClassAssessmentsBySubjectMonthlyHandlerFunc turns a function with the right signature into a get class assessments by subject monthly handler
type GetClassAssessmentsBySubjectMonthlyHandlerFunc func(GetClassAssessmentsBySubjectMonthlyParams, interface{}) middleware.Responder

// Handle executing the request and returning a response
func (fn GetClassAssessmentsBySubjectMonthlyHandlerFunc) Handle(params GetClassAssessmentsBySubjectMonthlyParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// GetClassAssessmentsBySubjectMonthlyHandler interface for that can handle valid get class assessments by subject monthly params
type GetClassAssessmentsBySubjectMonthlyHandler interface {
	Handle(GetClassAssessmentsBySubjectMonthlyParams, interface{}) middleware.Responder
}

// NewGetClassAssessmentsBySubjectMonthly creates a new http.Handler for the get class assessments by subject monthly operation
func NewGetClassAssessmentsBySubjectMonthly(ctx *middleware.Context, handler GetClassAssessmentsBySubjectMonthlyHandler) *GetClassAssessmentsBySubjectMonthly {
	return &GetClassAssessmentsBySubjectMonthly{Context: ctx, Handler: handler}
}

/*
	GetClassAssessmentsBySubjectMonthly swagger:route GET /institute/{instituteId}/class/{class}/assessmentsbysub/monthly stats getClassAssessmentsBySubjectMonthly

# Get Class Assessments By Subject Monthly

Get Class Assessments By Subject Monthly for the last 12 months.
*/
type GetClassAssessmentsBySubjectMonthly struct {
	Context *middleware.Context
	Handler GetClassAssessmentsBySubjectMonthlyHandler
}

func (o *GetClassAssessmentsBySubjectMonthly) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewGetClassAssessmentsBySubjectMonthlyParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
