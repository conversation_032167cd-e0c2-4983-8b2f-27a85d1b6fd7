// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetMonthlyAssessmentsOKCode is the HTTP code returned for type GetMonthlyAssessmentsOK
const GetMonthlyAssessmentsOKCode int = 200

/*
GetMonthlyAssessmentsOK Successful operation

swagger:response getMonthlyAssessmentsOK
*/
type GetMonthlyAssessmentsOK struct {

	/*
	  In: Body
	*/
	Payload *models.MonthlyAssessmentsGraded `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsOK creates GetMonthlyAssessmentsOK with default headers values
func NewGetMonthlyAssessmentsOK() *GetMonthlyAssessmentsOK {

	return &GetMonthlyAssessmentsOK{}
}

// WithPayload adds the payload to the get monthly assessments o k response
func (o *GetMonthlyAssessmentsOK) WithPayload(payload *models.MonthlyAssessmentsGraded) *GetMonthlyAssessmentsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments o k response
func (o *GetMonthlyAssessmentsOK) SetPayload(payload *models.MonthlyAssessmentsGraded) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetMonthlyAssessmentsBadRequestCode is the HTTP code returned for type GetMonthlyAssessmentsBadRequest
const GetMonthlyAssessmentsBadRequestCode int = 400

/*
GetMonthlyAssessmentsBadRequest Bad Request

swagger:response getMonthlyAssessmentsBadRequest
*/
type GetMonthlyAssessmentsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsBadRequest creates GetMonthlyAssessmentsBadRequest with default headers values
func NewGetMonthlyAssessmentsBadRequest() *GetMonthlyAssessmentsBadRequest {

	return &GetMonthlyAssessmentsBadRequest{}
}

// WithPayload adds the payload to the get monthly assessments bad request response
func (o *GetMonthlyAssessmentsBadRequest) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments bad request response
func (o *GetMonthlyAssessmentsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetMonthlyAssessmentsForbiddenCode is the HTTP code returned for type GetMonthlyAssessmentsForbidden
const GetMonthlyAssessmentsForbiddenCode int = 403

/*
GetMonthlyAssessmentsForbidden Forbidden

swagger:response getMonthlyAssessmentsForbidden
*/
type GetMonthlyAssessmentsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsForbidden creates GetMonthlyAssessmentsForbidden with default headers values
func NewGetMonthlyAssessmentsForbidden() *GetMonthlyAssessmentsForbidden {

	return &GetMonthlyAssessmentsForbidden{}
}

// WithPayload adds the payload to the get monthly assessments forbidden response
func (o *GetMonthlyAssessmentsForbidden) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments forbidden response
func (o *GetMonthlyAssessmentsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetMonthlyAssessmentsNotFoundCode is the HTTP code returned for type GetMonthlyAssessmentsNotFound
const GetMonthlyAssessmentsNotFoundCode int = 404

/*
GetMonthlyAssessmentsNotFound Not Found

swagger:response getMonthlyAssessmentsNotFound
*/
type GetMonthlyAssessmentsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsNotFound creates GetMonthlyAssessmentsNotFound with default headers values
func NewGetMonthlyAssessmentsNotFound() *GetMonthlyAssessmentsNotFound {

	return &GetMonthlyAssessmentsNotFound{}
}

// WithPayload adds the payload to the get monthly assessments not found response
func (o *GetMonthlyAssessmentsNotFound) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments not found response
func (o *GetMonthlyAssessmentsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetMonthlyAssessmentsTooManyRequestsCode is the HTTP code returned for type GetMonthlyAssessmentsTooManyRequests
const GetMonthlyAssessmentsTooManyRequestsCode int = 429

/*
GetMonthlyAssessmentsTooManyRequests Too Many Requests

swagger:response getMonthlyAssessmentsTooManyRequests
*/
type GetMonthlyAssessmentsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsTooManyRequests creates GetMonthlyAssessmentsTooManyRequests with default headers values
func NewGetMonthlyAssessmentsTooManyRequests() *GetMonthlyAssessmentsTooManyRequests {

	return &GetMonthlyAssessmentsTooManyRequests{}
}

// WithPayload adds the payload to the get monthly assessments too many requests response
func (o *GetMonthlyAssessmentsTooManyRequests) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments too many requests response
func (o *GetMonthlyAssessmentsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetMonthlyAssessmentsInternalServerErrorCode is the HTTP code returned for type GetMonthlyAssessmentsInternalServerError
const GetMonthlyAssessmentsInternalServerErrorCode int = 500

/*
GetMonthlyAssessmentsInternalServerError Internal Server Error

swagger:response getMonthlyAssessmentsInternalServerError
*/
type GetMonthlyAssessmentsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsInternalServerError creates GetMonthlyAssessmentsInternalServerError with default headers values
func NewGetMonthlyAssessmentsInternalServerError() *GetMonthlyAssessmentsInternalServerError {

	return &GetMonthlyAssessmentsInternalServerError{}
}

// WithPayload adds the payload to the get monthly assessments internal server error response
func (o *GetMonthlyAssessmentsInternalServerError) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments internal server error response
func (o *GetMonthlyAssessmentsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetMonthlyAssessmentsServiceUnavailableCode is the HTTP code returned for type GetMonthlyAssessmentsServiceUnavailable
const GetMonthlyAssessmentsServiceUnavailableCode int = 503

/*
GetMonthlyAssessmentsServiceUnavailable Service Unvailable

swagger:response getMonthlyAssessmentsServiceUnavailable
*/
type GetMonthlyAssessmentsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetMonthlyAssessmentsServiceUnavailable creates GetMonthlyAssessmentsServiceUnavailable with default headers values
func NewGetMonthlyAssessmentsServiceUnavailable() *GetMonthlyAssessmentsServiceUnavailable {

	return &GetMonthlyAssessmentsServiceUnavailable{}
}

// WithPayload adds the payload to the get monthly assessments service unavailable response
func (o *GetMonthlyAssessmentsServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetMonthlyAssessmentsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get monthly assessments service unavailable response
func (o *GetMonthlyAssessmentsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetMonthlyAssessmentsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
