// Code generated by go-swagger; DO NOT EDIT.

package stats

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// GetClassAssessmentsBySubjectMonthlyOKCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyOK
const GetClassAssessmentsBySubjectMonthlyOKCode int = 200

/*
GetClassAssessmentsBySubjectMonthlyOK Successful operation

swagger:response getClassAssessmentsBySubjectMonthlyOK
*/
type GetClassAssessmentsBySubjectMonthlyOK struct {

	/*
	  In: Body
	*/
	Payload *models.ClassAggMonthlyAssessmentsBySubject `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyOK creates GetClassAssessmentsBySubjectMonthlyOK with default headers values
func NewGetClassAssessmentsBySubjectMonthlyOK() *GetClassAssessmentsBySubjectMonthlyOK {

	return &GetClassAssessmentsBySubjectMonthlyOK{}
}

// WithPayload adds the payload to the get class assessments by subject monthly o k response
func (o *GetClassAssessmentsBySubjectMonthlyOK) WithPayload(payload *models.ClassAggMonthlyAssessmentsBySubject) *GetClassAssessmentsBySubjectMonthlyOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly o k response
func (o *GetClassAssessmentsBySubjectMonthlyOK) SetPayload(payload *models.ClassAggMonthlyAssessmentsBySubject) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// GetClassAssessmentsBySubjectMonthlyBadRequestCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyBadRequest
const GetClassAssessmentsBySubjectMonthlyBadRequestCode int = 400

/*
GetClassAssessmentsBySubjectMonthlyBadRequest Bad Request

swagger:response getClassAssessmentsBySubjectMonthlyBadRequest
*/
type GetClassAssessmentsBySubjectMonthlyBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyBadRequest creates GetClassAssessmentsBySubjectMonthlyBadRequest with default headers values
func NewGetClassAssessmentsBySubjectMonthlyBadRequest() *GetClassAssessmentsBySubjectMonthlyBadRequest {

	return &GetClassAssessmentsBySubjectMonthlyBadRequest{}
}

// WithPayload adds the payload to the get class assessments by subject monthly bad request response
func (o *GetClassAssessmentsBySubjectMonthlyBadRequest) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly bad request response
func (o *GetClassAssessmentsBySubjectMonthlyBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectMonthlyForbiddenCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyForbidden
const GetClassAssessmentsBySubjectMonthlyForbiddenCode int = 403

/*
GetClassAssessmentsBySubjectMonthlyForbidden Forbidden

swagger:response getClassAssessmentsBySubjectMonthlyForbidden
*/
type GetClassAssessmentsBySubjectMonthlyForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyForbidden creates GetClassAssessmentsBySubjectMonthlyForbidden with default headers values
func NewGetClassAssessmentsBySubjectMonthlyForbidden() *GetClassAssessmentsBySubjectMonthlyForbidden {

	return &GetClassAssessmentsBySubjectMonthlyForbidden{}
}

// WithPayload adds the payload to the get class assessments by subject monthly forbidden response
func (o *GetClassAssessmentsBySubjectMonthlyForbidden) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly forbidden response
func (o *GetClassAssessmentsBySubjectMonthlyForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectMonthlyNotFoundCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyNotFound
const GetClassAssessmentsBySubjectMonthlyNotFoundCode int = 404

/*
GetClassAssessmentsBySubjectMonthlyNotFound Not Found

swagger:response getClassAssessmentsBySubjectMonthlyNotFound
*/
type GetClassAssessmentsBySubjectMonthlyNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyNotFound creates GetClassAssessmentsBySubjectMonthlyNotFound with default headers values
func NewGetClassAssessmentsBySubjectMonthlyNotFound() *GetClassAssessmentsBySubjectMonthlyNotFound {

	return &GetClassAssessmentsBySubjectMonthlyNotFound{}
}

// WithPayload adds the payload to the get class assessments by subject monthly not found response
func (o *GetClassAssessmentsBySubjectMonthlyNotFound) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly not found response
func (o *GetClassAssessmentsBySubjectMonthlyNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectMonthlyTooManyRequestsCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyTooManyRequests
const GetClassAssessmentsBySubjectMonthlyTooManyRequestsCode int = 429

/*
GetClassAssessmentsBySubjectMonthlyTooManyRequests Too Many Requests

swagger:response getClassAssessmentsBySubjectMonthlyTooManyRequests
*/
type GetClassAssessmentsBySubjectMonthlyTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyTooManyRequests creates GetClassAssessmentsBySubjectMonthlyTooManyRequests with default headers values
func NewGetClassAssessmentsBySubjectMonthlyTooManyRequests() *GetClassAssessmentsBySubjectMonthlyTooManyRequests {

	return &GetClassAssessmentsBySubjectMonthlyTooManyRequests{}
}

// WithPayload adds the payload to the get class assessments by subject monthly too many requests response
func (o *GetClassAssessmentsBySubjectMonthlyTooManyRequests) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly too many requests response
func (o *GetClassAssessmentsBySubjectMonthlyTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectMonthlyInternalServerErrorCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyInternalServerError
const GetClassAssessmentsBySubjectMonthlyInternalServerErrorCode int = 500

/*
GetClassAssessmentsBySubjectMonthlyInternalServerError Internal Server Error

swagger:response getClassAssessmentsBySubjectMonthlyInternalServerError
*/
type GetClassAssessmentsBySubjectMonthlyInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyInternalServerError creates GetClassAssessmentsBySubjectMonthlyInternalServerError with default headers values
func NewGetClassAssessmentsBySubjectMonthlyInternalServerError() *GetClassAssessmentsBySubjectMonthlyInternalServerError {

	return &GetClassAssessmentsBySubjectMonthlyInternalServerError{}
}

// WithPayload adds the payload to the get class assessments by subject monthly internal server error response
func (o *GetClassAssessmentsBySubjectMonthlyInternalServerError) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly internal server error response
func (o *GetClassAssessmentsBySubjectMonthlyInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// GetClassAssessmentsBySubjectMonthlyServiceUnavailableCode is the HTTP code returned for type GetClassAssessmentsBySubjectMonthlyServiceUnavailable
const GetClassAssessmentsBySubjectMonthlyServiceUnavailableCode int = 503

/*
GetClassAssessmentsBySubjectMonthlyServiceUnavailable Service Unvailable

swagger:response getClassAssessmentsBySubjectMonthlyServiceUnavailable
*/
type GetClassAssessmentsBySubjectMonthlyServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewGetClassAssessmentsBySubjectMonthlyServiceUnavailable creates GetClassAssessmentsBySubjectMonthlyServiceUnavailable with default headers values
func NewGetClassAssessmentsBySubjectMonthlyServiceUnavailable() *GetClassAssessmentsBySubjectMonthlyServiceUnavailable {

	return &GetClassAssessmentsBySubjectMonthlyServiceUnavailable{}
}

// WithPayload adds the payload to the get class assessments by subject monthly service unavailable response
func (o *GetClassAssessmentsBySubjectMonthlyServiceUnavailable) WithPayload(payload models.ErrorResponse) *GetClassAssessmentsBySubjectMonthlyServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the get class assessments by subject monthly service unavailable response
func (o *GetClassAssessmentsBySubjectMonthlyServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *GetClassAssessmentsBySubjectMonthlyServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
