// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// EditAssignmentOKCode is the HTTP code returned for type EditAssignmentOK
const EditAssignmentOKCode int = 200

/*
EditAssignmentOK Successful operation

swagger:response editAssignmentOK
*/
type EditAssignmentOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewEditAssignmentOK creates EditAssignmentOK with default headers values
func NewEditAssignmentOK() *EditAssignmentOK {

	return &EditAssignmentOK{}
}

// WithPayload adds the payload to the edit assignment o k response
func (o *EditAssignmentOK) WithPayload(payload *models.SuccessResponse) *EditAssignmentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment o k response
func (o *EditAssignmentOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// EditAssignmentBadRequestCode is the HTTP code returned for type EditAssignmentBadRequest
const EditAssignmentBadRequestCode int = 400

/*
EditAssignmentBadRequest Bad Request

swagger:response editAssignmentBadRequest
*/
type EditAssignmentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentBadRequest creates EditAssignmentBadRequest with default headers values
func NewEditAssignmentBadRequest() *EditAssignmentBadRequest {

	return &EditAssignmentBadRequest{}
}

// WithPayload adds the payload to the edit assignment bad request response
func (o *EditAssignmentBadRequest) WithPayload(payload models.ErrorResponse) *EditAssignmentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment bad request response
func (o *EditAssignmentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditAssignmentForbiddenCode is the HTTP code returned for type EditAssignmentForbidden
const EditAssignmentForbiddenCode int = 403

/*
EditAssignmentForbidden Forbidden

swagger:response editAssignmentForbidden
*/
type EditAssignmentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentForbidden creates EditAssignmentForbidden with default headers values
func NewEditAssignmentForbidden() *EditAssignmentForbidden {

	return &EditAssignmentForbidden{}
}

// WithPayload adds the payload to the edit assignment forbidden response
func (o *EditAssignmentForbidden) WithPayload(payload models.ErrorResponse) *EditAssignmentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment forbidden response
func (o *EditAssignmentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditAssignmentNotFoundCode is the HTTP code returned for type EditAssignmentNotFound
const EditAssignmentNotFoundCode int = 404

/*
EditAssignmentNotFound Not Found

swagger:response editAssignmentNotFound
*/
type EditAssignmentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentNotFound creates EditAssignmentNotFound with default headers values
func NewEditAssignmentNotFound() *EditAssignmentNotFound {

	return &EditAssignmentNotFound{}
}

// WithPayload adds the payload to the edit assignment not found response
func (o *EditAssignmentNotFound) WithPayload(payload models.ErrorResponse) *EditAssignmentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment not found response
func (o *EditAssignmentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditAssignmentTooManyRequestsCode is the HTTP code returned for type EditAssignmentTooManyRequests
const EditAssignmentTooManyRequestsCode int = 429

/*
EditAssignmentTooManyRequests Too Many Requests

swagger:response editAssignmentTooManyRequests
*/
type EditAssignmentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentTooManyRequests creates EditAssignmentTooManyRequests with default headers values
func NewEditAssignmentTooManyRequests() *EditAssignmentTooManyRequests {

	return &EditAssignmentTooManyRequests{}
}

// WithPayload adds the payload to the edit assignment too many requests response
func (o *EditAssignmentTooManyRequests) WithPayload(payload models.ErrorResponse) *EditAssignmentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment too many requests response
func (o *EditAssignmentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditAssignmentInternalServerErrorCode is the HTTP code returned for type EditAssignmentInternalServerError
const EditAssignmentInternalServerErrorCode int = 500

/*
EditAssignmentInternalServerError Internal Server Error

swagger:response editAssignmentInternalServerError
*/
type EditAssignmentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentInternalServerError creates EditAssignmentInternalServerError with default headers values
func NewEditAssignmentInternalServerError() *EditAssignmentInternalServerError {

	return &EditAssignmentInternalServerError{}
}

// WithPayload adds the payload to the edit assignment internal server error response
func (o *EditAssignmentInternalServerError) WithPayload(payload models.ErrorResponse) *EditAssignmentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment internal server error response
func (o *EditAssignmentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// EditAssignmentServiceUnavailableCode is the HTTP code returned for type EditAssignmentServiceUnavailable
const EditAssignmentServiceUnavailableCode int = 503

/*
EditAssignmentServiceUnavailable Service Unvailable

swagger:response editAssignmentServiceUnavailable
*/
type EditAssignmentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewEditAssignmentServiceUnavailable creates EditAssignmentServiceUnavailable with default headers values
func NewEditAssignmentServiceUnavailable() *EditAssignmentServiceUnavailable {

	return &EditAssignmentServiceUnavailable{}
}

// WithPayload adds the payload to the edit assignment service unavailable response
func (o *EditAssignmentServiceUnavailable) WithPayload(payload models.ErrorResponse) *EditAssignmentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the edit assignment service unavailable response
func (o *EditAssignmentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *EditAssignmentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
