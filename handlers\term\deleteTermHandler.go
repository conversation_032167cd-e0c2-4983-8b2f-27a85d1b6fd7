package handlers

import (
	"eddyowl-backend/constants"
	"eddyowl-backend/data_providers"
	"eddyowl-backend/models"
	"eddyowl-backend/restapi/operations/term"
	"eddyowl-backend/utils"

	"github.com/go-openapi/runtime/middleware"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

// deleteTermImpl implements the DeleteTermByIDHandler interface
type deleteTermImpl struct {
	provider          data_providers.TermProvider
	userRolesProvider data_providers.UserRolesProvider
	tracer            trace.Tracer
}

// NewDeleteTermByIDHandler constructs a new handler for deleting a term
func NewDeleteTermByIDHandler(
	provider data_providers.TermProvider,
	userRolesProvider data_providers.UserRolesProvider,
	tracer trace.Tracer,
) term.DeleteTermByIDHandler {
	return &deleteTermImpl{
		provider:          provider,
		userRolesProvider: userRolesProvider,
		tracer:            tracer,
	}
}

// <PERSON><PERSON> processes the "delete term" request
func (impl *deleteTermImpl) Handle(params term.DeleteTermByIDParams, principal interface{}) middleware.Responder {
	ctx, span := impl.tracer.Start(params.HTTPRequest.Context(), "HTTP : DeleteTermByIDHandler")
	defer span.End()

	principal = principal.(string)
	err := utils.CheckUserRoleAndInstitute(ctx, impl.userRolesProvider, principal.(string), params.InstituteID, []int{constants.AdminRole})

	if err != nil {
		log.Error().Err(err).Msg("Failed to check user roles")
		return term.NewDeleteTermByIDForbidden().WithPayload("Unauthorized")
	}

	termID := params.TermID
	instituteID := params.InstituteID

	// Basic validation checks
	if termID == constants.EmptyString || instituteID == constants.EmptyString {
		return term.NewDeleteTermByIDBadRequest().WithPayload("Invalid termID or instituteID")
	}

	// Attempt to delete the term
	err = impl.provider.Delete(ctx, termID, instituteID)
	if err != nil {
		log.Error().Msg(err.Error())
		return term.NewDeleteTermByIDInternalServerError().WithPayload("Unable to delete Term")
	}

	// Successfully deleted
	return term.NewDeleteTermByIDOK().WithPayload(
		&models.SuccessResponse{
			ID:      termID,
			Message: "Term deleted successfully",
		},
	)
}
