// Code generated by go-swagger; DO NOT EDIT.

package folder

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// CreateFolderOKCode is the HTTP code returned for type CreateFolderOK
const CreateFolderOKCode int = 200

/*
CreateFolderOK Folder created

swagger:response createFolderOK
*/
type CreateFolderOK struct {

	/*
	  In: Body
	*/
	Payload *models.Folder `json:"body,omitempty"`
}

// NewCreateFolderOK creates CreateFolderOK with default headers values
func NewCreateFolderOK() *CreateFolderOK {

	return &CreateFolderOK{}
}

// WithPayload adds the payload to the create folder o k response
func (o *CreateFolderOK) WithPayload(payload *models.Folder) *CreateFolderOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create folder o k response
func (o *CreateFolderOK) SetPayload(payload *models.Folder) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateFolderOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// CreateFolderBadRequestCode is the HTTP code returned for type CreateFolderBadRequest
const CreateFolderBadRequestCode int = 400

/*
CreateFolderBadRequest Bad Request

swagger:response createFolderBadRequest
*/
type CreateFolderBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewCreateFolderBadRequest creates CreateFolderBadRequest with default headers values
func NewCreateFolderBadRequest() *CreateFolderBadRequest {

	return &CreateFolderBadRequest{}
}

// WithPayload adds the payload to the create folder bad request response
func (o *CreateFolderBadRequest) WithPayload(payload models.ErrorResponse) *CreateFolderBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the create folder bad request response
func (o *CreateFolderBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *CreateFolderBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
