// Code generated by go-swagger; DO NOT EDIT.

package submission

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// PublishAllSubmissionsOKCode is the HTTP code returned for type PublishAllSubmissionsOK
const PublishAllSubmissionsOKCode int = 200

/*
PublishAllSubmissionsOK Successful operation

swagger:response publishAllSubmissionsOK
*/
type PublishAllSubmissionsOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsOK creates PublishAllSubmissionsOK with default headers values
func NewPublishAllSubmissionsOK() *PublishAllSubmissionsOK {

	return &PublishAllSubmissionsOK{}
}

// WithPayload adds the payload to the publish all submissions o k response
func (o *PublishAllSubmissionsOK) WithPayload(payload *models.SuccessResponse) *PublishAllSubmissionsOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions o k response
func (o *PublishAllSubmissionsOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// PublishAllSubmissionsBadRequestCode is the HTTP code returned for type PublishAllSubmissionsBadRequest
const PublishAllSubmissionsBadRequestCode int = 400

/*
PublishAllSubmissionsBadRequest Bad Request

swagger:response publishAllSubmissionsBadRequest
*/
type PublishAllSubmissionsBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsBadRequest creates PublishAllSubmissionsBadRequest with default headers values
func NewPublishAllSubmissionsBadRequest() *PublishAllSubmissionsBadRequest {

	return &PublishAllSubmissionsBadRequest{}
}

// WithPayload adds the payload to the publish all submissions bad request response
func (o *PublishAllSubmissionsBadRequest) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions bad request response
func (o *PublishAllSubmissionsBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishAllSubmissionsForbiddenCode is the HTTP code returned for type PublishAllSubmissionsForbidden
const PublishAllSubmissionsForbiddenCode int = 403

/*
PublishAllSubmissionsForbidden Forbidden

swagger:response publishAllSubmissionsForbidden
*/
type PublishAllSubmissionsForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsForbidden creates PublishAllSubmissionsForbidden with default headers values
func NewPublishAllSubmissionsForbidden() *PublishAllSubmissionsForbidden {

	return &PublishAllSubmissionsForbidden{}
}

// WithPayload adds the payload to the publish all submissions forbidden response
func (o *PublishAllSubmissionsForbidden) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions forbidden response
func (o *PublishAllSubmissionsForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishAllSubmissionsNotFoundCode is the HTTP code returned for type PublishAllSubmissionsNotFound
const PublishAllSubmissionsNotFoundCode int = 404

/*
PublishAllSubmissionsNotFound Not Found

swagger:response publishAllSubmissionsNotFound
*/
type PublishAllSubmissionsNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsNotFound creates PublishAllSubmissionsNotFound with default headers values
func NewPublishAllSubmissionsNotFound() *PublishAllSubmissionsNotFound {

	return &PublishAllSubmissionsNotFound{}
}

// WithPayload adds the payload to the publish all submissions not found response
func (o *PublishAllSubmissionsNotFound) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions not found response
func (o *PublishAllSubmissionsNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishAllSubmissionsTooManyRequestsCode is the HTTP code returned for type PublishAllSubmissionsTooManyRequests
const PublishAllSubmissionsTooManyRequestsCode int = 429

/*
PublishAllSubmissionsTooManyRequests Too Many Requests

swagger:response publishAllSubmissionsTooManyRequests
*/
type PublishAllSubmissionsTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsTooManyRequests creates PublishAllSubmissionsTooManyRequests with default headers values
func NewPublishAllSubmissionsTooManyRequests() *PublishAllSubmissionsTooManyRequests {

	return &PublishAllSubmissionsTooManyRequests{}
}

// WithPayload adds the payload to the publish all submissions too many requests response
func (o *PublishAllSubmissionsTooManyRequests) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions too many requests response
func (o *PublishAllSubmissionsTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishAllSubmissionsInternalServerErrorCode is the HTTP code returned for type PublishAllSubmissionsInternalServerError
const PublishAllSubmissionsInternalServerErrorCode int = 500

/*
PublishAllSubmissionsInternalServerError Internal Server Error

swagger:response publishAllSubmissionsInternalServerError
*/
type PublishAllSubmissionsInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsInternalServerError creates PublishAllSubmissionsInternalServerError with default headers values
func NewPublishAllSubmissionsInternalServerError() *PublishAllSubmissionsInternalServerError {

	return &PublishAllSubmissionsInternalServerError{}
}

// WithPayload adds the payload to the publish all submissions internal server error response
func (o *PublishAllSubmissionsInternalServerError) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions internal server error response
func (o *PublishAllSubmissionsInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// PublishAllSubmissionsServiceUnavailableCode is the HTTP code returned for type PublishAllSubmissionsServiceUnavailable
const PublishAllSubmissionsServiceUnavailableCode int = 503

/*
PublishAllSubmissionsServiceUnavailable Service Unvailable

swagger:response publishAllSubmissionsServiceUnavailable
*/
type PublishAllSubmissionsServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewPublishAllSubmissionsServiceUnavailable creates PublishAllSubmissionsServiceUnavailable with default headers values
func NewPublishAllSubmissionsServiceUnavailable() *PublishAllSubmissionsServiceUnavailable {

	return &PublishAllSubmissionsServiceUnavailable{}
}

// WithPayload adds the payload to the publish all submissions service unavailable response
func (o *PublishAllSubmissionsServiceUnavailable) WithPayload(payload models.ErrorResponse) *PublishAllSubmissionsServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the publish all submissions service unavailable response
func (o *PublishAllSubmissionsServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *PublishAllSubmissionsServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
