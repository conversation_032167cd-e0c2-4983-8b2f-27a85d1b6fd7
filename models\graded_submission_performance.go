// Code generated by go-swagger; DO NOT EDIT.

package models

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"strconv"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/strfmt"
	"github.com/go-openapi/swag"
)

// GradedSubmissionPerformance graded submission performance
//
// swagger:model GradedSubmissionPerformance
type GradedSubmissionPerformance struct {

	// stats
	Stats []*QuestionStats `json:"stats"`
}

// Validate validates this graded submission performance
func (m *GradedSubmissionPerformance) Validate(formats strfmt.Registry) error {
	var res []error

	if err := m.validateStats(formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *GradedSubmissionPerformance) validateStats(formats strfmt.Registry) error {
	if swag.IsZero(m.Stats) { // not required
		return nil
	}

	for i := 0; i < len(m.Stats); i++ {
		if swag.IsZero(m.Stats[i]) { // not required
			continue
		}

		if m.Stats[i] != nil {
			if err := m.Stats[i].Validate(formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("stats" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("stats" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// ContextValidate validate this graded submission performance based on the context it is used
func (m *GradedSubmissionPerformance) ContextValidate(ctx context.Context, formats strfmt.Registry) error {
	var res []error

	if err := m.contextValidateStats(ctx, formats); err != nil {
		res = append(res, err)
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}

func (m *GradedSubmissionPerformance) contextValidateStats(ctx context.Context, formats strfmt.Registry) error {

	for i := 0; i < len(m.Stats); i++ {

		if m.Stats[i] != nil {

			if swag.IsZero(m.Stats[i]) { // not required
				return nil
			}

			if err := m.Stats[i].ContextValidate(ctx, formats); err != nil {
				if ve, ok := err.(*errors.Validation); ok {
					return ve.ValidateName("stats" + "." + strconv.Itoa(i))
				} else if ce, ok := err.(*errors.CompositeError); ok {
					return ce.ValidateName("stats" + "." + strconv.Itoa(i))
				}
				return err
			}
		}

	}

	return nil
}

// MarshalBinary interface implementation
func (m *GradedSubmissionPerformance) MarshalBinary() ([]byte, error) {
	if m == nil {
		return nil, nil
	}
	return swag.WriteJSON(m)
}

// UnmarshalBinary interface implementation
func (m *GradedSubmissionPerformance) UnmarshalBinary(b []byte) error {
	var res GradedSubmissionPerformance
	if err := swag.ReadJSON(b, &res); err != nil {
		return err
	}
	*m = res
	return nil
}
