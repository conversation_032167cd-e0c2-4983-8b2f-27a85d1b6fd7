// Code generated by go-swagger; DO NOT EDIT.

package assignment

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// AddTopicsToAssignmentOKCode is the HTTP code returned for type AddTopicsToAssignmentOK
const AddTopicsToAssignmentOKCode int = 200

/*
AddTopicsToAssignmentOK Successful operation

swagger:response addTopicsToAssignmentOK
*/
type AddTopicsToAssignmentOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentOK creates AddTopicsToAssignmentOK with default headers values
func NewAddTopicsToAssignmentOK() *AddTopicsToAssignmentOK {

	return &AddTopicsToAssignmentOK{}
}

// WithPayload adds the payload to the add topics to assignment o k response
func (o *AddTopicsToAssignmentOK) WithPayload(payload *models.SuccessResponse) *AddTopicsToAssignmentOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment o k response
func (o *AddTopicsToAssignmentOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// AddTopicsToAssignmentBadRequestCode is the HTTP code returned for type AddTopicsToAssignmentBadRequest
const AddTopicsToAssignmentBadRequestCode int = 400

/*
AddTopicsToAssignmentBadRequest Bad Request

swagger:response addTopicsToAssignmentBadRequest
*/
type AddTopicsToAssignmentBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentBadRequest creates AddTopicsToAssignmentBadRequest with default headers values
func NewAddTopicsToAssignmentBadRequest() *AddTopicsToAssignmentBadRequest {

	return &AddTopicsToAssignmentBadRequest{}
}

// WithPayload adds the payload to the add topics to assignment bad request response
func (o *AddTopicsToAssignmentBadRequest) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment bad request response
func (o *AddTopicsToAssignmentBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddTopicsToAssignmentForbiddenCode is the HTTP code returned for type AddTopicsToAssignmentForbidden
const AddTopicsToAssignmentForbiddenCode int = 403

/*
AddTopicsToAssignmentForbidden Forbidden

swagger:response addTopicsToAssignmentForbidden
*/
type AddTopicsToAssignmentForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentForbidden creates AddTopicsToAssignmentForbidden with default headers values
func NewAddTopicsToAssignmentForbidden() *AddTopicsToAssignmentForbidden {

	return &AddTopicsToAssignmentForbidden{}
}

// WithPayload adds the payload to the add topics to assignment forbidden response
func (o *AddTopicsToAssignmentForbidden) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment forbidden response
func (o *AddTopicsToAssignmentForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddTopicsToAssignmentNotFoundCode is the HTTP code returned for type AddTopicsToAssignmentNotFound
const AddTopicsToAssignmentNotFoundCode int = 404

/*
AddTopicsToAssignmentNotFound Not Found

swagger:response addTopicsToAssignmentNotFound
*/
type AddTopicsToAssignmentNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentNotFound creates AddTopicsToAssignmentNotFound with default headers values
func NewAddTopicsToAssignmentNotFound() *AddTopicsToAssignmentNotFound {

	return &AddTopicsToAssignmentNotFound{}
}

// WithPayload adds the payload to the add topics to assignment not found response
func (o *AddTopicsToAssignmentNotFound) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment not found response
func (o *AddTopicsToAssignmentNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddTopicsToAssignmentTooManyRequestsCode is the HTTP code returned for type AddTopicsToAssignmentTooManyRequests
const AddTopicsToAssignmentTooManyRequestsCode int = 429

/*
AddTopicsToAssignmentTooManyRequests Too Many Requests

swagger:response addTopicsToAssignmentTooManyRequests
*/
type AddTopicsToAssignmentTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentTooManyRequests creates AddTopicsToAssignmentTooManyRequests with default headers values
func NewAddTopicsToAssignmentTooManyRequests() *AddTopicsToAssignmentTooManyRequests {

	return &AddTopicsToAssignmentTooManyRequests{}
}

// WithPayload adds the payload to the add topics to assignment too many requests response
func (o *AddTopicsToAssignmentTooManyRequests) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment too many requests response
func (o *AddTopicsToAssignmentTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddTopicsToAssignmentInternalServerErrorCode is the HTTP code returned for type AddTopicsToAssignmentInternalServerError
const AddTopicsToAssignmentInternalServerErrorCode int = 500

/*
AddTopicsToAssignmentInternalServerError Internal Server Error

swagger:response addTopicsToAssignmentInternalServerError
*/
type AddTopicsToAssignmentInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentInternalServerError creates AddTopicsToAssignmentInternalServerError with default headers values
func NewAddTopicsToAssignmentInternalServerError() *AddTopicsToAssignmentInternalServerError {

	return &AddTopicsToAssignmentInternalServerError{}
}

// WithPayload adds the payload to the add topics to assignment internal server error response
func (o *AddTopicsToAssignmentInternalServerError) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment internal server error response
func (o *AddTopicsToAssignmentInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// AddTopicsToAssignmentServiceUnavailableCode is the HTTP code returned for type AddTopicsToAssignmentServiceUnavailable
const AddTopicsToAssignmentServiceUnavailableCode int = 503

/*
AddTopicsToAssignmentServiceUnavailable Service Unvailable

swagger:response addTopicsToAssignmentServiceUnavailable
*/
type AddTopicsToAssignmentServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewAddTopicsToAssignmentServiceUnavailable creates AddTopicsToAssignmentServiceUnavailable with default headers values
func NewAddTopicsToAssignmentServiceUnavailable() *AddTopicsToAssignmentServiceUnavailable {

	return &AddTopicsToAssignmentServiceUnavailable{}
}

// WithPayload adds the payload to the add topics to assignment service unavailable response
func (o *AddTopicsToAssignmentServiceUnavailable) WithPayload(payload models.ErrorResponse) *AddTopicsToAssignmentServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the add topics to assignment service unavailable response
func (o *AddTopicsToAssignmentServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *AddTopicsToAssignmentServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
