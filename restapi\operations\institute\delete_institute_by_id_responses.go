// Code generated by go-swagger; DO NOT EDIT.

package institute

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"net/http"

	"github.com/go-openapi/runtime"

	"eddyowl-backend/models"
)

// DeleteInstituteByIDOKCode is the HTTP code returned for type DeleteInstituteByIDOK
const DeleteInstituteByIDOKCode int = 200

/*
DeleteInstituteByIDOK Successful operation

swagger:response deleteInstituteByIdOK
*/
type DeleteInstituteByIDOK struct {

	/*
	  In: Body
	*/
	Payload *models.SuccessResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDOK creates DeleteInstituteByIDOK with default headers values
func NewDeleteInstituteByIDOK() *DeleteInstituteByIDOK {

	return &DeleteInstituteByIDOK{}
}

// WithPayload adds the payload to the delete institute by Id o k response
func (o *DeleteInstituteByIDOK) WithPayload(payload *models.SuccessResponse) *DeleteInstituteByIDOK {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id o k response
func (o *DeleteInstituteByIDOK) SetPayload(payload *models.SuccessResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDOK) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(200)
	if o.Payload != nil {
		payload := o.Payload
		if err := producer.Produce(rw, payload); err != nil {
			panic(err) // let the recovery middleware deal with this
		}
	}
}

// DeleteInstituteByIDBadRequestCode is the HTTP code returned for type DeleteInstituteByIDBadRequest
const DeleteInstituteByIDBadRequestCode int = 400

/*
DeleteInstituteByIDBadRequest Bad Request

swagger:response deleteInstituteByIdBadRequest
*/
type DeleteInstituteByIDBadRequest struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDBadRequest creates DeleteInstituteByIDBadRequest with default headers values
func NewDeleteInstituteByIDBadRequest() *DeleteInstituteByIDBadRequest {

	return &DeleteInstituteByIDBadRequest{}
}

// WithPayload adds the payload to the delete institute by Id bad request response
func (o *DeleteInstituteByIDBadRequest) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDBadRequest {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id bad request response
func (o *DeleteInstituteByIDBadRequest) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDBadRequest) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(400)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstituteByIDForbiddenCode is the HTTP code returned for type DeleteInstituteByIDForbidden
const DeleteInstituteByIDForbiddenCode int = 403

/*
DeleteInstituteByIDForbidden Forbidden

swagger:response deleteInstituteByIdForbidden
*/
type DeleteInstituteByIDForbidden struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDForbidden creates DeleteInstituteByIDForbidden with default headers values
func NewDeleteInstituteByIDForbidden() *DeleteInstituteByIDForbidden {

	return &DeleteInstituteByIDForbidden{}
}

// WithPayload adds the payload to the delete institute by Id forbidden response
func (o *DeleteInstituteByIDForbidden) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDForbidden {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id forbidden response
func (o *DeleteInstituteByIDForbidden) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDForbidden) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(403)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstituteByIDNotFoundCode is the HTTP code returned for type DeleteInstituteByIDNotFound
const DeleteInstituteByIDNotFoundCode int = 404

/*
DeleteInstituteByIDNotFound Not Found

swagger:response deleteInstituteByIdNotFound
*/
type DeleteInstituteByIDNotFound struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDNotFound creates DeleteInstituteByIDNotFound with default headers values
func NewDeleteInstituteByIDNotFound() *DeleteInstituteByIDNotFound {

	return &DeleteInstituteByIDNotFound{}
}

// WithPayload adds the payload to the delete institute by Id not found response
func (o *DeleteInstituteByIDNotFound) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDNotFound {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id not found response
func (o *DeleteInstituteByIDNotFound) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDNotFound) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(404)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstituteByIDTooManyRequestsCode is the HTTP code returned for type DeleteInstituteByIDTooManyRequests
const DeleteInstituteByIDTooManyRequestsCode int = 429

/*
DeleteInstituteByIDTooManyRequests Too Many Requests

swagger:response deleteInstituteByIdTooManyRequests
*/
type DeleteInstituteByIDTooManyRequests struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDTooManyRequests creates DeleteInstituteByIDTooManyRequests with default headers values
func NewDeleteInstituteByIDTooManyRequests() *DeleteInstituteByIDTooManyRequests {

	return &DeleteInstituteByIDTooManyRequests{}
}

// WithPayload adds the payload to the delete institute by Id too many requests response
func (o *DeleteInstituteByIDTooManyRequests) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDTooManyRequests {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id too many requests response
func (o *DeleteInstituteByIDTooManyRequests) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDTooManyRequests) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(429)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstituteByIDInternalServerErrorCode is the HTTP code returned for type DeleteInstituteByIDInternalServerError
const DeleteInstituteByIDInternalServerErrorCode int = 500

/*
DeleteInstituteByIDInternalServerError Internal Server Error

swagger:response deleteInstituteByIdInternalServerError
*/
type DeleteInstituteByIDInternalServerError struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDInternalServerError creates DeleteInstituteByIDInternalServerError with default headers values
func NewDeleteInstituteByIDInternalServerError() *DeleteInstituteByIDInternalServerError {

	return &DeleteInstituteByIDInternalServerError{}
}

// WithPayload adds the payload to the delete institute by Id internal server error response
func (o *DeleteInstituteByIDInternalServerError) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDInternalServerError {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id internal server error response
func (o *DeleteInstituteByIDInternalServerError) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDInternalServerError) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(500)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}

// DeleteInstituteByIDServiceUnavailableCode is the HTTP code returned for type DeleteInstituteByIDServiceUnavailable
const DeleteInstituteByIDServiceUnavailableCode int = 503

/*
DeleteInstituteByIDServiceUnavailable Service Unvailable

swagger:response deleteInstituteByIdServiceUnavailable
*/
type DeleteInstituteByIDServiceUnavailable struct {

	/*
	  In: Body
	*/
	Payload models.ErrorResponse `json:"body,omitempty"`
}

// NewDeleteInstituteByIDServiceUnavailable creates DeleteInstituteByIDServiceUnavailable with default headers values
func NewDeleteInstituteByIDServiceUnavailable() *DeleteInstituteByIDServiceUnavailable {

	return &DeleteInstituteByIDServiceUnavailable{}
}

// WithPayload adds the payload to the delete institute by Id service unavailable response
func (o *DeleteInstituteByIDServiceUnavailable) WithPayload(payload models.ErrorResponse) *DeleteInstituteByIDServiceUnavailable {
	o.Payload = payload
	return o
}

// SetPayload sets the payload to the delete institute by Id service unavailable response
func (o *DeleteInstituteByIDServiceUnavailable) SetPayload(payload models.ErrorResponse) {
	o.Payload = payload
}

// WriteResponse to the client
func (o *DeleteInstituteByIDServiceUnavailable) WriteResponse(rw http.ResponseWriter, producer runtime.Producer) {

	rw.WriteHeader(503)
	payload := o.Payload
	if err := producer.Produce(rw, payload); err != nil {
		panic(err) // let the recovery middleware deal with this
	}
}
