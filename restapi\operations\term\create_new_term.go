// Code generated by go-swagger; DO NOT EDIT.

package term

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the generate command

import (
	"net/http"

	"github.com/go-openapi/runtime/middleware"
)

// CreateNewTermHandlerFunc turns a function with the right signature into a create new term handler
type CreateNewTermHandlerFunc func(CreateNewTermParams, interface{}) middleware.Responder

// <PERSON>le executing the request and returning a response
func (fn CreateNewTermHandlerFunc) Handle(params CreateNewTermParams, principal interface{}) middleware.Responder {
	return fn(params, principal)
}

// CreateNewTermHandler interface for that can handle valid create new term params
type CreateNewTermHandler interface {
	Handle(CreateNewTermParams, interface{}) middleware.Responder
}

// NewCreateNewTerm creates a new http.Handler for the create new term operation
func NewCreateNewTerm(ctx *middleware.Context, handler <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) *CreateNewTerm {
	return &CreateNewTerm{Context: ctx, Handler: handler}
}

/*
	CreateNewTerm swagger:route POST /institute/{instituteId}/terms term createNewTerm

# Create new term

Create new term
*/
type CreateNewTerm struct {
	Context *middleware.Context
	Handler CreateNewTermHandler
}

func (o *CreateNewTerm) ServeHTTP(rw http.ResponseWriter, r *http.Request) {
	route, rCtx, _ := o.Context.RouteInfo(r)
	if rCtx != nil {
		*r = *rCtx
	}
	var Params = NewCreateNewTermParams()
	uprinc, aCtx, err := o.Context.Authorize(r, route)
	if err != nil {
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}
	if aCtx != nil {
		*r = *aCtx
	}
	var principal interface{}
	if uprinc != nil {
		principal = uprinc.(interface{}) // this is really a interface{}, I promise
	}

	if err := o.Context.BindValidRequest(r, route, &Params); err != nil { // bind params
		o.Context.Respond(rw, r, route.Produces, route, err)
		return
	}

	res := o.Handler.Handle(Params, principal) // actually handle the request
	o.Context.Respond(rw, r, route.Produces, route, res)

}
